#pragma once

#include <QObject>
#include <QMap>
#include <QVector>
#include "QCustomPlot.h"
#include "OpenProjectWidget.h"

// 前向声明
class SharedDataManager;

// Enum to identify different tabs
enum class TabType {
    BaseTab,
    AcquireTab,
    ProcessTab,
    AnalysisTab
};

// Enum to identify different plot types
enum class PlotType {
    FluorescenceMap,
    DecayCurve,
    SpectralCurve,
    TotalCounts
};

class TabSynchronizer : public QObject {
    Q_OBJECT

public:
    static TabSynchronizer* getInstance();

    // Plot registration methods
    void registerPlot(TabType tab, PlotType plotType, QCustomPlot* plot);
    void unregisterPlot(QCustomPlot* plot);

    // OpenProjectWidget registration methods
    void registerOpenProjectWidget(OpenProjectWidget* widget, TabType tab);
    void unregisterOpenProjectWidget(OpenProjectWidget* widget);

public slots:
    // Tab visibility change handler
    void onTabChanged(int index);


    // Synchronize toolbar states between tabs
    void syncToolbarStates(TabType sourceTab, TabType targetTab);

    // Synchronize file selection between tabs
    void syncFileSelection(TabType sourceTab, TabType targetTab);
private:
    TabSynchronizer(QObject* parent = nullptr);
    ~TabSynchronizer();

    static TabSynchronizer* instance;

    // Maps to store registered plots
    QMap<TabType, QMap<PlotType, QCustomPlot*>> m_plots;

    // Map to store registered OpenProjectWidgets
    QMap<TabType, OpenProjectWidget*> m_openProjectWidgets;
};
