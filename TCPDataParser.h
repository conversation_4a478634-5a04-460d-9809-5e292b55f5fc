#pragma once

#include <QTimer>
#include <QDateTime>
#include <QThread>
#include <QQueue>
#include <QMutex>
#include <QWaitCondition>
#include <QObject>
#include <QByteArray>
#include <QSemaphore>
#include <vector>

class TCPDataParser : public QThread
{
    Q_OBJECT
public:
    explicit TCPDataParser(QObject *parent = nullptr);
    ~TCPDataParser();

    void run() override;

    //QString ByteToHexString(QByteArray arr);
    //QByteArray HexStringToByte(QString str);

    void resetData();

    std::vector<std::vector<int>> getAccumulatedData(){return m_accumulatedData;}
    quint64 getTotalDataLength() const { return m_uiTotalDataLength; }
    void pause();
    void resume();
    void clearBuffers();
    void setStopPhotonCondition(int maxPhotonCnt); // 新增：设置最大光子计数
    void setStopTimeCondition(int stopTimeCondition); // 新增：设置停止时间条件
    quint16 getCurrentFrameCount() const;
    void setSpectralChannelNumber(int iSpectralChnNum);
    void setTimeChannelNumber(int iTimeChlNum);

    bool m_paused;      // 暂停标志


private:
    enum class ParserState {
        SearchingHeader,
        ReceivingPayload,
        VerifyingFooter
    };

    int stopcount;
    QMutex m_stopCountMutex;

    //QByteArray m_recvArr;
    QByteArray m_fullData;

    QQueue<QByteArray> m_TCPQueue;
    quint64 m_uiTotalDataLength;

    bool m_running;
    QDateTime m_startTime;
    std::vector<std::vector<int>> m_accumulatedData;

    QMutex m_mutex;
    QWaitCondition m_waitCondition;
    QWaitCondition m_pauseCondition;

    int m_maxAccumulatedValue; // 新增：存储最大累加值

    // 新增：最大光子计数和停止时间条件
    int m_stopPhotonCondition;
    int m_stopTimeCondition;
    int m_iTotalFrameCnt;
    bool m_bNeedStop;

    int m_iTimeChannelNumber; // 时间通道数（常量）
    int m_iSpectralChannelNumber;   // 光谱通道数
    const int m_iFrameHeadLen = 16;
    const int m_iFrameRearLen = 8;

    ParserState m_mCurrentState; // 当前解析状态
    static constexpr int MAX_BUFFER_SIZE = 1024 * 1024; // 最大缓冲区大小(1MB)

    quint32 m_uiPulseCount;

    //qint64 m_lastReceivedBytes;
    //qint64 m_lastTime;
    //qint64 m_iReceivedSize;



    //QByteArray mCachaBuff;

    //qint64 m_iTotalCount;

    QByteArray m_middleData;






    void parseData(QByteArray);
    void processData(const QByteArray& frameData);
    int  findValidFrameHeaderPosition(const QByteArray& data) const;
    bool validateFrameHeader(const QByteArray& data) const;
    bool validateFrameFooter(const QByteArray& data, quint16 frameNumber) const;
    void resetAccumulatedData(int spectralChannels, int timeChannels);
    int getMiddleDataSize() const;
    int getFrameSize() const;

    quint32 getPulseCountFromHeader(const QByteArray& data) const;

signals:
    void dataReady();

    void ShowDataReady(const std::vector<std::vector<int>> &Accumulatedata, const std::vector<std::vector<int>>&data ,quint32 pulseCount);
    void updateTotalDataCount(qint64 totalDataCount);
    void receiveRateChanged(double rate);
    void stopcondition();
    void updateFrameSeq();
    void maxAccumulatedValueChanged(int maxValue); // 新增：最大累加值变化信号
    void showDataIntegrityError();

public slots:
    void Stop_condition(int &data);
    void TCPDataReady(QByteArray);
    std::vector<std::vector<int>> dataTo2D(QByteArray arr, quint32 pulseCount);


};
