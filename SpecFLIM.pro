QT       += core gui serialport gui-private concurrent axcontainer

greaterThan(QT_MAJOR_VERSION, 4): QT +=core gui widgets network printsupport charts

CONFIG += c++17

DEFINES += NOMINMAX
# 不再使用编译时定义HAVE_HARDWARE，改为运行时检测

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0
LIBS+=$$PWD/spectrometer_x64.lib

INCLUDEPATH += .\quaZlibManager\include  \
             .\thirdPart\zlib-1.3.1\include


INCLUDEPATH +=$$PWD/fit
INCLUDEPATH +=$$PWD/boost
INCLUDEPATH +=$$PWD
LIBS += $$PWD/quaZlibManager/lib/quazip1-qt6.lib \
      $$PWD/quaZlibManager/lib/nlopt.lib \
      -L$$PWD/quaZlibManager/dll\
      -luser32

SOURCES += \
    AcquireTab.cpp \
    Analysis.cpp \
    AppConfig.cpp \
    BaseTab.cpp \
    CUDPParserThread.cpp \
    ConfigurationTab.cpp \
    CustomizePlot.cpp \
    DummyTcpServer.cpp \
    FileWriterThread.cpp \
    HardwareManager.cpp \
    IRFImporter.cpp \
    analysis/FitCurveDisplay.cpp \
    analysis/FitManager.cpp \
    analysis/FitModels.cpp \
    analysis/FitParameterModel.cpp \
    analysis/FitUIBinder.cpp \
    LocalSerialStatus.cpp \
    MonochromatorConfiguration.cpp \
    OpenProjectWidget.cpp \
    PlotToolbar.cpp \
    ProcessTab.cpp \
    QProcessIndicator.cpp \
    QTCPMgr.cpp \
    MeasureDataHandler.cpp\
    ProjectFileManager.cpp \
    QuantifyTab.cpp \
    Settings.cpp \
    SfdViewerMainWindow.cpp \
    SharedDataManager.cpp \
    TabSynchronizer.cpp \
    TCPDataParser.cpp \
    TDCConfiguration.cpp \
    ThemeManager.cpp \
    UdpReceiverThread.cpp \
    UserConfiguration.cpp \
    Utility.cpp \
    main.cpp \
    monochromatoracquire.cpp \
    qcustomplot.cpp \
    specflim.cpp\
    CompressionThread.cpp

HEADERS += \
    AcquireTab.h \
    Analysis.h \
    AppConfig.h \
    BaseTab.h \
    CUDPParserThread.h \
    ConfigurationTab.h \
    CustomizePlot.h \
    DummyTcpServer.h \
    FileWriterThread.h \
    HardwareManager.h \
    IRFImporter.h \
    analysis/FitCurveDisplay.h \
    analysis/FitManager.h \
    analysis/FitModelInterface.h \
    analysis/FitModels.h \
    analysis/FitParameterModel.h \
    analysis/FitUIBinder.h \
    Head.h \
    Constants.h \
    LocalSerialStatus.h \
    MonochromatorConfiguration.h \
    OpenProjectWidget.h \
    PlotToolbar.h \
    ProcessTab.h \
    QProgressIndicator.h \
    QTCPMgr.h \
    MeasureDataHandler.h \
    ProjectFileManager.h \
    QuantifyTab.h \
    Settings.h \
    SfdViewerMainWindow.h \
    SharedDataManager.h \
    TabSynchronizer.h \
    TCPDataParser.h \
    TDCConfiguration.h \
    ThemeManager.h \
    UdpReceiverThread.h \
    UserConfiguration.h \
    monochromatoracquire.h \
    Utility.h \
    qcustomplot.h \
    specflim.h\
    CompressionThread.h \
    spectrometer.h

FORMS += \
    specflim.ui

RESOURCES += SpecFLIM.qrc
RESOURCES += qdarkstyle/dark/darkstyle.qrc
RESOURCES += qdarkstyle/light/lightstyle.qrc

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

DISTFILES +=
