#include "BaseTab.h"
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>
#include <QPushButton>
#include <QStackedWidget>
#include <QGroupBox>
#include <QLabel>
#include <QListWidget>
#include <QTreeWidget>
#include <QFrame>
#include <QFileDialog>
#include <QFileSystemModel>
#include <QTreeView>
#include <QLineEdit>
#include <QRadioButton>
#include <QSlider>
#include <QMessageBox>
#include <QFileDialog>
#include <QRubberBand>
#include <QAxObject>
#include <QFileDialog>
#include <QMessageBox>
#include <QMenu>
#include <QFile>
#include "ProjectFileManager.h"
#include "CustomizePlot.h"
#include "ThemeManager.h"

// BaseTab 构造函数
BaseTab::BaseTab(QWidget *parent, TabType tabType)
    : QWidget(parent),
    hline(nullptr),
    vline(nullptr),
    //m_originalInteractions(QCP::iRangeDrag | QCP::iRangeZoom), // 默认交互状态
    m_tabType(tabType)
{
    // 连接主题变化信号
    connect(ThemeManager::instance(), &ThemeManager::themeChanged, this, &BaseTab::onThemeChanged);
}

BaseTab::~BaseTab()
{
    // 清理资源
    removeCrosshairs(); // 清理十字线项

    // 注意：具有父对象的QObjects（如plots、buttons等）将在父对象（此widget）被删除时自动删除
    // 所以我们不需要手动删除它们
}

// 数据处理函数
void BaseTab::fillQCustomPlot(const std::vector<std::vector<int>> &Data, bool suppressReplot)
{
    try {
        // 检查数据有效性
        if (Data.empty()) {
            qWarning() << "Cannot fill QCustomPlot: Data is empty";
            return;
        }

        if (Data[0].empty()) {
            qWarning() << "Cannot fill QCustomPlot: Data[0] is empty";
            return;
        }

        // 检查plot1是否存在
        if (!m_pPlot1) {
            qWarning() << "Cannot fill QCustomPlot: m_pPlot1 is null";
            return;
        }
        // 在开始大量操作前禁用所有图表的自动重绘
        if (m_pPlot1) m_pPlot1->setUpdatesEnabled(false);
        if (m_pPlot2) m_pPlot2->setUpdatesEnabled(false);
        if (m_pPlot3) m_pPlot3->setUpdatesEnabled(false);

        // 获取数据维度
        int xA = Data.size();
        int yA = Data[0].size();

        // 创建可变数据副本
        mutableData.clear();
        mutableData.resize(xA);

        // 将整数数据转换为浮点数，并过滤小值
        for (int row = 0; row < xA; ++row) {
            mutableData[row].resize(yA);
            for (int col = 0; col < yA; ++col) {
                double value = static_cast<double>(Data[row][col]);
                // 将小于或等于5的值设置为0
                mutableData[row][col] = (value <= 5) ? 0.0 : value;
            }
        }

        calculateWavelengths(600);
        calculateTime(50);
        // 创建和配置颜色图，传递suppressReplot参数以控制是否立即重绘
        createColorMap(xA, yA, suppressReplot);


        // 保存数据以供后续使用
        numRows = xA;
        numCols = yA; // 固定值为时间通道数
        m_fillQCustomPlotData = Data;
        // 在所有操作完成后，重新启用更新并执行一次性重绘
        if (m_pPlot1) {
            m_pPlot1->setUpdatesEnabled(true);
            if (!suppressReplot) m_pPlot1->replot(QCustomPlot::rpQueuedReplot);
        }
        if (m_pPlot2) {
            m_pPlot2->setUpdatesEnabled(true);
            if (!suppressReplot) m_pPlot2->replot(QCustomPlot::rpQueuedReplot);
        }
        if (m_pPlot3) {
            m_pPlot3->setUpdatesEnabled(true);
            if (!suppressReplot) m_pPlot3->replot(QCustomPlot::rpQueuedReplot);
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in fillQCustomPlot: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in fillQCustomPlot()";
    }
}

// 创建颜色图
void BaseTab::createColorMap(int xA, int yA, bool suppressReplot)
{


    // 创建 QCPColorMap 对象
    QCPColorMap *colorMap = new QCPColorMap(m_pPlot1->xAxis, m_pPlot1->yAxis);
    colorMap->data()->setSize(xA, yA);
    colorMap->data()->setRange(QCPRange(wavelengthValues.front(), wavelengthValues.back()), QCPRange(timeValues.front(), timeValues.back()));

    // 填充数据
    for (int row = 0; row < xA; ++row) {
        for (int col = 0; col < yA; ++col) {
            colorMap->data()->setCell(row, col, mutableData[row][col]);
        }
    }

    // 创建或获取颜色标度
    QCPColorScale *colorScale = nullptr;

    // 先尝试查找现有的 colorScale
    for (int i = 0; i < m_pPlot1->plotLayout()->elementCount(); ++i) {
        for (int j = 0; j < m_pPlot1->plotLayout()->elementCount(); ++j) {
            if (m_pPlot1->plotLayout()->hasElement(i, j)) {
                QCPLayoutElement* element = m_pPlot1->plotLayout()->element(i, j);
                QCPColorScale* scale = qobject_cast<QCPColorScale*>(element);
                if (scale) {
                    colorScale = scale;
                    break;
                }
            }
        }
        if (colorScale) break;
    }

    // 如果没有找到，创建新的 colorScale
    if (!colorScale) {
        colorScale = new QCPColorScale(m_pPlot1);
        m_pPlot1->plotLayout()->addElement(1, 1, colorScale);
    }

    // 设置 colorScale 的属性
    colorScale->setType(QCPAxis::atRight); // 将颜色标度放在右侧
    colorMap->setColorScale(colorScale);

    // 创建颜色渐变
    QCPColorGradient gradient;
    gradient.setColorStopAt(0, QColor(0, 0, 100));
    gradient.setColorStopAt(0.25, QColor(0, 100, 255));
    gradient.setColorStopAt(0.5, QColor(0, 255, 255));
    gradient.setColorStopAt(0.75, QColor(255, 255, 0));
    gradient.setColorStopAt(1, QColor(255, 0, 0));
    colorMap->setGradient(gradient);

    // 设置数据范围
    //colorMap->setDataRange(QCPRange(0, 100));

    // 只应用主题样式，不修改图形颜色
    m_pPlot1->applyThemeOnly();

    // 更新颜色范围
    colorScale->rescaleDataRange(true);


    // // 只有在不抑制重绘的情况下才重绘
    // if (!suppressReplot) {
    //     m_pPlot1->replot();
    // }
}

// onResetBtnClicked 是 AcquireTab 特有的功能

void BaseTab::onFileSelected(int row, const QString &filePath)
{
    // 添加调试信息
    qDebug() << "onFileSelected called with row:" << row << "filePath:" << filePath;

    // 检查文件路径是否为空
    if (filePath.isEmpty()) {
        qWarning() << "Empty file path in onFileSelected";
        return;
    }

    QFileInfo fileInfo(filePath);
    QString fileName = fileInfo.fileName();

    // 检查文件名是否为空
    if (fileName.isEmpty()) {
        qWarning() << "Empty file name in onFileSelected";
        return;
    }

    // 重置所有工具栏按钮状态
    resetToolbarButtons();

    ProjectFileManager *fileManager = ProjectFileManager::getInstance();
    if (!fileManager) {
        qCritical() << "ProjectFileManager instance is null";
        return;
    }

    // 获取处理器
    MeasureDataHandler *handler = fileManager->getHandler(fileName);
    if (!handler)
    {
        qWarning() << "No handler found for file:" << fileName;
        return;
    }

    try {
        // 先清除十字线相关对象
        if (hasHVLine) {
            // 移除十字线
            removeCrosshairs();
            // 重置标志位
            hasHVLine = false;
            // 禁用鼠标跟踪
            disableMouseTracking();
            qDebug() << "Removed crosshairs and disabled mouse tracking";
        }

        // 清除所有图表数据
        clearPlots();

        // 再次检查处理器是否有效
        if (!handler) {
            qCritical() << "Handler became invalid during processing";
            return;
        }

        // 获取数据
        std::vector<std::vector<int>> Data = handler->getDataArrayTotal();

        // 设置文件路径到 pLineSaveDataPath
        if (pLineSaveDataPath) {
            pLineSaveDataPath->setText(handler->getFilePath());
        } else {
            qWarning() << "pLineSaveDataPath is null";
        }

        // 添加边界检查
        if (!Data.empty() && !Data[0].empty())
        {
            // 在onFileSelected中调用fillQCustomPlot时不抑制重绘
            fillQCustomPlot(Data, false);
            qDebug() << "Successfully filled plot with data from file:" << fileName;

            // 如果之前有十字线，重新创建
            if (hasHVLine) {
                createCrosshairs();
                enableMouseTracking();
                qDebug() << "Recreated crosshairs and enabled mouse tracking";
            }
        }
        else
        {
            QMessageBox::information(nullptr, "错误", "读入失败");
            qCritical() << "Error: Data array is empty or invalid for file:" << fileName;
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in onFileSelected:" << e.what();
        // 确保十字线相关对象被正确重置
        removeCrosshairs();
        hasHVLine = false;
    } catch (...) {
        qCritical() << "Unknown exception in onFileSelected";
        // 确保十字线相关对象被正确重置
        removeCrosshairs();
        hasHVLine = false;
    }
}

// 清除所有图表的内容
void BaseTab::clearPlots()
{
    try {
        // 使用数组和基于范围的循环来简化代码
        CustomizePlot* plotsArray[] = {m_pPlot1, m_pPlot2, m_pPlot3};

        // 清除图表内容，但不重绘
        for (auto& plot : plotsArray) {
            if (plot) {
                // 设置更新暂停，避免多次重绘
                plot->setUpdatesEnabled(false);
                plot->clearPlottables();
                plot->clearItems(); // 清除所有项目，包括文本项
                plot->rescaleAxes();
                // 重新启用更新
                plot->setUpdatesEnabled(true);
            }
        }

        // 清除十字线相关指针
        hline = nullptr;
        vline = nullptr;

        // 清除数据容器
        mutableData.clear();
        wavelengthValues.clear();
        timeValues.clear();
        numRows = 0;
        numCols = 0;

        // 最后只重绘一次
        for (auto& plot : plotsArray) {
            if (plot) {
                plot->replot();
            }
        }

        qDebug() << "Cleared all plots and related objects";
    } catch (const std::exception& e) {
        qCritical() << "Exception in clearPlots: " << e.what();
        // 异常已经处理，不再抛出
    } catch (...) {
        qCritical() << "Unknown exception in clearPlots()";
    }
}

// 创建十字线
void BaseTab::createCrosshairs()
{
    try
    {
        // 先清除现有的十字线
        removeCrosshairs();

        // 检查plot1是否存在
        if (!m_pPlot1) {
            qWarning() << "Cannot create crosshairs: m_pPlot1 is null";
            return;
        }

        // 创建新的十字线对象
        hline = new QCPItemStraightLine(m_pPlot1);
        if (!hline) {
            qCritical() << "Failed to create horizontal crosshair line";
            return;
        }

        vline = new QCPItemStraightLine(m_pPlot1);
        if (!vline) {
            qCritical() << "Failed to create vertical crosshair line";
            // 清理已创建的水平线
            m_pPlot1->removeItem(hline);
            hline = nullptr;
            return;
        }

        // 根据当前主题设置十字线样式
        updateCrosshairStyle();

        // 设置属性
        hline->setLayer("overlay");
        hline->setClipToAxisRect(true);
        hline->setVisible(true);
        vline->setLayer("overlay");
        vline->setClipToAxisRect(true);
        vline->setVisible(true);

        // 设置初始坐标
        if (hline->point1 && hline->point2)
        {
            hline->point1->setCoords(m_pPlot1->xAxis->range().upper, 45);
            hline->point2->setCoords(m_pPlot1->xAxis->range().lower, 45);
        }

        if (vline->point1 && vline->point2)
        {
            vline->point1->setCoords(589, m_pPlot1->yAxis->range().upper);
            vline->point2->setCoords(589, m_pPlot1->yAxis->range().lower);
        }

        // 重绘图表以显示十字线
        m_pPlot1->replot();
    }
    catch (const std::exception &e)
    {
        qCritical() << "Exception creating crosshair lines: " << e.what();
        // 如果创建失败，确保清除所有十字线
        removeCrosshairs();
        // 不再抛出异常，而是在这里处理
    }
    catch (...) {
        qCritical() << "Unknown exception in createCrosshairs()";
        removeCrosshairs();
    }
}

// 移除十字线
void BaseTab::removeCrosshairs()
{
    try {
        // 安全地清除所有十字线对象
        if (hline && m_pPlot1)
        {
            hline->setClipToAxisRect(false);
            hline->setVisible(false);
            m_pPlot1->removeItem(hline);
            hline = nullptr;
        }
        else if (hline) {
            // plot1为空，但hline不为空，这是一种异常情况
            hline = nullptr; // 直接置空指针，避免内存泄漏
            qWarning() << "Crosshair hline exists but m_pPlot1 is null";
        }

        if (vline && m_pPlot1)
        {
            vline->setClipToAxisRect(false);
            vline->setVisible(false);
            m_pPlot1->removeItem(vline);
            vline = nullptr;
        }
        else if (vline) {
            // plot1为空，但vline不为空，这是一种异常情况
            vline = nullptr; // 直接置空指针，避免内存泄漏
            qWarning() << "Crosshair vline exists but m_pPlot1 is null";
        }

        // 重绘图表（如果存在）
        if (m_pPlot1) {
            m_pPlot1->replot();
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in removeCrosshairs: " << e.what();
        // 直接置空指针，确保不会再次尝试访问
        hline = nullptr;
        vline = nullptr;
    } catch (...) {
        qCritical() << "Unknown exception in removeCrosshairs()";
        hline = nullptr;
        vline = nullptr;
    }
}

// 启用鼠标跟踪
void BaseTab::enableMouseTracking()
{
    try {
        // 只为plot1启用鼠标跟踪
        if (m_pPlot1) {
            // 启用鼠标跟踪
            m_pPlot1->setMouseTracking(true);

            // 先断开之前的连接，避免重复连接
            disconnect(m_pPlot1, SIGNAL(mouseMove(QMouseEvent *)), this, SLOT(onMouseMove(QMouseEvent *)));
            disconnect(m_pPlot1, SIGNAL(mouseRelease(QMouseEvent *)), this, SLOT(onMouseRelease(QMouseEvent *)));

            // 重新连接信号
            connect(m_pPlot1, SIGNAL(mouseMove(QMouseEvent *)), this, SLOT(onMouseMove(QMouseEvent *)));
            connect(m_pPlot1, SIGNAL(mouseRelease(QMouseEvent *)), this, SLOT(onMouseRelease(QMouseEvent *)));

            // 存储原始交互状态，以便稍后恢复
            m_originalInteractions = m_pPlot1->interactions();

            // 当十字线模式激活时，禁用图表拖动功能
            // 这样可以避免十字线移动时图表也被拖动
            m_pPlot1->setInteraction(QCP::iRangeDrag, false);
        } else {
            qWarning() << "Cannot enable mouse tracking: m_pPlot1 is null";
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in enableMouseTracking: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in enableMouseTracking()";
    }
}

// 重置所有工具栏按钮状态
void BaseTab::resetToolbarButtons()
{
    try {
        // 重置 Plot1 工具栏按钮状态
        if (m_pPlot1Toolbar) {
            // 重置十字线按钮
            m_pPlot1Toolbar->setCrosshairsState(false);
            // 重置坐标轴状态
            m_pPlot1Toolbar->setXAxisLogState(false);
            m_pPlot1Toolbar->setYAxisLogState(false);
            m_pPlot1Toolbar->setZAxisLogState(false);
            // 重置放大缩小按钮
            m_pPlot1Toolbar->setZoomInState(false);
            m_pPlot1Toolbar->setZoomOutState(false);
        }

        // 重置 Plot2 工具栏按钮状态
        if (m_pPlot2Toolbar) {
            m_pPlot2Toolbar->setXAxisLogState(false);
            m_pPlot2Toolbar->setYAxisLogState(false);
        }

        // 重置 Plot3 工具栏按钮状态
        if (m_pPlot3Toolbar) {
            m_pPlot3Toolbar->setYAxisLogState(false);
        }

        qDebug() << "All toolbar buttons have been reset";
    } catch (const std::exception& e) {
        qCritical() << "Exception in resetToolbarButtons: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in resetToolbarButtons()";
    }
}

// 禁用鼠标跟踪
void BaseTab::disableMouseTracking()
{
    try {
        // 只为plot1禁用鼠标跟踪
        if (m_pPlot1) {
            // 断开信号连接
            disconnect(m_pPlot1, SIGNAL(mouseMove(QMouseEvent *)), this, SLOT(onMouseMove(QMouseEvent *)));
            disconnect(m_pPlot1, SIGNAL(mouseRelease(QMouseEvent *)), this, SLOT(onMouseRelease(QMouseEvent *)));

            // 禁用鼠标跟踪
            m_pPlot1->setMouseTracking(false);

            // 恢复原始交互状态，包括图表拖动功能
            m_pPlot1->setInteractions(m_originalInteractions);

            // 重绘图表
            m_pPlot1->replot();
        }

        // 重绘plot2和plot3
        if (m_pPlot2) m_pPlot2->replot();
        if (m_pPlot3) m_pPlot3->replot();
    } catch (const std::exception& e) {
        qCritical() << "Exception in disableMouseTracking: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in disableMouseTracking()";
    }
}

// 切换十字线显示/隐藏
void BaseTab::showHideCrossHairs()
{
    try
    {
        if (!hasHVLine) // 当前没有显示十字线，需要显示
        {
            // 创建十字线
            createCrosshairs();

            // 启用鼠标跟踪
            enableMouseTracking();

            // 设置标志位
            hasHVLine = true;

            // 重绘所有图表
            CustomizePlot* plotsArray[] = {m_pPlot1, m_pPlot2, m_pPlot3};
            for (auto& plot : plotsArray) {
                if (plot) {
                    plot->replot();
                }
            }
        }
        else // 当前显示十字线，需要隐藏
        {
            // 禁用鼠标跟踪
            disableMouseTracking();

            // 移除十字线
            removeCrosshairs();

            // 设置标志位
            hasHVLine = false;
        }
    }
    catch (const std::exception &e)
    {
        qCritical() << "Exception in showHideCrossHairs(): " << e.what();
        // 确保清除所有十字线
        removeCrosshairs();
        // 确保状态一致
        hasHVLine = false;
    }
    catch (...) {
        qCritical() << "Unknown exception in showHideCrossHairs()";
        removeCrosshairs();
        hasHVLine = false;
    }
}

void BaseTab::reset()
{
    try {
        // 创建一个临时数组来存储所有图表
        CustomizePlot* plotsArray[] = {m_pPlot1, m_pPlot2, m_pPlot3};

        // 重置所有图表的坐标轴类型和范围
        for (auto& plot : plotsArray) {
            if (plot) {
                // 重置坐标轴类型为线性
                plot->xAxis->setScaleType(QCPAxis::stLinear);
                plot->yAxis->setScaleType(QCPAxis::stLinear);

                // 重置坐标轴刻度器
                plot->xAxis->setTicker(QSharedPointer<QCPAxisTicker>(new QCPAxisTicker));
                plot->yAxis->setTicker(QSharedPointer<QCPAxisTicker>(new QCPAxisTicker));

                // 重置坐标轴范围并重绘
                plot->rescaleAxes();
                plot->replot();
            }
        }

        // 重置所有工具栏的状态
        PlotToolbar* toolbarsArray[] = {m_pPlot1Toolbar, m_pPlot2Toolbar, m_pPlot3Toolbar};
        for (auto& toolbar : toolbarsArray) {
            if (toolbar) {
                toolbar->setXAxisLogState(false);
                toolbar->setYAxisLogState(false);
                toolbar->setZAxisLogState(false);
            }
        }

        // 确保标题元素不为空
        if (!m_title1 && m_pPlot1) {
            m_pPlot1->plotLayout()->insertRow(0);
            m_title1 = new QCPTextElement(m_pPlot1, "Fluorescence Map");
            m_pPlot1->plotLayout()->addElement(0, 0, m_title1);
            m_pPlot1->replot();
        }

        if (!m_title2 && m_pPlot2) {
            m_pPlot2->plotLayout()->insertRow(0);
            m_title2 = new QCPTextElement(m_pPlot2, "Decay Curve");
            m_pPlot2->plotLayout()->addElement(0, 0, m_title2);
            m_pPlot2->replot();
        }

        if (!m_title3 && m_pPlot3) {
            m_pPlot3->plotLayout()->insertRow(0);
            m_title3 = new QCPTextElement(m_pPlot3, " Spectral Curve");
            m_pPlot3->plotLayout()->addElement(0, 0, m_title3);
            m_pPlot3->replot();
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in reset: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in reset()";
    }
}

void BaseTab::choose_zoom()
{
    try {
        if (m_pPlot1) {
            // 先断开之前的连接，避免重复连接
            disconnect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(mousePress(QMouseEvent *)));
            disconnect(m_pPlot1, SIGNAL(mouseMove(QMouseEvent *)), this, SLOT(mouseMove2(QMouseEvent *)));
            disconnect(m_pPlot1, SIGNAL(mouseRelease(QMouseEvent *)), this, SLOT(mouseRelease(QMouseEvent *)));

            // 重新连接信号
            connect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(mousePress(QMouseEvent *)));
            connect(m_pPlot1, SIGNAL(mouseMove(QMouseEvent *)), this, SLOT(mouseMove2(QMouseEvent *)));
            connect(m_pPlot1, SIGNAL(mouseRelease(QMouseEvent *)), this, SLOT(mouseRelease(QMouseEvent *)));
        } else {
            qWarning() << "Cannot setup zoom: m_pPlot1 is null";
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in choose_zoom: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in choose_zoom()";
    }
}

void BaseTab::onMousePress(QMouseEvent *event)
{
    try
    {
        if (!event) {
            qWarning() << "onMousePress called with null event";
            return;
        }

        // 处理右键点击 - 对所有图表生效
        if (event->button() == Qt::RightButton)
        {
            onRightButtonPress(event);
            return;
        }

        // 以下为左键点击处理
        // 获取发送信号的对象
        QObject* sender = QObject::sender();

        // 只处理来自plot1的左键点击
        if (sender == m_pPlot1 && event->button() == Qt::LeftButton)
        {
            // 处理十字线
            if (hasHVLine && !mutableData.empty() && !mutableData[0].empty())
            {
                // 确保十字线模式下图表不会被拖动或缩放
                if ((m_pPlot1->interactions() & QCP::iRangeDrag) || (m_pPlot1->interactions() & QCP::iRangeZoom)) {
                    // 如果图表拖动或缩放仍然启用，则禁用它们
                    m_originalInteractions = m_pPlot1->interactions();
                    m_pPlot1->setInteraction(QCP::iRangeDrag, false);
                    m_pPlot1->setInteraction(QCP::iRangeZoom, false);
                }

                QCPColorMap *colorMap = qobject_cast<QCPColorMap*>(m_pPlot1->plottable(0));
                if (!colorMap) return;

                // 1. 获取原始屏幕坐标映射到的数据坐标（用于十字线）
                int xScreen = static_cast<int>(m_pPlot1->xAxis->pixelToCoord(event->pos().x()));
                int yScreen = static_cast<int>(m_pPlot1->yAxis->pixelToCoord(event->pos().y()));

                // 2. 获取映射到实际数据索引的坐标（用于更新曲线）
                double xValue, yValue;
                colorMap->pixelsToCoords(event->pos(), xValue, yValue);
                int dataIndex[2];
                colorMap->data()->coordToCell(xValue, yValue, &dataIndex[0], &dataIndex[1]);

                // 确保坐标在有效范围内
                xScreen = qBound(0, xScreen, static_cast<int>(m_pPlot1->xAxis->range().upper));
                yScreen = qBound(0, yScreen, static_cast<int>(m_pPlot1->yAxis->range().upper));

                int xData = qBound(0, dataIndex[0], static_cast<int>(mutableData.size()) - 1);
                int yData = qBound(0, dataIndex[1], static_cast<int>(mutableData[0].size()) - 1);

                // 3. 使用不同的坐标更新不同的显示
                updateCrosshairPosition(xScreen, yScreen);  // 使用屏幕映射坐标更新十字线
                updateDecayCurvePlot(xData);               // 使用数据索引更新衰减曲线
                updateSpectralCurvePlot(yData);            // 使用数据索引更新光谱曲线

                // 4. 重绘主图表
                m_pPlot1->replot();
            }
        }
    }
    catch (const std::exception &e)
    {
        qCritical() << "Exception in onMousePress(): " << e.what();
    }
    catch (...) {
        qCritical() << "Unknown exception in onMousePress()";
    }
}

void BaseTab::onRightButtonPress(QMouseEvent *event)
{
    try
    {
        if (!event) {
            qWarning() << "onRightButtonPress called with null event";
            return;
        }

        // 只处理右键点击
        if (event->button() != Qt::RightButton) {
            return;
        }

        // 创建上下文菜单
        QMenu contextMenu(this);

        // 添加“保存数据”动作
        QAction *saveDataAction = contextMenu.addAction("保存数据");
        connect(saveDataAction, &QAction::triggered, [this]()
                {
                    if (m_fillQCustomPlotData.empty()) {
                        QMessageBox::warning(this, tr("警告"), tr("无数据图像保存"));
                        return;
                    }

                    // 弹出文件保存对话框
                    QString filePath = QFileDialog::getSaveFileName(this, tr("Save to CSV"), "", tr("CSV Files (*.csv)"));
                    if (!filePath.isEmpty()) {
                        // 打开文件
                        QFile file(filePath);
                        if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
                            QMessageBox::warning(this, tr("错误"), tr("无法打开文件进行写入"));
                            return;
                        }

                        QTextStream out(&file);
                        out.setEncoding(QStringConverter::Utf8); // 设置编码为UTF-8

                        // 写入表头（时间通道数）
                        out << 0;
                        for (int col = 0; col < m_fillQCustomPlotData[0].size(); ++col) {
                            out << "," << col + 1;
                        }
                        out << "\n";

                        // 写入数据
                        for (int row = 0; row < m_fillQCustomPlotData.size(); ++row) {
                            // 写入波长通道数
                            out << row + 1;

                            // 写入数据行
                            for (int col = 0; col < m_fillQCustomPlotData[row].size(); ++col) {
                                out << "," << m_fillQCustomPlotData[row][col];
                            }
                            out << "\n";
                        }

                        // 关闭文件
                        file.close();

                        QMessageBox::information(this, tr("成功"), tr("数据已保存为CSV文件"));
                    } });

        // 添加“保存图像”动作
        QAction *saveImageAction = contextMenu.addAction("保存图像");
        connect(saveImageAction, &QAction::triggered, [this]()
                {
                    if (m_fillQCustomPlotData.empty()) {
                        QMessageBox::warning(this, tr("警告"), tr("无数据图像保存"));
                        return;
                    }

                    // 弹出文件保存对话框
                    QString filePath = QFileDialog::getSaveFileName(this, tr("Save Image"), "", tr("Image Files (*.png *.jpg)"));
                    if (!filePath.isEmpty()) {
                        // 保存图像
                        if (filePath.endsWith(".png", Qt::CaseInsensitive)) {
                            if (m_pPlot1->savePng(filePath)) {
                                QMessageBox::information(this, tr("Success"), tr("Image saved successfully"));
                            } else {
                                QMessageBox::warning(this, tr("Error"), tr("Failed to save image"));
                            }
                        } else if (filePath.endsWith(".jpg", Qt::CaseInsensitive)) {
                            if (m_pPlot1->saveJpg(filePath)) {
                                QMessageBox::information(this, tr("Success"), tr("Image saved successfully"));
                            } else {
                                QMessageBox::warning(this, tr("Error"), tr("Failed to save image"));
                            }
                        }
                    } });

        // 在鼠标右键点击位置显示菜单
        contextMenu.exec(event->globalPos());
    }
    catch (const std::exception &e)
    {
        qCritical() << "Exception in onRightButtonPress(): " << e.what();
    }
    catch (...) {
        qCritical() << "Unknown exception in onRightButtonPress()";
    }
}

// 更新十字线样式
void BaseTab::updateCrosshairStyle()
{
    try {
        // 检查十字线是否存在
        if (!hline || !vline) {
            qWarning() << "Cannot update crosshair style: crosshair lines are null";
            return;
        }

        // 获取当前主题
        QString theme = ThemeManager::getCurrentTheme();

        QPen crosshairPen;

        if (theme == "Dark") {
            // 暗色主题下的十字线样式 - 使用浅色虚线
            crosshairPen = QPen(QColor(255, 255, 255, 180)); // 半透明白色
        } else {
            // 亮色主题下的十字线样式 - 使用深色虚线
            crosshairPen = QPen(QColor(0, 0, 0, 180)); // 半透明黑色
        }

        // 设置虚线样式
        crosshairPen.setStyle(Qt::DashLine);
        crosshairPen.setWidthF(1.0); // 设置线宽

        // 应用样式到十字线
        hline->setPen(crosshairPen);
        vline->setPen(crosshairPen);

    } catch (const std::exception& e) {
        qCritical() << "Exception in updateCrosshairStyle: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in updateCrosshairStyle()";
    }
}

// 更新十字线位置
void BaseTab::updateCrosshairPosition(int x, int y)
{
    try {
        // 检查plot1是否存在
        if (!m_pPlot1) {
            qWarning() << "Cannot update crosshair position: m_pPlot1 is null";
            return;
        }

        // 更新水平线位置
        if (hline != nullptr && hline->point1 && hline->point2)
        {
            hline->point1->setCoords(m_pPlot1->xAxis->range().upper, y);
            hline->point2->setCoords(m_pPlot1->xAxis->range().lower, y);
            // 确保水平线可见
            hline->setVisible(true);
        }

        // 更新垂直线位置
        if (vline != nullptr && vline->point1 && vline->point2)
        {
            vline->point1->setCoords(x, m_pPlot1->yAxis->range().upper);
            vline->point2->setCoords(x, m_pPlot1->yAxis->range().lower);
            // 确保垂直线可见
            vline->setVisible(true);
        }

        // 不在这里重绘图表，由调用者负责重绘
    } catch (const std::exception& e) {
        qCritical() << "Exception in updateCrosshairPosition: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in updateCrosshairPosition()";
    }
}

// 更新衰减曲线图表 (m_pPlot2)
void BaseTab::updateDecayCurvePlot(int x)
{
    try {
        // 检查plot2是否存在
        if (!m_pPlot2) {
            qWarning() << "Cannot update decay curve plot: m_pPlot2 is null";
            return;
        }
        // 获取m_pPlot1的纵坐标范围
        QCPRange yRange = m_pPlot1->yAxis->range();

        // 创建数据点
        QVector<double> xData, yData;

        // 填充数据
        if (x >= 0 && x < static_cast<int>(mutableData.size()))
        {
            // 只添加在当前范围内的数据点
            for (int i = 0; i < numCols; ++i)
            {
                double timeValue = timeValues[i];  // 使用计算好的时间值
                if (timeValue >= yRange.lower && timeValue <= yRange.upper)
                {
                    xData.append(timeValue);
                    if (i < static_cast<int>(mutableData[x].size()))
                    {
                        yData.append(mutableData[x][i]);
                    }
                    else
                    {
                        yData.append(0.0);
                    }
                }
            }
        }

        // 移除临时曲线（十字线生成的曲线）
        // 保留用户添加的曲线
        for (int i = m_pPlot2->graphCount() - 1; i >= 0; --i) {
            QCPGraph* graph = m_pPlot2->graph(i);
            if (graph && graph->property("isCrosshairCurve").toBool()) {
                m_pPlot2->removeGraph(i);
            }
        }

        // 创建新图形
        m_pPlot2->addGraph();
        QCPGraph* newGraph = m_pPlot2->graph(m_pPlot2->graphCount()-1);
        newGraph->setData(xData, yData);
        newGraph->setLineStyle(QCPGraph::lsLine);

        // 使用 ThemeManager 获取主题感知的颜色
        QColor curveColor = ThemeManager::getDecayCurveColor();
        newGraph->setPen(QPen(curveColor, 2));

        // 设置有意义的名称
        QString curveName = QString("λ=%1nm").arg(x);
        newGraph->setName(curveName);

        // 设置自定义属性，标记为临时曲线
        newGraph->setProperty("isCrosshairCurve", true);

        // 确保图形添加到图例中
        newGraph->addToLegend();

        // 确保标题元素存在
        if (!m_title2)
        {
            m_pPlot2->plotLayout()->insertRow(0);
            m_title2 = new QCPTextElement(m_pPlot2, "Decay Curve");
            m_pPlot2->plotLayout()->addElement(0, 0, m_title2);
        }

        // 只应用主题样式，不修改图形颜色
        m_pPlot2->applyThemeOnly();

        // 计算Y轴范围
        double maxY = 0;
        if (x >= 0 && x < static_cast<int>(mutableData.size()))
        {
            for (int j = 0; j < static_cast<int>(mutableData[x].size()); ++j)
            {
                maxY = std::max(maxY, mutableData[x][j]);
            }
        }

        // 增加缓冲空间
        maxY = maxY * 1.2;
        if (maxY <= 0) maxY = 100;

        // 设置轴标签和范围
        m_pPlot2->xAxis->setLabel("Time (ns)");
        m_pPlot2->xAxis->setRange(yRange); // 使用m_pPlot1的纵坐标范围
        m_pPlot2->yAxis->setRange(5, maxY);//暂时设置一下不从0开始
        m_pPlot2->yAxis->setLabel("Intensity"); // 与 setupPlots 中的设置保持一致

        // 重绘
        m_pPlot2->replot();
    } catch (const std::exception& e) {
        qCritical() << "Exception in updateDecayCurvePlot: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in updateDecayCurvePlot()";
    }
}

// 更新光谱曲线图表 (m_pPlot3)
void BaseTab::updateSpectralCurvePlot(int y)
{
    try {
        // 检查plot3是否存在
        if (!m_pPlot3) {
            qWarning() << "Cannot update spectral curve plot: m_pPlot3 is null";
            return;
        }
        // 获取m_pPlot1的纵坐标范围
        QCPRange xRange = m_pPlot1->xAxis->range();

        // 创建数据点
        QVector<double> xData, yData;

        // 安全检查
        if (mutableData.empty()) {
            qWarning() << "Cannot update spectral curve plot: mutableData is empty";
            return;
        }

        // 填充数据
        if (y >= 0 && !mutableData.empty() && y < static_cast<int>(mutableData[0].size()))
        {
            // 只添加在当前范围内的数据点
            for (int i = 0; i < numRows; ++i)
            {
                double wavelengthValue = wavelengthValues[i];  // 使用计算好的波长值
                if (wavelengthValue >= xRange.lower && wavelengthValue <= xRange.upper)
                {
                    xData.append(wavelengthValue);
                    if (i < static_cast<int>(mutableData.size()) && y < static_cast<int>(mutableData[i].size()))
                    {
                        yData.append(mutableData[i][y]);
                    }
                    else
                    {
                        yData.append(0.0);
                    }
                }
            }
        }

        // 移除临时曲线（十字线生成的曲线）
        // 保留用户添加的曲线
        for (int i = m_pPlot3->graphCount() - 1; i >= 0; --i) {
            QCPGraph* graph = m_pPlot3->graph(i);
            if (graph && graph->property("isCrosshairCurve").toBool()) {
                m_pPlot3->removeGraph(i);
            }
        }

        // 创建新图形
        m_pPlot3->addGraph();
        QCPGraph* newGraph = m_pPlot3->graph(m_pPlot3->graphCount()-1);
        newGraph->setData(xData, yData);
        newGraph->setLineStyle(QCPGraph::lsLine);

        // 使用 ThemeManager 获取主题感知的颜色
        QColor curveColor = ThemeManager::getSpectralCurveColor();
        newGraph->setPen(QPen(curveColor, 2));

        // 设置有意义的名称
        QString curveName = QString("t=%1ns").arg(y);
        newGraph->setName(curveName);

        // 设置自定义属性，标记为临时曲线
        newGraph->setProperty("isCrosshairCurve", true);

        // 确保图形添加到图例中
        newGraph->addToLegend();

        qDebug() << "BaseTab::updateSpectralCurvePlot - Updated Plot3 with" << xData.size() << "data points";

        // 确保标题元素存在
        if (!m_title3)
        {
            m_pPlot3->plotLayout()->insertRow(0);
            m_title3 = new QCPTextElement(m_pPlot3, " Spectral Curve");
            m_pPlot3->plotLayout()->addElement(0, 0, m_title3);
        }

        // 只应用主题样式，不修改图形颜色
        m_pPlot3->applyThemeOnly();

        // 计算Y轴范围
        double maxY = 0;
        for (int i = 0; i < numRows; ++i)
        {
            if (i < static_cast<int>(mutableData.size()) && y < static_cast<int>(mutableData[i].size()))
            {
                maxY = std::max(maxY, mutableData[i][y]);
            }
        }

        // 增加缓冲空间
        maxY = maxY * 1.2;
        if (maxY <= 0) maxY = 100;

        // 设置轴标签和范围
        m_pPlot3->xAxis->setLabel("Wavelength (nm)"); // 与 setupPlots 中的设置保持一致，注意大小写
        m_pPlot3->yAxis->setRange(5, maxY);//暂时设置一下不从0开始
        m_pPlot3->xAxis->setRange(xRange);
        m_pPlot3->yAxis->setLabel("Intensity");

        // 重绘
        m_pPlot3->replot();

    } catch (const std::exception& e) {
        qCritical() << "Exception in updateSpectralCurvePlot: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in updateSpectralCurvePlot()";
    }
}

void BaseTab::onMouseMove(QMouseEvent *event)
{
    try
    {
        if (!event) {
            qWarning() << "onMouseMove called with null event";
            return;
        }

        // 鼠标移动事件只处理来自plot1的事件
        // 这里不需要检查sender，因为我们只为plot1连接了mouseMove信号

        // 防止多次调用导致的问题
        static QPoint lastPos = QPoint(-1, -1);
        if (lastPos == event->pos())
        {
            return; // 如果鼠标位置没有变化，则不处理
        }
        lastPos = event->pos();

        // 检查十字线是否启用
        if (!hasHVLine)
        {
            return; // 如果十字线未启用，则不处理
        }

        // 检查是否按下了鼠标左键
        if (!(event->buttons() & Qt::LeftButton))
        {
            return; // 如果没有按下鼠标左键，则不处理
        }

        // 检查十字线对象是否存在
        if (!hline || !vline)
        {
            // 如果十字线对象不存在，则创建十字线
            createCrosshairs();
            if (!hline || !vline) {
                qWarning() << "Cannot process mouse move: failed to create crosshairs";
                return;
            }
        }

        // 检查plot1是否存在
        if (!m_pPlot1) {
            qWarning() << "Cannot process mouse move: m_pPlot1 is null";
            return;
        }

        // 检查数据是否有效
        if (mutableData.empty() || mutableData[0].empty())
        {
            qWarning() << "Cannot process mouse move: mutableData is empty";
            return; // 如果数据为空，则不处理
        }
        QCPColorMap *colorMap = qobject_cast<QCPColorMap*>(m_pPlot1->plottable(0));
        if (!colorMap) return;

        // 1. 获取原始屏幕坐标映射到的数据坐标（用于十字线）
        int xScreen = static_cast<int>(m_pPlot1->xAxis->pixelToCoord(event->pos().x()));
        int yScreen = static_cast<int>(m_pPlot1->yAxis->pixelToCoord(event->pos().y()));

        // 2. 获取映射到实际数据索引的坐标（用于更新曲线）
        double xValue, yValue;
        colorMap->pixelsToCoords(event->pos(), xValue, yValue);
        int dataIndex[2];
        colorMap->data()->coordToCell(xValue, yValue, &dataIndex[0], &dataIndex[1]);

        // 确保坐标在有效范围内
        xScreen = qBound(0, xScreen, static_cast<int>(m_pPlot1->xAxis->range().upper));
        yScreen = qBound(0, yScreen, static_cast<int>(m_pPlot1->yAxis->range().upper));

        int xData = qBound(0, dataIndex[0], static_cast<int>(mutableData.size()) - 1);
        int yData = qBound(0, dataIndex[1], static_cast<int>(mutableData[0].size()) - 1);

        // 3. 使用不同的坐标更新不同的显示
        updateCrosshairPosition(xScreen, yScreen);  // 使用屏幕映射坐标更新十字线
        updateDecayCurvePlot(xData);               // 使用数据索引更新衰减曲线
        updateSpectralCurvePlot(yData);            // 使用数据索引更新光谱曲线

        // 4. 重绘主图表
        m_pPlot1->replot();
    }
    catch (const std::exception &e)
    {
        qCritical() << "Exception in onMouseMove(): " << e.what();
    }
    catch (...) {
        qCritical() << "Unknown exception in onMouseMove()";
    }
}

void BaseTab::onMouseRelease(QMouseEvent *event)
{
    try
    {
        if (!event) {
            qWarning() << "onMouseRelease called with null event";
            return;
        }

        // 鼠标释放事件只处理来自plot1的事件
        // 这里不需要检查sender，因为我们只为plot1连接了mouseRelease信号

        // 如果十字线模式激活，确保图表拖动和缩放仍然禁用
        // 这样可以避免在十字线模式下意外地拖动或缩放图表
        if (hasHVLine && m_pPlot1) {
            // 确保图表拖动和缩放仍然禁用
            m_pPlot1->setInteraction(QCP::iRangeDrag, false);
            m_pPlot1->setInteraction(QCP::iRangeZoom, false);
        }
    }
    catch (const std::exception &e)
    {
        qCritical() << "Exception in onMouseRelease(): " << e.what();
    }
    catch (...) {
        qCritical() << "Unknown exception in onMouseRelease()";
    }
}

void BaseTab::mousePress(QMouseEvent *e)
{
    // 处理右键点击，初始化橡皮筋选择
    if (e->button() == Qt::RightButton)
    {
        // 记录鼠标点击的起始位置
        rubberOrigin = e->pos();

        // 初始化橡皮筋矩形为零大小
        rubberBand->setGeometry(QRect(rubberOrigin, QSize()));

        // 显示橡皮筋
        rubberBand->show();
    }
}

void BaseTab::mouseMove2(QMouseEvent *e)
{
    try {
        if (!e) {
            qWarning() << "mouseMove2 called with null event";
            return;
        }

        // 检查rubberBand是否存在
        if (!rubberBand) {
            qWarning() << "Cannot update rubber band: rubberBand is null";
            return;
        }

        // 如果橡皮筋可见，更新其几何形状
        if (rubberBand->isVisible())
        {
            // 使用原点和当前鼠标位置创建矩形，并进行标准化
            QRect rect = QRect(rubberOrigin, e->pos()).normalized();
            rubberBand->setGeometry(rect);
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in mouseMove2: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in mouseMove2()";
    }
}

void BaseTab::mouseRelease(QMouseEvent *e)
{
    try {
        // 不使用事件参数，但仍然检查是否为空
        if (!e) {
            qWarning() << "mouseRelease called with null event";
            // 继续执行，因为我们实际上并不使用事件参数
        }

        // 检查rubberBand是否存在
        if (!rubberBand) {
            qWarning() << "Cannot process rubber band selection: rubberBand is null";
            return;
        }

        // 处理橡皮筋选择完成
        if (rubberBand->isVisible())
        {
            // 检查plot1是否存在
            if (!m_pPlot1) {
                qWarning() << "Cannot process rubber band selection: m_pPlot1 is null";
                rubberBand->hide();
                return;
            }

            // 获取橡皮筋选择区域
            const QRect zoomRect = rubberBand->geometry();

            // 获取选择区域的像素坐标
            int xp1, yp1, xp2, yp2;
            zoomRect.getCoords(&xp1, &yp1, &xp2, &yp2);

            // 将像素坐标转换为数据坐标
            double x1 = m_pPlot1->xAxis->pixelToCoord(xp1);
            double x2 = m_pPlot1->xAxis->pixelToCoord(xp2);
            double y1 = m_pPlot1->yAxis->pixelToCoord(yp1);
            double y2 = m_pPlot1->yAxis->pixelToCoord(yp2);

            // 设置 m_pPlot1 的新范围
            m_pPlot1->xAxis->setRange(x1, x2);
            m_pPlot1->yAxis->setRange(y1, y2);

            // 隐藏橡皮筋
            rubberBand->hide();

            // 更新并重绘 m_pPlot1
            m_pPlot1->replot();

            // 更新并重绘 m_pPlot2
            if (m_pPlot2) {
                m_pPlot2->xAxis->setLabel("wavelength (nm)");
                m_pPlot2->xAxis->setRange(x1, x2);
                m_pPlot2->yAxis->rescale();
                m_pPlot2->replot();
            }

            // 更新并重绘 m_pPlot3
            if (m_pPlot3) {
                m_pPlot3->xAxis->setLabel("Time (ns)");
                m_pPlot3->xAxis->setRange(y2, y1);
                m_pPlot3->yAxis->rescale();
                m_pPlot3->replot();
            }
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in mouseRelease: " << e.what();
        // 确保橡皮筋被隐藏
        if (rubberBand && rubberBand->isVisible()) {
            rubberBand->hide();
        }
    } catch (...) {
        qCritical() << "Unknown exception in mouseRelease()";
        // 确保橡皮筋被隐藏
        if (rubberBand && rubberBand->isVisible()) {
            rubberBand->hide();
        }
    }
}


void BaseTab::Zero_Data(std::vector<std::vector<int>> &data)
{
    try {
        // 安全检查
        if (data.empty()) {
            qWarning() << "Cannot process Zero_Data: data is empty";
            return;
        }

        std::vector<int> risingPoints;
        for (int x = 0; x < data.size(); ++x)
        {
            const std::vector<int> &curve = data[x];

            // 安全检查
            if (curve.empty()) {
                qWarning() << "Cannot process Zero_Data: curve at index " << x << " is empty";
                risingPoints.push_back(0); // 添加默认值以保持索引对齐
                continue;
            }

            int risingPoint = 0;
            int maxPeakIndex = -1;

            // 找到第一个既是最大值点又是极值点的位置
            for (size_t i = 1; i < curve.size() - 1; ++i)
            {
                if (curve[i] > curve[i - 1] && curve[i] > curve[i + 1])
                {
                    // 是极值点
                    if (maxPeakIndex == -1)
                    {
                        // 还没找到最大值点，当前点作为候选
                        maxPeakIndex = static_cast<int>(i);
                    }
                    else if (curve[i] > curve[maxPeakIndex])
                    {
                        // 当前极值点的值比之前记录的最大值点大，更新最大值点
                        maxPeakIndex = static_cast<int>(i);
                    }
                }
            }
            if (maxPeakIndex != -1)
            {
                // 从最大值点往前回溯，找到不满足递减趋势的点
                for (int j = maxPeakIndex; j > 0; --j)
                {
                    if (curve[j] < curve[j - 1])
                    {
                        // 不满足递减趋势，前一点即为时间零点
                        risingPoint = j;
                        break;
                    }
                }
            }
            risingPoints.push_back(risingPoint);
        }

        // 安全检查
        if (risingPoints.empty()) {
            qWarning() << "Cannot process Zero_Data: risingPoints is empty";
            return;
        }

        // 找到统一的零点
        int unifiedZero = *std::min_element(risingPoints.begin(), risingPoints.end());

        // 根据统一零点进行数据对齐
        for (int x = 0; x < data.size(); ++x)
        {
            // 安全检查
            if (x >= risingPoints.size()) {
                qWarning() << "Cannot process Zero_Data: index out of bounds for risingPoints";
                continue;
            }

            int shift = risingPoints[x] - unifiedZero;
            std::vector<int> &curve = data[x];

            // 安全检查
            if (curve.empty()) {
                continue;
            }

            if (shift > 0)
            {
                // 右移数据
                for (size_t i = 0; i < curve.size() - shift; ++i)
                {
                    curve[i] = curve[i + shift];
                }
                // 填充末尾部分为0
                for (size_t i = curve.size() - shift; i < curve.size(); ++i)
                {
                    curve[i] = 0;
                }
            }
            else if (shift < 0)
            {
                // 左移数据
                for (size_t i = -shift; i < curve.size(); ++i)
                {
                    curve[i + shift] = curve[i];
                }
                // 填充开头部分为0
                for (size_t i = 0; i < -shift; ++i)
                {
                    curve[i] = 0;
                }
            }
        }

        // 使用统一时间零点后的数据调用绘图函数
        fillQCustomPlot(data);
    } catch (const std::exception& e) {
        qCritical() << "Exception in Zero_Data: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in Zero_Data()";
    }
}

void BaseTab::setupPlots()
{
    try {
        // 确保在重新初始化前清理旧的图表对象
        if (m_pPlot1) { delete m_pPlot1; m_pPlot1 = nullptr; }
        if (m_pPlot2) { delete m_pPlot2; m_pPlot2 = nullptr; }
        if (m_pPlot3) { delete m_pPlot3; m_pPlot3 = nullptr; }

        // 初始化所有图表
        m_pPlot1 = new CustomizePlot(this);  // 设置父对象为this，确保自动删除
        m_pPlot2 = new CustomizePlot(this);
        m_pPlot3 = new CustomizePlot(this);

        // 设置图表类型
        m_pPlot1->setPlotStyle(PlotStyle::FluorescenceMap);
        m_pPlot2->setPlotStyle(PlotStyle::DecayCurve);
        m_pPlot3->setPlotStyle(PlotStyle::SpectralCurve);

        if (!m_pPlot1 || !m_pPlot2 || !m_pPlot3) {
            qCritical() << "Failed to create one or more plot objects";
            throw std::runtime_error("Plot creation failed");
        }

        // 设置图例可见性
        if (m_pPlot2) {
            // 确保 legend 存在
            if (!m_pPlot2->legend) {
                m_pPlot2->legend = new QCPLegend;
            }
            m_pPlot2->legend->setVisible(true);
            m_pPlot2->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop|Qt::AlignRight);
        }

        if (m_pPlot3) {
            // 确保 legend 存在
            if (!m_pPlot3->legend) {
                m_pPlot3->legend = new QCPLegend;
            }
            m_pPlot3->legend->setVisible(true);
            m_pPlot3->axisRect()->insetLayout()->setInsetAlignment(0, Qt::AlignTop|Qt::AlignRight);
        }

        // 设置所有图表的通用属性
        CustomizePlot* plots[] = {m_pPlot1, m_pPlot2, m_pPlot3};
        for (int i = 0; i < 3; ++i) {
            if (plots[i]) {
                // 设置大小策略为强制扩展，使图表充分利用可用空间
                plots[i]->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
                // 设置最小尺寸，确保图表不会被压缩得太小
                plots[i]->setMinimumSize(300, 200);
            }
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in setupPlots: " << e.what();
        // 清理任何可能已创建的对象
        if (m_pPlot1) { delete m_pPlot1; m_pPlot1 = nullptr; }
        if (m_pPlot2) { delete m_pPlot2; m_pPlot2 = nullptr; }
        if (m_pPlot3) { delete m_pPlot3; m_pPlot3 = nullptr; }
        throw; // 重新抛出异常
    }

    try {
        // 设置 m_pPlot1 (荧光图)
        if (m_pPlot1) {
            m_pPlot1->plotLayout()->insertRow(0);
            m_title1 = new QCPTextElement(m_pPlot1, "Fluorescence Map");
            m_pPlot1->plotLayout()->addElement(0, 0, m_title1);
            // 所有类中共同使用的设置：
            m_pPlot1->xAxis->setLabel("Wavelength (nm)");
            m_pPlot1->yAxis->setLabel("Time (ns)");
        }

        // 设置 m_pPlot2 (衰减曲线)
        if (m_pPlot2) {
            m_pPlot2->plotLayout()->insertRow(0);
            m_title2 = new QCPTextElement(m_pPlot2, "Decay Curve");
            m_pPlot2->plotLayout()->addElement(0, 0, m_title2);
            // 所有类中共同使用的设置：
            m_pPlot2->xAxis->setLabel("Time (ns)");
            m_pPlot2->yAxis->setLabel("Intensity");
        }

        // 设置 m_pPlot3 (光谱曲线)
        if (m_pPlot3) {
            m_pPlot3->plotLayout()->insertRow(0);
            m_title3 = new QCPTextElement(m_pPlot3, " Spectral Curve");
            m_pPlot3->plotLayout()->addElement(0, 0, m_title3);
            // 所有类中共同使用的设置：
            m_pPlot3->xAxis->setLabel("Wavelength (nm)");
            m_pPlot3->yAxis->setLabel("Intensity");
        }


        // 在创建时应用当前主题样式，但不修改图形颜色
        if (m_pPlot1) m_pPlot1->applyCurrentThemeStyle(); // 初始化时使用applyCurrentThemeStyle是安全的，因为还没有图形
        if (m_pPlot2) m_pPlot2->applyCurrentThemeStyle(); // 初始化时使用applyCurrentThemeStyle是安全的，因为还没有图形
        if (m_pPlot3) m_pPlot3->applyCurrentThemeStyle(); // 初始化时使用applyCurrentThemeStyle是安全的，因为还没有图形
    } catch (const std::exception& e) {
        qCritical() << "Exception in setupPlots (plot configuration): " << e.what();
        throw; // 重新抛出异常
    }

    try {
        // 隐藏所有图表的关闭按钮
        QString hideCloseButtonStyle = "QTabBar::close-button { image: none; width: 0; height: 0; }";
        // 创建一个临时数组来存储所有图表
        CustomizePlot* plotsArray[] = {m_pPlot1, m_pPlot2, m_pPlot3};
        for (int i = 0; i < 3; ++i) {
            if (plotsArray[i]) {
                plotsArray[i]->setStyleSheet(hideCloseButtonStyle);
                plotsArray[i]->rescaleAxes();

                // 为所有图表连接鼠标按下信号
                // 在onMousePress中判断是否为右键点击或来自plot1的事件
                connect(plotsArray[i], SIGNAL(mousePress(QMouseEvent*)), this, SLOT(onMousePress(QMouseEvent*)));
            }
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in setupPlots (final configuration): " << e.what();
        // 异常已经处理，不再抛出
    }
}

void BaseTab::setupPlotToolbars()
{
    // 创建每个图表的工具栏
    m_pPlot1Toolbar = new PlotToolbar(m_pPlot1, m_tabType, PlotType::FluorescenceMap, this);
    m_pPlot2Toolbar = new PlotToolbar(m_pPlot2, m_tabType, PlotType::DecayCurve, this);
    m_pPlot3Toolbar = new PlotToolbar(m_pPlot3, m_tabType, PlotType::SpectralCurve, this);

    // 显示工具栏
    PlotToolbar* toolbars[] = {m_pPlot1Toolbar, m_pPlot2Toolbar, m_pPlot3Toolbar};
    for (int i = 0; i < 3; ++i) {
        if (toolbars[i]) {
            toolbars[i]->show();
        }
    }

    // 注意：不再在这里禁用同步功能，而是由各个Tab自己决定是否启用同步
    // 同步配置由SpecFLIM.cpp中的initializeOpenProjectSynchronizer方法统一管理

    // 连接工具栏信号到槽函数
    // 为每个工具栏单独连接信号，确保各自独立操作

    // Plot1 工具栏连接
    connect(m_pPlot1Toolbar, &PlotToolbar::crosshairsToggled, this, &BaseTab::onCrosshairsToggled);
    connect(m_pPlot1Toolbar, &PlotToolbar::resetClicked, this, &BaseTab::onPlot1ResetClicked);
    connect(m_pPlot1Toolbar, &PlotToolbar::zoomInClicked, this, &BaseTab::onPlot1ZoomInClicked);
    connect(m_pPlot1Toolbar, &PlotToolbar::zoomOutClicked, this, &BaseTab::onPlot1ZoomOutClicked);
    connect(m_pPlot1Toolbar, &PlotToolbar::linLogToggled, this, &BaseTab::onLinLogToggled);

    // Plot2 工具栏连接 - DecayCurve 类型，有 X轴、Y轴和归一化按钮
    connect(m_pPlot2Toolbar, &PlotToolbar::linLogToggled, this, &BaseTab::onLinLogToggled);
    connect(m_pPlot2Toolbar, &PlotToolbar::normalizeClicked, this, &BaseTab::onNormalizeClicked);

    // Plot3 工具栏连接 - SpectralCurve 类型，有 Y轴和归一化按钮
    connect(m_pPlot3Toolbar, &PlotToolbar::linLogToggled, this, &BaseTab::onLinLogToggled);
    connect(m_pPlot3Toolbar, &PlotToolbar::normalizeClicked, this, &BaseTab::onNormalizeClicked);
}

// Plot toolbar slot implementations
void BaseTab::onCrosshairsToggled(bool enabled)
{
    // Implementation of crosshairs toggle
    // 如果当前状态与期望状态不一致，才调用 showHideCrossHairs()
    if ((enabled && !hasHVLine) || (!enabled && hasHVLine))
    {
        try
        {
            showHideCrossHairs();
        }
        catch (const std::exception &e)
        {
            qDebug() << "Exception in showHideCrossHairs(): " << e.what();
            // 如果发生异常，重置 hasHVLine 状态
            hasHVLine = enabled;
        }
    }

    // 只更新发送信号的toolbar状态
    QObject *sender = QObject::sender();
    if (sender == m_pPlot1Toolbar)
    {
        m_pPlot1Toolbar->setCrosshairsState(enabled);
    }
    else if (sender == m_pPlot2Toolbar)
    {
        m_pPlot2Toolbar->setCrosshairsState(enabled);
    }
    else if (sender == m_pPlot3Toolbar)
    {
        m_pPlot3Toolbar->setCrosshairsState(enabled);
    }
}

void BaseTab::onResetClicked()
{
    // 如果在放大或缩小模式，先退出
    if (m_bZoomInMode)
    {
        m_bZoomInMode = false;
        disconnect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));
        m_pPlot1->setCursor(Qt::ArrowCursor);
        m_pPlot1Toolbar->setZoomInState(false);
    }

    if (m_bZoomOutMode)
    {
        m_bZoomOutMode = false;
        disconnect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));
        m_pPlot1->setCursor(Qt::ArrowCursor);
        m_pPlot1Toolbar->setZoomOutState(false);
    }

    // 恢复原始范围
    restoreOriginalRange();

    // 重置坐标轴类型和其他设置
    reset();
}

// Plot1 specific toolbar slots
void BaseTab::onPlot1ResetClicked()
{
    try {
        // 如果在放大模式，先退出放大模式
        if (m_bZoomInMode)
        {
            m_bZoomInMode = false;
            disconnect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));
            m_pPlot1->setCursor(Qt::ArrowCursor);
            m_pPlot1Toolbar->setZoomInState(false);
        }

        if (m_bZoomOutMode)
        {
            m_bZoomOutMode = false;
            disconnect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));
            m_pPlot1->setCursor(Qt::ArrowCursor);
            m_pPlot1Toolbar->setZoomOutState(false);
        }

        // 只重置 m_pPlot1
        if (m_pPlot1) {
            m_pPlot1->rescaleAxes();
            m_pPlot1->replot();
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in onPlot1ResetClicked: " << e.what();
    }
}

void BaseTab::onPlot1ZoomInClicked()
{
    try {
        // 如果已经在放大模式，则退出放大模式
        if (m_bZoomInMode)
        {
            m_bZoomInMode = false;
            // 断开鼠标点击信号连接
            disconnect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));
            // 恢复鼠标指针
            m_pPlot1->setCursor(Qt::ArrowCursor);
            // 取消选中状态
            m_pPlot1Toolbar->setZoomInState(false);
            return;
        }

        // 如果在缩小模式，先退出缩小模式
        if (m_bZoomOutMode)
        {
            m_bZoomOutMode = false;
            disconnect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));
            m_pPlot1->setCursor(Qt::ArrowCursor);
            m_pPlot1Toolbar->setZoomOutState(false);
        }

        // 进入放大模式
        m_bZoomInMode = true;

        // 保存原始范围（如果还没有保存过）
        if (m_originalXRange.size() == 0 || m_originalYRange.size() == 0)
        {
            saveOriginalRange();
        }

        // 设置鼠标指针为放大镜形状
        m_pPlot1->setCursor(Qt::CrossCursor);

        // 连接鼠标点击信号
        connect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));

        // 设置按钮选中状态
        m_pPlot1Toolbar->setZoomInState(true);
    } catch (const std::exception& e) {
        qCritical() << "Exception in onPlot1ZoomInClicked: " << e.what();
    }
}

void BaseTab::onPlot1ZoomOutClicked()
{
    try {
        // 如果已经在缩小模式，则退出缩小模式
        if (m_bZoomOutMode)
        {
            m_bZoomOutMode = false;
            // 断开鼠标点击信号连接
            disconnect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));
            // 恢复鼠标指针
            m_pPlot1->setCursor(Qt::ArrowCursor);
            // 取消选中状态
            m_pPlot1Toolbar->setZoomOutState(false);
            return;
        }

        // 如果在放大模式，先退出放大模式
        if (m_bZoomInMode)
        {
            m_bZoomInMode = false;
            disconnect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));
            m_pPlot1->setCursor(Qt::ArrowCursor);
            m_pPlot1Toolbar->setZoomInState(false);
        }

        // 进入缩小模式
        m_bZoomOutMode = true;

        // 保存原始范围（如果还没有保存过）
        if (m_originalXRange.size() == 0 || m_originalYRange.size() == 0)
        {
            saveOriginalRange();
        }

        // 设置鼠标指针为缩小镜形状
        m_pPlot1->setCursor(Qt::CrossCursor);

        // 连接鼠标点击信号
        connect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));

        // 设置按钮选中状态
        m_pPlot1Toolbar->setZoomOutState(true);
    } catch (const std::exception& e) {
        qCritical() << "Exception in onPlot1ZoomOutClicked: " << e.what();
    }
}



// Plot2 specific toolbar slots
void BaseTab::onPlot2ResetClicked()
{
    try {
        // 只重置 m_pPlot2
        if (m_pPlot2) {
            m_pPlot2->rescaleAxes();
            m_pPlot2->replot();
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in onPlot2ResetClicked: " << e.what();
    }
}



// Plot3 specific toolbar slots
void BaseTab::onPlot3ResetClicked()
{
    try {
        // 只重置 m_pPlot3
        if (m_pPlot3) {
            m_pPlot3->rescaleAxes();
            m_pPlot3->replot();
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in onPlot3ResetClicked: " << e.what();
    }
}



void BaseTab::onZoomInClicked()
{
    // 如果已经在放大模式，则退出放大模式
    // 注意：这里的“放大”是指显示内容变小（就像缩小一样）
    if (m_bZoomInMode)
    {
        m_bZoomInMode = false;
        // 断开鼠标点击信号连接
        disconnect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));
        // 恢复鼠标指针
        m_pPlot1->setCursor(Qt::ArrowCursor);
        // 取消选中状态
        m_pPlot1Toolbar->setZoomInState(false);
        return;
    }

    // 如果在缩小模式，先退出缩小模式
    if (m_bZoomOutMode)
    {
        m_bZoomOutMode = false;
        disconnect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));
        m_pPlot1->setCursor(Qt::ArrowCursor);
        m_pPlot1Toolbar->setZoomOutState(false);
    }

    // 进入放大模式
    m_bZoomInMode = true;

    // 保存原始范围（如果还没有保存过）
    if (m_originalXRange.size() == 0 || m_originalYRange.size() == 0)
    {
        saveOriginalRange();
    }

    // 设置鼠标指针为放大镜形状
    m_pPlot1->setCursor(Qt::CrossCursor);

    // 连接鼠标点击信号
    connect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));

    // 设置按钮选中状态
    m_pPlot1Toolbar->setZoomInState(true);
}

void BaseTab::onZoomOutClicked()
{
    // 如果已经在缩小模式，则退出缩小模式
    // 注意：这里的“缩小”是指显示内容变大（就像放大一样）
    if (m_bZoomOutMode)
    {
        m_bZoomOutMode = false;
        // 断开鼠标点击信号连接
        disconnect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));
        // 恢复鼠标指针
        m_pPlot1->setCursor(Qt::ArrowCursor);
        // 取消选中状态
        m_pPlot1Toolbar->setZoomOutState(false);
        return;
    }

    // 如果在放大模式，先退出放大模式
    if (m_bZoomInMode)
    {
        m_bZoomInMode = false;
        disconnect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));
        m_pPlot1->setCursor(Qt::ArrowCursor);
        m_pPlot1Toolbar->setZoomInState(false);
    }

    // 进入缩小模式
    m_bZoomOutMode = true;

    // 保存原始范围（如果还没有保存过）
    if (m_originalXRange.size() == 0 || m_originalYRange.size() == 0)
    {
        saveOriginalRange();
    }

    // 设置鼠标指针为缩小镜形状
    m_pPlot1->setCursor(Qt::CrossCursor);

    // 连接鼠标点击信号
    connect(m_pPlot1, SIGNAL(mousePress(QMouseEvent *)), this, SLOT(onZoomMousePress(QMouseEvent *)));

    // 设置按钮选中状态
    m_pPlot1Toolbar->setZoomOutState(true);
}

void BaseTab::onPanClicked(bool enabled)
{
    // Implementation of pan mode
    if (enabled)
    {
        m_pPlot1->setInteraction(QCP::iRangeDrag, true);
        m_pPlot2->setInteraction(QCP::iRangeDrag, true);
        m_pPlot3->setInteraction(QCP::iRangeDrag, true);
    }
    else
    {
        m_pPlot1->setInteraction(QCP::iRangeDrag, false);
        m_pPlot2->setInteraction(QCP::iRangeDrag, false);
        m_pPlot3->setInteraction(QCP::iRangeDrag, false);
    }
}

void BaseTab::onLinLogToggled(bool isLogScale, const QString& axisType)
{
    // 获取发送信号的对象
    QObject *sender = QObject::sender();

    // 根据发送者和轴类型确定要处理的图表和轴
    if (sender == m_pPlot1Toolbar)
    {
        if (axisType == "xAxis")
        {
            // Plot1 的 X 轴切换
            if (isLogScale)
            {
                m_pPlot1->xAxis->setScaleType(QCPAxis::stLogarithmic);
                QSharedPointer<QCPAxisTickerLog> logTicker(new QCPAxisTickerLog);
                logTicker->setLogBase(10);
                m_pPlot1->xAxis->setTicker(logTicker);
            }
            else
            {
                m_pPlot1->xAxis->setScaleType(QCPAxis::stLinear);
                m_pPlot1->xAxis->setTicker(QSharedPointer<QCPAxisTicker>(new QCPAxisTicker));
            }
            m_pPlot1->replot();

            // 更新 X 轴按钮状态
            m_pPlot1Toolbar->setXAxisLogState(isLogScale);
        }
        else if (axisType == "yAxis")
        {
            // Plot1 的 Y 轴切换
            if (isLogScale)
            {
                m_pPlot1->yAxis->setScaleType(QCPAxis::stLogarithmic);
                QSharedPointer<QCPAxisTickerLog> logTicker(new QCPAxisTickerLog);
                logTicker->setLogBase(10);
                m_pPlot1->yAxis->setTicker(logTicker);
            }
            else
            {
                m_pPlot1->yAxis->setScaleType(QCPAxis::stLinear);
                m_pPlot1->yAxis->setTicker(QSharedPointer<QCPAxisTicker>(new QCPAxisTicker));
            }
            m_pPlot1->replot();

            // 更新 Y 轴按钮状态
            m_pPlot1Toolbar->setYAxisLogState(isLogScale);
        }
        else if (axisType == "zAxis")
        {
            // Plot1 的 Z 轴切换（如果有颜色标度）
            // 在这里实现 Z 轴处理逻辑
            // 更新 Z 轴按钮状态
            m_pPlot1Toolbar->setZAxisLogState(isLogScale);
        }
    }
    else if (sender == m_pPlot2Toolbar)
    {
        if (axisType == "xAxis")
        {
            // Plot2 的 X 轴切换
            if (isLogScale)
            {
                m_pPlot2->xAxis->setScaleType(QCPAxis::stLogarithmic);
                QSharedPointer<QCPAxisTickerLog> logTicker(new QCPAxisTickerLog);
                logTicker->setLogBase(10);
                m_pPlot2->xAxis->setTicker(logTicker);
            }
            else
            {
                m_pPlot2->xAxis->setScaleType(QCPAxis::stLinear);
                m_pPlot2->xAxis->setTicker(QSharedPointer<QCPAxisTicker>(new QCPAxisTicker));
            }
            m_pPlot2->replot();

            // 更新 X 轴按钮状态
            m_pPlot2Toolbar->setXAxisLogState(isLogScale);
        }
        else if (axisType == "yAxis")
        {
            // Plot2 的 Y 轴切换
            if (isLogScale)
            {
                m_pPlot2->yAxis->setScaleType(QCPAxis::stLogarithmic);
                QSharedPointer<QCPAxisTickerLog> logTicker(new QCPAxisTickerLog);
                logTicker->setLogBase(10);
                m_pPlot2->yAxis->setTicker(logTicker);
            }
            else
            {
                m_pPlot2->yAxis->setScaleType(QCPAxis::stLinear);
                m_pPlot2->yAxis->setTicker(QSharedPointer<QCPAxisTicker>(new QCPAxisTicker));
            }
            m_pPlot2->replot();

            // 更新 Y 轴按钮状态
            m_pPlot2Toolbar->setYAxisLogState(isLogScale);
        }
    }
    else if (sender == m_pPlot3Toolbar)
    {
        if (axisType == "xAxis")
        {
            // Plot3 的 X 轴切换
            if (isLogScale)
            {
                m_pPlot3->xAxis->setScaleType(QCPAxis::stLogarithmic);
                QSharedPointer<QCPAxisTickerLog> logTicker(new QCPAxisTickerLog);
                logTicker->setLogBase(10);
                m_pPlot3->xAxis->setTicker(logTicker);
            }
            else
            {
                m_pPlot3->xAxis->setScaleType(QCPAxis::stLinear);
                m_pPlot3->xAxis->setTicker(QSharedPointer<QCPAxisTicker>(new QCPAxisTicker));
            }
            m_pPlot3->replot();

            // 更新 X 轴按钮状态
            m_pPlot3Toolbar->setXAxisLogState(isLogScale);
        }
        else if (axisType == "yAxis")
        {
            // Plot3 的 Y 轴切换
            if (isLogScale)
            {
                m_pPlot3->yAxis->setScaleType(QCPAxis::stLogarithmic);
                QSharedPointer<QCPAxisTickerLog> logTicker(new QCPAxisTickerLog);
                logTicker->setLogBase(10);
                m_pPlot3->yAxis->setTicker(logTicker);
            }
            else
            {
                m_pPlot3->yAxis->setScaleType(QCPAxis::stLinear);
                m_pPlot3->yAxis->setTicker(QSharedPointer<QCPAxisTicker>(new QCPAxisTicker));
            }
            m_pPlot3->replot();

            // 更新 Y 轴按钮状态
            m_pPlot3Toolbar->setYAxisLogState(isLogScale);
        }
    }
}

void BaseTab::onNormalizeClicked()
{
    // 获取发送信号的对象
    QObject *sender = QObject::sender();

    try {
        // 根据发送者确定要归一化的图表
        QCustomPlot* targetPlot = nullptr;

        if (sender == m_pPlot1Toolbar) {
            targetPlot = m_pPlot1;
        } else if (sender == m_pPlot2Toolbar) {
            targetPlot = m_pPlot2;
        } else if (sender == m_pPlot3Toolbar) {
            targetPlot = m_pPlot3;
        }

        if (targetPlot) {
            // 找出图表中所有数据的最大值
            double maxValue = 0;
            for (int i = 0; i < targetPlot->graphCount(); ++i) {
                QCPGraph* graph = targetPlot->graph(i);
                if (graph) {
                    for (int j = 0; j < graph->dataCount(); ++j) {
                        double value = graph->data()->at(j)->value;
                        if (value > maxValue) {
                            maxValue = value;
                        }
                    }
                }
            }

            // 归一化所有数据点
            if (maxValue > 0) {
                for (int i = 0; i < targetPlot->graphCount(); ++i) {
                    QCPGraph* graph = targetPlot->graph(i);
                    if (graph) {
                        QVector<QCPGraphData> normalizedData;
                        for (int j = 0; j < graph->dataCount(); ++j) {
                            QCPGraphData point = *graph->data()->at(j);
                            point.value /= maxValue;
                            normalizedData.append(point);
                        }

                        // 替换图表数据为归一化后的数据
                        graph->data()->clear();
                        for (const QCPGraphData& point : normalizedData) {
                            graph->addData(point.key, point.value);
                        }
                    }
                }

                // 重新缩放坐标轴并重绘图表
                targetPlot->rescaleAxes();
                targetPlot->replot();
            }
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in onNormalizeClicked: " << e.what();
    }
}

// 响应主题变化
void BaseTab::onThemeChanged(const QString& theme)
{
    // 如果十字线已经创建，更新其样式
    if (hasHVLine && hline && vline) {
        updateCrosshairStyle();

        // 重绘图表以显示更新后的十字线
        if (m_pPlot1) m_pPlot1->replot();
    }
}

bool BaseTab::eventFilter(QObject *watched, QEvent *event)
{
    // 处理窗口大小变化事件
    if (event->type() == QEvent::Resize)
    {
        // 如果是 m_pPlot1 或其容器的大小变化
        if (watched == m_pPlot1 || watched == m_pPlot1->parentWidget())
        {
            // 重新设置工具栏位置
            if (m_pPlot1Toolbar)
            {
                m_pPlot1Toolbar->move(m_pPlot1->width() - m_pPlot1Toolbar->width(), m_pPlot1->height() - m_pPlot1Toolbar->height());
            }
        }
        // 如果是 m_pPlot2 或其容器的大小变化
        else if (watched == m_pPlot2 || watched == m_pPlot2->parentWidget())
        {
            // 重新设置工具栏位置
            if (m_pPlot2Toolbar)
            {
                m_pPlot2Toolbar->move(m_pPlot2->width() - m_pPlot2Toolbar->width(), m_pPlot2->height() - m_pPlot2Toolbar->height());
            }
        }
    }

    // 继续传递事件给父类处理
    return QWidget::eventFilter(watched, event);
}

// 保存原始范围
void BaseTab::saveOriginalRange()
{
    // 保存 m_pPlot1 的原始范围
    QCPRange xRange = m_pPlot1->xAxis->range();
    QCPRange yRange = m_pPlot1->yAxis->range();

    // 检查范围是否有效
    if (xRange.size() > 0 && yRange.size() > 0)
    {
        m_originalXRange = xRange;
        m_originalYRange = yRange;
    }
    else
    {
        // 如果范围无效，设置一个默认范围
        m_originalXRange = QCPRange(0, 100);
        m_originalYRange = QCPRange(0, 100);
    }
}

// 恢复原始范围
void BaseTab::restoreOriginalRange()
{
    // 如果没有有效的原始范围，先保存当前范围
    if (m_originalXRange.size() <= 0 || m_originalYRange.size() <= 0)
    {
        saveOriginalRange();
    }

    // 再次检查以确保原始范围有效
    if (m_originalXRange.size() > 0 && m_originalYRange.size() > 0)
    {
        // 恢复 m_pPlot1 的范围
        m_pPlot1->xAxis->setRange(m_originalXRange);
        m_pPlot1->yAxis->setRange(m_originalYRange);
        m_pPlot1->replot();

        // 恢复 m_pPlot2, m_pPlot3 的范围
        m_pPlot2->rescaleAxes();
        m_pPlot2->replot();
        m_pPlot3->rescaleAxes();
        m_pPlot3->replot();
    }
    else
    {
        // 如果原始范围仍然无效，则使用 rescaleAxes 重置所有图表
        m_pPlot1->rescaleAxes();
        m_pPlot1->replot();
        m_pPlot2->rescaleAxes();
        m_pPlot2->replot();
        m_pPlot3->rescaleAxes();
        m_pPlot3->replot();
    }
}

// 处理放大/缩小模式下的鼠标点击
void BaseTab::onZoomMousePress(QMouseEvent *event)
{
    try {
        // 只处理左键点击
        if (event->button() != Qt::LeftButton)
        {
            return;
        }

        // 获取点击位置的坐标
        double x = m_pPlot1->xAxis->pixelToCoord(event->pos().x());
        double y = m_pPlot1->yAxis->pixelToCoord(event->pos().y());

        // 获取当前范围
        QCPRange xRange = m_pPlot1->xAxis->range();
        QCPRange yRange = m_pPlot1->yAxis->range();

        // 计算新的范围大小
        double xSize, ySize;

        if (m_bZoomInMode)
        {
            // 放大模式：扩大范围到 120%（缩小 20%）
            // 范围变大，显示内容变小
            xSize = xRange.size() * 1.2;
            ySize = yRange.size() * 1.2;
        }
        else if (m_bZoomOutMode)
        {
            // 缩小模式：缩小范围到 80%（放大 20%）
            // 范围变小，显示内容变大
            xSize = xRange.size() * 0.8;
            ySize = yRange.size() * 0.8;
        }
        else
        {
            // 不应该进入这里
            return;
        }

        // 设置新的范围，以点击位置为中心
        m_pPlot1->xAxis->setRange(x - xSize / 2, x + xSize / 2);
        m_pPlot1->yAxis->setRange(y - ySize / 2, y + ySize / 2);
        m_pPlot1->replot();

        // 不再同步到其他图表，每个图表独立操作
    } catch (const std::exception& e) {
        qCritical() << "Exception in onZoomMousePress: " << e.what();
    }
}
void BaseTab::calculateWavelengths(double centerWavelength)
{
    try {
        const double grooves = 600.0;  // 光栅刻线密度 (lines/mm)
        const double d = 1.0 / grooves;  // 光栅常数 (mm)
        const int m = 1;  // 衍射级次
        const double f = 305.0;  // 焦距 (mm)
        const double x_mppc = 0.5;  // 探测器偏移 (mm)
        const double delta = 0.0;  // 探测器角度 (degree)
        const double gamma = 26.0;  // 入射角和衍射角的夹角 (degree)

        // 计算光栅旋转角
        const double phi = std::asin(m * centerWavelength * 1e-6 / (2.0 * d * std::cos(gamma * M_PI / 360.0))) * 180.0 / M_PI;

        // 计算入射角和衍射角
        const double alpha = phi - gamma / 2.0;  // 入射角 (degree)
        const double beta = phi + gamma / 2.0;   // 衍射角 (degree)

        // 调整数组大小为128
        wavelengthValues.resize(128);

        // 计算前64个通道的波长
        for (int i = 0; i < 64; ++i) {
            double nx = -0.235 + x_mppc + (i - 64) * 0.25;  // mm
            double epsilon = std::atan((nx * std::cos(delta * M_PI / 180.0)) /
                                       (f + nx * std::sin(delta * M_PI / 180.0)));  // rad
            wavelengthValues[i] = (d / m) * (std::sin(alpha * M_PI / 180.0) +
                                             std::sin(beta * M_PI / 180.0 + epsilon)) * 1e6;  // nm
        }

        // 计算后64个通道的波长
        for (int i = 64; i < 128; ++i) {
            double nx = 0.235 + x_mppc + (i - 65) * 0.25;  // mm
            double epsilon = std::atan((nx * std::cos(delta * M_PI / 180.0)) /
                                       (f + nx * std::sin(delta * M_PI / 180.0)));  // rad
            wavelengthValues[i] = (d / m) * (std::sin(alpha * M_PI / 180.0) +
                                             std::sin(beta * M_PI / 180.0 + epsilon)) * 1e6;  // nm
        }
        // 设置坐标轴
        if (m_pPlot1) {
            QSharedPointer<QCPAxisTickerFixed> wavelengthTicker(new QCPAxisTickerFixed);
            wavelengthTicker->setTickStep(20.0);  // 每50nm显示一个刻度
            wavelengthTicker->setScaleStrategy(QCPAxisTickerFixed::ssNone);

            m_pPlot1->xAxis->setScaleType(QCPAxis::stLinear);
            m_pPlot1->xAxis->setTicker(wavelengthTicker);
            m_pPlot1->xAxis->setRange(wavelengthValues.front(), wavelengthValues.back());
            m_pPlot1->xAxis->setLabel("Wavelength (nm)");

        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in calculateWavelengths:" << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in calculateWavelengths";
    }
}

void BaseTab::calculateTime(int resolution)
{
    try {
        // 检查输入参数的有效性
        if (resolution <= 0) {
            qCritical() << "Invalid scale factor in calculateTime:" << resolution;
            return;
        }


        // 调整数组大小为4096
        timeValues.resize(4096);
        scaleFactor = resolution;

        // 计算时间值
        for (int i = 0; i < 4096; ++i) {
            timeValues[i] = (static_cast<double>(i) * resolution) / 1000.0;
        }

        // 设置坐标轴
        if (m_pPlot1) {
            QSharedPointer<QCPAxisTickerFixed> timeTicker(new QCPAxisTickerFixed);
            timeTicker->setTickStep(20);  // 将范围分为大约10个刻度
            timeTicker->setScaleStrategy(QCPAxisTickerFixed::ssNone);

            m_pPlot1->yAxis->setScaleType(QCPAxis::stLinear);
            m_pPlot1->yAxis->setTicker(timeTicker);
            m_pPlot1->yAxis->setRange(timeValues.front(), timeValues.back());
            m_pPlot1->yAxis->setLabel("Time (ns)");
        }

        qInfo() << "Time range:" << timeValues.front() << "to" << timeValues.back() << "ps";

    } catch (const std::exception& e) {
        qCritical() << "Exception in calculateTime:" << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in calculateTime";
    }
}


