#include "FileNameManager.h"
#include <QDir>
#include <QFileInfo>
#include <QRegularExpression>
#include <QMutexLocker>
#include <QDebug>
#include <QStandardPaths>

// 假设AppConfig类的简单接口
class AppConfig {
public:
    static AppConfig* getInstance() {
        static AppConfig instance;
        return &instance;
    }
    
    QString getValue(const QString& key, const QString& defaultValue = QString()) const {
        // 简化实现，实际应该从配置文件读取
        if (key == "ProjectPrefix") {
            return m_projectPrefix.isEmpty() ? defaultValue : m_projectPrefix;
        }
        return defaultValue;
    }
    
    void setValue(const QString& key, const QString& value) {
        if (key == "ProjectPrefix") {
            m_projectPrefix = value;
        }
    }
    
private:
    QString m_projectPrefix = "SFL";
};

// 静态成员初始化
FileNameManager* FileNameManager::s_instance = nullptr;
QMutex FileNameManager::s_mutex;

FileNameManager* FileNameManager::getInstance() {
    if (!s_instance) {
        QMutexLocker locker(&s_mutex);
        if (!s_instance) {
            s_instance = new FileNameManager();
        }
    }
    return s_instance;
}

FileNameManager::FileNameManager(QObject* parent)
    : QObject(parent)
    , m_appConfig(AppConfig::getInstance())
{
    initializeFromConfig();
}

FileNameManager::~FileNameManager() {
    // 析构函数实现
}

QString FileNameManager::getProjectPrefix() const {
    QString prefix = getConfigValue("ProjectPrefix", getDefaultProjectPrefix());
    return prefix.isEmpty() ? getDefaultProjectPrefix() : prefix;
}

QString FileNameManager::generateWorkspaceFileName() const {
    return generateWorkspaceFileName(QDate::currentDate());
}

QString FileNameManager::generateWorkspaceFileName(const QDate& date) const {
    QString prefix = getProjectPrefix();
    QString dateStr = formatDateString(date);
    return QString("%1%2.sflw").arg(prefix, dateStr);
}

QString FileNameManager::generateProjectFileName(int sequence) const {
    return generateProjectFileName(QDate::currentDate(), sequence);
}

QString FileNameManager::generateProjectFileName(const QDate& date, int sequence) const {
    QString prefix = getProjectPrefix();
    QString dateStr = formatDateString(date);
    return QString("%1%2_%3.sflp").arg(prefix, dateStr).arg(sequence, 3, 10, QChar('0'));
}

QString FileNameManager::generateOperationName(const QString& baseFileName, 
                                              const OperationSequence& sequence) const {
    if (sequence.isEmpty()) {
        qWarning() << "Empty operation sequence for base file:" << baseFileName;
        return QString();
    }
    
    QString baseName = extractBaseFileName(baseFileName);
    QString sequenceStr = sequence.toString();
    
    return QString("%1_%2").arg(baseName, sequenceStr);
}

QString FileNameManager::generateDecayAnalysisName(const QString& baseFileName, 
                                                   int sequence) const {
    if (sequence <= 0) {
        qWarning() << "Invalid sequence number for decay analysis:" << sequence;
        return QString();
    }
    
    QString baseName = extractBaseFileName(baseFileName);
    return QString("%1_DecAna_%2").arg(baseName).arg(sequence);
}

QString FileNameManager::generateSpectralAnalysisName(const QString& baseFileName, 
                                                      int sequence) const {
    if (sequence <= 0) {
        qWarning() << "Invalid sequence number for spectral analysis:" << sequence;
        return QString();
    }
    
    QString baseName = extractBaseFileName(baseFileName);
    return QString("%1_SpeAna_%2").arg(baseName).arg(sequence);
}

QString FileNameManager::generateSplitFileName(const QString& originalFileName, 
                                               int splitSequence) const {
    if (splitSequence <= 0) {
        qWarning() << "Invalid split sequence number:" << splitSequence;
        return QString();
    }
    
    QFileInfo fileInfo(originalFileName);
    QString baseName = fileInfo.completeBaseName();
    QString extension = fileInfo.suffix();
    
    return QString("%1_split_%2.%3").arg(baseName).arg(splitSequence).arg(extension);
}

QString FileNameManager::generateDisplayName(const QString& fileName, 
                                             FileType fileType) const {
    if (fileName.isEmpty()) {
        return QString();
    }
    
    switch (fileType) {
    case FileType::Workspace:
    case FileType::Project:
    case FileType::Split:
        return QFileInfo(fileName).fileName();
        
    case FileType::Operation:
        // 显示操作序列部分
        return extractOperationDisplayName(fileName);
        
    case FileType::DecayAnalysis:
    case FileType::SpectralAnalysis:
        // 显示分析类型和序号
        return extractAnalysisDisplayName(fileName);
    }
    
    return fileName;
}

int FileNameManager::getNextProjectSequence() const {
    // 扫描当前目录中的项目文件，找到最大序号
    QString currentDir = QDir::currentPath();
    QString pattern = QString("%1*.sflp").arg(getProjectPrefix());
    
    QStringList existingFiles = getExistingFiles(currentDir, pattern);
    int maxSequence = 0;
    
    QRegularExpression re(QString("%1\\d{6}_(\\d+)\\.sflp").arg(getProjectPrefix()));
    for (const QString& fileName : existingFiles) {
        QRegularExpressionMatch match = re.match(fileName);
        if (match.hasMatch()) {
            int sequence = match.captured(1).toInt();
            maxSequence = qMax(maxSequence, sequence);
        }
    }
    
    return maxSequence + 1;
}

int FileNameManager::getNextOperationSequence(const QString& sflpFileName, 
                                              OperationType operationType) const {
    QString typePrefix = UnifiedFileUtils::operationTypeToString(operationType);
    return getNextSequenceForType(sflpFileName, typePrefix);
}

int FileNameManager::getNextAnalysisSequence(const QString& sflpFileName, 
                                             AnalysisType analysisType) const {
    QString typePrefix = UnifiedFileUtils::analysisTypeToString(analysisType);
    return getNextSequenceForType(sflpFileName, typePrefix);
}

bool FileNameManager::validateFileName(const QString& fileName) const {
    return UnifiedFileUtils::validateFileName(fileName);
}

QString FileNameManager::sanitizeFileName(const QString& fileName) const {
    return UnifiedFileUtils::sanitizeFileName(fileName);
}

FileType FileNameManager::identifyFileType(const QString& fileName) const {
    if (fileName.isEmpty()) {
        return FileType::Project;
    }
    
    QFileInfo fileInfo(fileName);
    QString suffix = fileInfo.suffix().toLower();
    QString baseName = fileInfo.completeBaseName();
    
    if (suffix == "sflw") {
        return FileType::Workspace;
    } else if (suffix == "sflp") {
        if (baseName.contains("_split_")) {
            return FileType::Split;
        }
        return FileType::Project;
    } else if (baseName.contains("_DecAna_")) {
        return FileType::DecayAnalysis;
    } else if (baseName.contains("_SpeAna_")) {
        return FileType::SpectralAnalysis;
    } else if (baseName.contains("_al") || baseName.contains("_cr") || baseName.contains("_ad")) {
        return FileType::Operation;
    }
    
    return FileType::Project;
}

bool FileNameManager::isOperationFile(const QString& fileName) const {
    return identifyFileType(fileName) == FileType::Operation;
}

bool FileNameManager::isAnalysisFile(const QString& fileName) const {
    FileType type = identifyFileType(fileName);
    return type == FileType::DecayAnalysis || type == FileType::SpectralAnalysis;
}

int FileNameManager::extractSequenceNumber(const QString& fileName, const QString& pattern) const {
    QRegularExpression re(pattern);
    QRegularExpressionMatch match = re.match(fileName);
    
    if (match.hasMatch() && match.lastCapturedIndex() >= 1) {
        bool ok;
        int sequence = match.captured(1).toInt(&ok);
        return ok ? sequence : 0;
    }
    
    return 0;
}

OperationSequence FileNameManager::parseOperationSequence(const QString& fileName) const {
    // 从文件名中解析操作序列
    QRegularExpression re("_(al\\d+|cr\\d+|ad\\d+)");
    QRegularExpressionMatchIterator it = re.globalMatch(fileName);
    
    OperationSequence sequence;
    while (it.hasNext()) {
        QRegularExpressionMatch match = it.next();
        QString opStr = match.captured(1);
        
        QString typeStr = opStr.left(2);
        QString numStr = opStr.mid(2);
        
        OperationType type = UnifiedFileUtils::stringToOperationType(typeStr);
        bool ok;
        int num = numStr.toInt(&ok);
        
        if (ok && num > 0) {
            sequence.appendOperation(type, num);
        }
    }
    
    return sequence;
}

// 私有辅助方法实现
QString FileNameManager::formatDateString(const QDate& date) const {
    return date.toString("yyMMdd");
}

bool FileNameManager::isValidFileName(const QString& fileName) const {
    return validateFileName(fileName);
}

QString FileNameManager::getDefaultProjectPrefix() const {
    return "SFL";
}

QString FileNameManager::extractBaseFileName(const QString& fileName) const {
    QFileInfo fileInfo(fileName);
    return fileInfo.completeBaseName();
}

QString FileNameManager::generateUniqueFileName(const QString& baseName, const QString& extension) const {
    QString fileName = QString("%1.%2").arg(baseName, extension);

    if (!QFile::exists(fileName)) {
        return fileName;
    }

    int counter = 1;
    do {
        fileName = QString("%1_%2.%3").arg(baseName).arg(counter).arg(extension);
        counter++;
    } while (QFile::exists(fileName) && counter < 1000);

    return fileName;
}

int FileNameManager::getNextSequenceForType(const QString& sflpFileName, const QString& typePrefix) const {
    QMutexLocker locker(&m_operationMutex);

    // 扫描现有文件，找到该类型的最大序号
    QString baseName = extractBaseFileName(sflpFileName);
    QString currentDir = QFileInfo(sflpFileName).absolutePath();

    QStringList existingFiles = getExistingFiles(currentDir, QString("%1_*").arg(baseName));
    int maxSequence = 0;

    QString pattern = QString("%1_.*_%2(\\d+)").arg(baseName, typePrefix);
    QRegularExpression re(pattern);

    for (const QString& fileName : existingFiles) {
        QRegularExpressionMatch match = re.match(fileName);
        if (match.hasMatch()) {
            int sequence = match.captured(1).toInt();
            maxSequence = qMax(maxSequence, sequence);
        }
    }

    return maxSequence + 1;
}

void FileNameManager::updateSequenceCounter(const QString& sflpFileName, const QString& typePrefix, int value) const {
    // 发送信号通知序号更新
    if (typePrefix == "al") {
        emit const_cast<FileNameManager*>(this)->sequenceNumberUpdated(sflpFileName, OperationType::Alignment, value);
    } else if (typePrefix == "cr") {
        emit const_cast<FileNameManager*>(this)->sequenceNumberUpdated(sflpFileName, OperationType::Crop, value);
    } else if (typePrefix == "ad") {
        emit const_cast<FileNameManager*>(this)->sequenceNumberUpdated(sflpFileName, OperationType::AddCurve, value);
    } else if (typePrefix == "DecAna") {
        emit const_cast<FileNameManager*>(this)->sequenceNumberUpdated(sflpFileName, AnalysisType::DecayAnalysis, value);
    } else if (typePrefix == "SpeAna") {
        emit const_cast<FileNameManager*>(this)->sequenceNumberUpdated(sflpFileName, AnalysisType::SpectralAnalysis, value);
    }
}

void FileNameManager::initializeFromConfig() {
    // 从配置初始化
    if (m_appConfig) {
        // 配置已经通过AppConfig单例管理
        qDebug() << "FileNameManager initialized with project prefix:" << getProjectPrefix();
    }
}

QString FileNameManager::getConfigValue(const QString& key, const QString& defaultValue) const {
    if (m_appConfig) {
        return m_appConfig->getValue(key, defaultValue);
    }
    return defaultValue;
}

bool FileNameManager::matchesPattern(const QString& fileName, const QString& pattern) const {
    QRegularExpression re(QRegularExpression::wildcardToRegularExpression(pattern));
    return re.match(fileName).hasMatch();
}

QStringList FileNameManager::getExistingFiles(const QString& directory, const QString& pattern) const {
    QDir dir(directory);
    if (!dir.exists()) {
        return QStringList();
    }

    QStringList nameFilters;
    nameFilters << pattern;

    return dir.entryList(nameFilters, QDir::Files, QDir::Name);
}

QString FileNameManager::extractOperationDisplayName(const QString& fileName) const {
    // 提取操作显示名称，例如 "al1_cr2_ad3"
    OperationSequence sequence = parseOperationSequence(fileName);
    return sequence.toString();
}

QString FileNameManager::extractAnalysisDisplayName(const QString& fileName) const {
    // 提取分析显示名称，例如 "DecAna_1" 或 "SpeAna_2"
    QRegularExpression re("_(DecAna|SpeAna)_(\\d+)");
    QRegularExpressionMatch match = re.match(fileName);

    if (match.hasMatch()) {
        return QString("%1_%2").arg(match.captured(1), match.captured(2));
    }

    return fileName;
}
