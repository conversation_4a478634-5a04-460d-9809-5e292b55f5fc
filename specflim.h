#pragma once

#include <QMainWindow>
#include <QComboBox>
#include <QSpinBox>
#include <QLabel>
#include <QtSerialPort/qserialport.h>
#include <QtSerialPort/qserialportinfo.h>
#include <QPushButton>
//#include <QToolBar>
#include <QVBoxLayout>
#include <QFileSystemModel>
#include <QTreeView>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPlainTextEdit>
#include <QMessageBox>
#include <QScrollArea>
#include <QHostAddress>
#include <QUdpSocket>
#include <QLineEdit>
//#include "qcustomPlot.h"
#include <QCheckBox>
#include <QFont>

#include "ConfigurationTab.h"
#include "AcquireTab.h"
#include "Analysis.h"
#include "ProcessTab.h"
#include "QuantifyTab.h"
#include "LocalSerialStatus.h"
#include "QTCPMgr.h"
#include "TabSynchronizer.h"
#include "data/UnifiedFileStructures.h"

QT_BEGIN_NAMESPACE
namespace Ui {
class SpecFLIM;
}
QT_END_NAMESPACE

class SpecFLIM : public QMainWindow
{
    Q_OBJECT

public:
    SpecFLIM(QWidget *parent = nullptr);
    ~SpecFLIM();

    void setSerialStatus(LocalSerialStatus *serialStatus);

protected:
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    bool eventFilter(QObject *obj, QEvent *event) override;
    //void resizeEvent(QResizeEvent *event) override;

private slots:
    void onSliderValueChanged(); // 滑动条滑动结束时的槽函数
    void maximizeContentArea();
    void toggleMaximize();
    void onResolutionChanged(int value); // 新增槽函数

private:
    Ui::SpecFLIM *ui;
    QTabWidget *m_pTabWidget;
    QSlider    *m_pScaleSlider; // 滑动条控件
    double      m_previousScaleFactor; // 新增成员变量来保存上一次的 ScaleFactor

    QToolBar *m_pTitleBar;
    bool m_isMaximized;

    bool m_dragging;
    QPoint m_dragPosition;

    // 窗口调整大小相关变量
    bool m_resizing;
    int m_resizeMargin;
    Qt::Edges m_resizeEdges;

    ConfigurationTab *m_pConfigTab;
    AcquireTab       *m_pAcquireTab;
    ProcessTab       *m_pProcessTab;
    QuantifyTab      *m_pQuantifyTab;
    AnalysisTab      *m_pAnalysisTab;



    QPushButton *m_pMinButton;
    QPushButton *m_pMaxButton;
    QPushButton *m_pCloseButton;

    LocalSerialStatus *m_pSerialStatus;

    QTCPMgr*   m_pTcpMgr;
    double     m_dMinBinWidth;
    int m_iSpectralChannelNumber;
    int m_iTimeChannelNumber;

private:
    void InitUI();
    void scaleWidget(QWidget *widget, double sizeScaleFactor, double fontScaleFactor);
    void initializeOpenProjectSynchronizer();
};

