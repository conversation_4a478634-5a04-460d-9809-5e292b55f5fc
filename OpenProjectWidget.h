#pragma once

#include <QWidget>
#include <QFileSystemModel>
#include <QTreeView>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QStackedWidget>
#include <QString>
#include <QMenu>
#include <QVector>
#include <QStandardItemModel> // 添加QStandardItemModel头文件
#include <QProgressDialog>
#include <memory>
#include "ProjectFileManager.h"
#include "AppConfig.h"
#include "data/UnifiedFileStructures.h"

// Tab类型枚举
enum class TabType {
    Acquire,
    Process,
    Analysis
};


class OpenProjectWidget : public QWidget
{
    Q_OBJECT

public:
    explicit OpenProjectWidget(QWidget *parent = nullptr);

    // Select a specific row by file name
    // If emitSignal is false, the dataRowClicked signal will not be emitted
    void selectFileByName(const QString& fileName, bool emitSignal = true);

    // Get the currently selected file name
    QString getSelectedFileName();

    // Get the tree view for direct access
    QTreeView* getTreeView() { return treeView; }

    // 统一文件管理接口
    void refreshFileTreeWithVirtualNodes();
    void setFileTypeFilter(TabType currentTab);
    bool selectFileInTree(const QString& fileName);
    QString getCurrentSelectedFile() const;


signals:
    void dataRowClicked(int rowIndex, const QString &fileName);

    // 统一文件管理信号
    void virtualNodeSelected(const QString& nodeName, FileType nodeType);
    void fileTreeRefreshed();
    void filterChanged(TabType currentTab);

private slots:
    void onShowLocalFiles();
    void onCreateProject();
    void onOpenProject();
    void onSaveProject();

private:
    bool readCSV(const QString &filePath, std::vector<std::vector<int> > &data);
    void showContextMenu(const QPoint &pos);
    bool loadData(const std::string &filePath, std::vector<std::vector<int>> &data);
    void onOpenCsvFile();
    void onOpenTxtFile();
    void onOpenDataFile();
    void onCurrentItemChanged(const QModelIndex &current, const QModelIndex &previous);
    QString formatFileSize(qint64 size);

    QFileSystemModel *fileSystemModel;
    QStandardItemModel *treeModel; // 添加QStandardItemModel成员变量
    QTreeView *treeView; // 修改为QTableView
    QPushButton *showLocalFilesButton;
    QPushButton *createProjectButton;
    QPushButton *openProjectButton;
    QPushButton *saveProjectButton;
    ProjectFileManager *projectFileManager;
    AppConfig *appConfig;

    // 标记是否正在处理选择操作，避免重复发送信号
    bool m_isProcessingSelection = false;

    // 统一文件管理私有成员
    QMap<QString, QStandardItem*> m_virtualNodeMap;
    TabType m_currentTabType;
    mutable QMutex m_treeMutex;
    QMap<QString, bool> m_nodeExpandState;
    QString m_lastSelectedFile;

    // 虚拟节点处理
    void addVirtualChildren(QStandardItem* parentItem, const QString& sflpFileName);
    QStandardItem* createFileItem(const QString& fileName);
    QStandardItem* createOperationItem(const QString& operationName);
    QStandardItem* createAnalysisItem(const QString& analysisName, AnalysisType type);
    QIcon getFileTypeIcon(FileType fileType) const;
    void applyCurrentFilter();
    void saveTreeState();
    void restoreTreeState();
    QStandardItem* findItemByFileName(const QString& fileName) const;
    void selectItem(QStandardItem* item);
    void logTreeOperation(const QString& operation, bool success);
};