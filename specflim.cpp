#include "specflim.h"
#include "ui_specflim.h"
#include <QSplitter>
#include <QFont>
#include "CompressionThread.h"
#include <QResizeEvent>

SpecFLIM::SpecFLIM(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::SpecFLIM)
    , m_pConfigTab(nullptr)
    , m_pAcquireTab(nullptr)
    , m_pProcessTab(nullptr)
    , m_pQuantifyTab(nullptr)
    , m_pAnalysisTab(nullptr)
    , m_previousScaleFactor(1.0)
    , m_dragging(false)
    , m_pTcpMgr(nullptr)
    , m_isMaximized(true)
    , m_resizing(false)
    , m_resizeMargin(5)
    , m_dMinBinWidth(0)
{
    resize(1024, 800); // 设置默认大小为 800x600 像素
    ui->setupUi(this);
    InitUI();

    // 新增：连接 AcquireTab 的 resolutionChanged 信号到 SpecFLIM 的槽函数
    connect(m_pAcquireTab, &AcquireTab::resolutionChanged, this, &SpecFLIM::onResolutionChanged);
}

void SpecFLIM::InitUI()
{
    QFont iconFont("iconfont");

    this->setWindowIcon(QIcon(":/Res/Title.jpeg"));
    this->setWindowTitle(QStringLiteral("SpecFLIM"));
    this->setWindowFlags(Qt::FramelessWindowHint); // 设置窗口无边框

    // 隐藏状态栏
    statusBar()->hide();

    // 安装事件过滤器以实现窗口调整大小
    this->installEventFilter(this);
    this->setAttribute(Qt::WA_Hover);

    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout;

    // 创建工具栏作为标题栏
    m_pTitleBar = new QToolBar(this);
    m_pTitleBar->setObjectName("TitleBar");
    m_pTitleBar->setFloatable(false);
    m_pTitleBar->setMouseTracking(true);

    // 创建一个包含 QHBoxLayout 的自定义小部件
    QWidget *customWidget = new QWidget(this);
    QHBoxLayout *titleLayout = new QHBoxLayout(customWidget);
    titleLayout->setContentsMargins(0, 0, 0, 0);
    titleLayout->setSpacing(0);

    // 添加窗口图标
    QLabel *iconLabel = new QLabel(customWidget);
    iconLabel->setPixmap(QPixmap(":/Res/Title.jpeg").scaled(32, 32, Qt::KeepAspectRatio));
    titleLayout->addWidget(iconLabel);
    titleLayout->addSpacing(10);

    // 添加标题
    QLabel *titleLabel = new QLabel("SFLT", customWidget);
    titleLayout->addWidget(titleLabel);
    titleLayout->addSpacing(10);

    // 创建滑动条
    m_pScaleSlider = new QSlider(Qt::Horizontal, customWidget);
    m_pScaleSlider->setRange(1, 100); // 设置滑动条的范围，例如从1到10
    m_pScaleSlider->setValue(50); // 设置初始值
    m_pScaleSlider->setFixedSize(200, 30); // 设置滑动条的固定大小
    connect(m_pScaleSlider, &QSlider::sliderReleased, this, &SpecFLIM::onSliderValueChanged);

    titleLayout->addWidget(m_pScaleSlider);
    // titleLayout->addSeparator();

    // 添加拉伸因子，使后续控件靠右
    titleLayout->addStretch();

    // 添加窗口控制按钮
    m_pMinButton = new QPushButton(customWidget);
    m_pMinButton->setFont(iconFont);
    m_pMinButton->setText(QChar(0xe61e)); // 0xe601为图标的Unicode编码

    m_pMaxButton = new QPushButton(customWidget);
    m_pMaxButton->setFont(iconFont);
    m_pMaxButton->setText(QChar(0xe6db));

    m_pCloseButton = new QPushButton(customWidget);
    m_pCloseButton->setFont(iconFont);
    m_pCloseButton->setText(QChar(0xe640));

    m_pMinButton->setStyleSheet("font-size: 16px;");
    m_pMaxButton->setStyleSheet("font-size: 16px;");
    m_pCloseButton->setStyleSheet("background-color: red; font-size: 16px;");

    // 连接窗口控制按钮的信号与槽
    connect(m_pMinButton, &QPushButton::clicked, this, &QWidget::showMinimized);
    connect(m_pMaxButton, &QPushButton::clicked, this, &SpecFLIM::toggleMaximize);
    connect(m_pCloseButton, &QPushButton::clicked, this, &QWidget::close);

    titleLayout->addWidget(m_pMinButton);
    titleLayout->addWidget(m_pMaxButton);
    titleLayout->addWidget(m_pCloseButton);

    // 创建 QWidgetAction 并设置自定义小部件
    QWidgetAction *widgetAction = new QWidgetAction(m_pTitleBar);
    widgetAction->setDefaultWidget(customWidget);

    // 将 QWidgetAction 添加到工具栏
    m_pTitleBar->addAction(widgetAction);
    mainLayout->addWidget(m_pTitleBar); // 添加标题栏到主布局

    // 创建选项卡栏
    m_pTabWidget = new QTabWidget(this);
    setMenuWidget(m_pTabWidget); // 设置为菜单栏的位置

    // 创建选项卡页面
    m_pConfigTab = new ConfigurationTab();

    m_pTabWidget->addTab(m_pConfigTab, "Configuration");

    m_pAcquireTab = new AcquireTab();
    m_pTabWidget->addTab(m_pAcquireTab, "Acquire");

    m_pProcessTab = new ProcessTab();
    m_pTabWidget->addTab(m_pProcessTab, "Process");


    m_pQuantifyTab = new QuantifyTab();
    // m_pTabWidget->addTab(m_pQuantifyTab, "Quality");


    m_pAnalysisTab = new AnalysisTab();
    m_pTabWidget->addTab(m_pAnalysisTab, "Analysis");

    m_pQuantifyTab = new QuantifyTab();
    m_pTabWidget->addTab(m_pQuantifyTab, "Quantify");

    // 设置选项卡栏的样式
    m_pTabWidget->setStyleSheet("QTabBar::tab {"
                             "padding: 10px;"
                             "min-width: 80px;"
                             "}");

    // 直接将选项卡栏添加到主布局
    mainLayout->addWidget(m_pTabWidget);


    // 设置主窗口的布局
    QWidget *centralWidget = new QWidget(this);
    centralWidget->setLayout(mainLayout);
    setCentralWidget(centralWidget);

    m_pAcquireTab->setMonoInfo(m_pConfigTab->m_MonoInfo);
    qInfo()<<"SpecFLIM::InitUI, id = "<< m_pConfigTab->m_MonoInfo.iMonoID;

    connect(m_pConfigTab, &ConfigurationTab::ConfigMonoOpened, m_pAcquireTab, &AcquireTab::onAcqMonoReady);
    connect(m_pConfigTab, &ConfigurationTab::CTUpdateFrameInMsec, m_pAcquireTab, &AcquireTab::SetFrameIntervalTime);

    if (m_pConfigTab) {
        m_pTcpMgr = m_pConfigTab->getTCPMgr();
        m_dMinBinWidth = m_pConfigTab->getMinBinWidth();
        m_iSpectralChannelNumber = m_pConfigTab->getSpectralChannelNumber(); // 获取光谱通道数
        m_iTimeChannelNumber = m_pConfigTab->getTimeChannelNumber();         // 获取时间通道数
    }

    if (m_pAcquireTab) {
        m_pAcquireTab->setTCPMgr(m_pTcpMgr);
        m_pAcquireTab->setMinBinWidth(m_dMinBinWidth);
        m_pAcquireTab->setSpectralChannelNumber(m_iSpectralChannelNumber); // 传递光谱通道数
        m_pAcquireTab->setTimeChannelNumber(m_iTimeChannelNumber);         // 传递时间通道数
    }

    // Initialize OpenProjectSynchronizer and register OpenProjectWidgets
    initializeOpenProjectSynchronizer();
    if (m_pConfigTab && m_pAcquireTab) {
        // 连接 TDCConfiguration 的 adjustableLevelsChanged 信号到 AcquireTab 的槽函数
        TDCConfiguration* tdcConfig = m_pConfigTab->getTDCConfig();
        if (tdcConfig) {
            connect(tdcConfig, &TDCConfiguration::adjustableLevelsChanged, m_pAcquireTab, &AcquireTab::onAdjustableLevelsChanged);
        }
    }
}

SpecFLIM::~SpecFLIM()
{
    delete ui;
}

void SpecFLIM::initializeOpenProjectSynchronizer() {
    // Get the TabSynchronizer instance
    TabSynchronizer* synchronizer = TabSynchronizer::getInstance();

    // Find OpenProjectWidget instances in each tab
    QList<OpenProjectWidget*> acquireWidgets = m_pAcquireTab->findChildren<OpenProjectWidget*>();
    QList<OpenProjectWidget*> processWidgets = m_pProcessTab->findChildren<OpenProjectWidget*>();
    QList<OpenProjectWidget*> analysisWidgets = m_pAnalysisTab->findChildren<OpenProjectWidget*>();

    // Register OpenProjectWidget instances with the synchronizer
    if (!acquireWidgets.isEmpty()) {
        synchronizer->registerOpenProjectWidget(acquireWidgets.first(), TabType::AcquireTab);
    }

    if (!processWidgets.isEmpty()) {
        synchronizer->registerOpenProjectWidget(processWidgets.first(), TabType::ProcessTab);
    }

    if (!analysisWidgets.isEmpty()) {
        synchronizer->registerOpenProjectWidget(analysisWidgets.first(), TabType::AnalysisTab);
    }

    // 注册每个标签页的plot到TabSynchronizer
    // Process标签页
    if (m_pProcessTab) {
        synchronizer->registerPlot(TabType::ProcessTab, PlotType::FluorescenceMap, m_pProcessTab->getPlot1());
        synchronizer->registerPlot(TabType::ProcessTab, PlotType::DecayCurve, m_pProcessTab->getPlot2());
        synchronizer->registerPlot(TabType::ProcessTab, PlotType::SpectralCurve, m_pProcessTab->getPlot3());
    }

    // Analysis标签页
    if (m_pAnalysisTab) {
        synchronizer->registerPlot(TabType::AnalysisTab, PlotType::FluorescenceMap, m_pAnalysisTab->getPlot1());
        synchronizer->registerPlot(TabType::AnalysisTab, PlotType::DecayCurve, m_pAnalysisTab->getPlot2());
        synchronizer->registerPlot(TabType::AnalysisTab, PlotType::SpectralCurve, m_pAnalysisTab->getPlot3());
    }

    // 连接标签页切换信号到TabSynchronizer的onTabChanged槽函数
    connect(m_pTabWidget, &QTabWidget::currentChanged, synchronizer, &TabSynchronizer::onTabChanged);

    qDebug() << "One-way synchronization configured from Process to Analysis tab";
}

void SpecFLIM::setSerialStatus(LocalSerialStatus *serialStatus) {
    this->m_pSerialStatus = serialStatus;
    if(m_pConfigTab)
        m_pConfigTab->setSerialStatus(m_pSerialStatus);
    if(m_pAcquireTab)
        m_pAcquireTab->setSerialStatus(m_pSerialStatus);
}

void SpecFLIM::onSliderValueChanged() {
    // 添加日志记录当前时间
    qDebug() << "onSliderValueChanged called at" << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz") << "with slider value" << m_pScaleSlider->value();

    // 根据滑动条的值来调整界面的缩放比例
    double scaleFactorChange = (m_pScaleSlider->value() - 50) / 50.0 * 0.5 + 1;
    double sizeScaleFactor = scaleFactorChange / m_previousScaleFactor;

    // 只有在缩放因子发生变化时才调整控件
    if (sizeScaleFactor != m_previousScaleFactor) {
        // 创建一个QWidget*数组来存储需要缩放的控件
        QWidget* widgetsToScale[] = {m_pTabWidget, m_pTitleBar};

        // 遍历数组并调用scaleWidget函数
        for (QWidget* widget : widgetsToScale) {
            if (widget) {
                scaleWidget(widget, sizeScaleFactor, scaleFactorChange);
            }
        }

        // 更新 m_previousScaleFactor
        m_previousScaleFactor = scaleFactorChange;
    }
}

// 添加新的辅助函数来递归调整子控件
void SpecFLIM::scaleWidget(QWidget *widget, double sizeScaleFactor, double fontScaleFactor) {
    // 获取默认字体并调整其大小
    QFont defaultFont = QApplication::font();
    defaultFont.setPointSizeF(defaultFont.pointSizeF() * fontScaleFactor);
    widget->setFont(defaultFont);

    // 调整当前控件的几何尺寸
    widget->resize(widget->size() * sizeScaleFactor);

    // 递归调整子控件
    for (QObject *child : widget->children()) {
        QWidget *childWidget = qobject_cast<QWidget*>(child);
        if (childWidget) {
            scaleWidget(childWidget, sizeScaleFactor, fontScaleFactor);
        }
    }
}

void SpecFLIM::onResolutionChanged(int value)
{
    qDebug() << "Resolution changed to:" << value;
    if(m_pConfigTab)
    {
        m_pConfigTab->setResolutionChanged(value);
    }



    // 在这里可以处理分辨率变化的逻辑
}

void SpecFLIM::mousePressEvent(QMouseEvent *event) {
    // 如果正在调整大小，则不处理拖动
    if (!m_resizing && event->button() == Qt::LeftButton && m_pTitleBar->rect().contains(event->pos())) {
        m_dragging = true;
        m_dragPosition = event->globalPos() - frameGeometry().topLeft();
    }
    QMainWindow::mousePressEvent(event);
}

void SpecFLIM::mouseMoveEvent(QMouseEvent *event) {
    // 如果正在调整大小，则不处理拖动
    if (!m_resizing && m_dragging && (event->buttons() & Qt::LeftButton)) {
        move(event->globalPos() - m_dragPosition);
        event->accept();
    }
    QMainWindow::mouseMoveEvent(event);
}

void SpecFLIM::mouseReleaseEvent(QMouseEvent *event) {
    if (event->button() == Qt::LeftButton) {
        m_dragging = false;
    }
    QMainWindow::mouseReleaseEvent(event);
}

void SpecFLIM::maximizeContentArea()
{
    // 直接显示选项卡内容
}

void SpecFLIM::toggleMaximize()
{
    if (m_isMaximized)
    {
        showNormal();
        m_isMaximized = false;
    }
    else
    {
        showMaximized();
        m_isMaximized = true;
    }
}

bool SpecFLIM::eventFilter(QObject *obj, QEvent *event)
{
    if (obj == this)
    {
        if (event->type() == QEvent::HoverMove)
        {
            // 当鼠标移动时，检测是否在窗口边缘
            QHoverEvent *hoverEvent = static_cast<QHoverEvent*>(event);
            QPoint pos = hoverEvent->pos();
            int x = pos.x();
            int y = pos.y();
            int width = this->width();
            int height = this->height();

            // 判断鼠标是否在窗口边缘
            bool onLeft = x <= m_resizeMargin;
            bool onRight = x >= width - m_resizeMargin;
            bool onTop = y <= m_resizeMargin;
            bool onBottom = y >= height - m_resizeMargin;

            // 设置鼠标形状
            if (onTop && onLeft) {
                setCursor(Qt::SizeFDiagCursor);
                m_resizeEdges = Qt::TopEdge | Qt::LeftEdge;
            } else if (onTop && onRight) {
                setCursor(Qt::SizeBDiagCursor);
                m_resizeEdges = Qt::TopEdge | Qt::RightEdge;
            } else if (onBottom && onLeft) {
                setCursor(Qt::SizeBDiagCursor);
                m_resizeEdges = Qt::BottomEdge | Qt::LeftEdge;
            } else if (onBottom && onRight) {
                setCursor(Qt::SizeFDiagCursor);
                m_resizeEdges = Qt::BottomEdge | Qt::RightEdge;
            } else if (onLeft) {
                setCursor(Qt::SizeHorCursor);
                m_resizeEdges = Qt::LeftEdge;
            } else if (onRight) {
                setCursor(Qt::SizeHorCursor);
                m_resizeEdges = Qt::RightEdge;
            } else if (onTop) {
                setCursor(Qt::SizeVerCursor);
                m_resizeEdges = Qt::TopEdge;
            } else if (onBottom) {
                setCursor(Qt::SizeVerCursor);
                m_resizeEdges = Qt::BottomEdge;
            } else {
                setCursor(Qt::ArrowCursor);
                m_resizeEdges = Qt::Edges();
            }
        }
        else if (event->type() == QEvent::MouseButtonPress)
        {
            // 当鼠标按下时，如果在边缘则开始调整大小
            QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
            if (mouseEvent->button() == Qt::LeftButton && m_resizeEdges != Qt::Edges())
            {
                m_resizing = true;
                m_dragPosition = mouseEvent->globalPos();
                return true; // 已处理事件
            }
        }
        else if (event->type() == QEvent::MouseMove)
        {
            // 当鼠标移动时，如果正在调整大小则改变窗口大小
            if (m_resizing)
            {
                QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
                QPoint delta = mouseEvent->globalPos() - m_dragPosition;
                QRect geometry = this->geometry();

                // 根据边缘调整窗口大小
                if (m_resizeEdges & Qt::LeftEdge)
                {
                    geometry.setLeft(geometry.left() + delta.x());
                }
                if (m_resizeEdges & Qt::RightEdge)
                {
                    geometry.setRight(geometry.right() + delta.x());
                }
                if (m_resizeEdges & Qt::TopEdge)
                {
                    geometry.setTop(geometry.top() + delta.y());
                }
                if (m_resizeEdges & Qt::BottomEdge)
                {
                    geometry.setBottom(geometry.bottom() + delta.y());
                }

                // 设置新的窗口几何尺寸
                this->setGeometry(geometry);
                m_dragPosition = mouseEvent->globalPos();
                return true; // 已处理事件
            }
        }
        else if (event->type() == QEvent::MouseButtonRelease)
        {
            // 当鼠标释放时，结束调整大小
            if (m_resizing)
            {
                m_resizing = false;
                return true; // 已处理事件
            }
        }
    }

    return QMainWindow::eventFilter(obj, event);
}
