#include "VirtualNodeManager.h"
#include "FileRelationshipCache.h"
#include "FileNameManager.h"
#include <QMutexLocker>
#include <QDebug>
#include <QDir>
#include <QFileInfo>
#include <QRegularExpression>
#include <QHeaderView>

VirtualNodeManager::VirtualNodeManager(QTreeView* treeView, QStandardItemModel* model, QObject* parent)
    : QObject(parent)
    , m_treeView(treeView)
    , m_treeModel(model)
    , m_relationshipCache(new FileRelationshipCache(this))
{
    loadIcons();
    
    // 连接关系缓存信号
    connect(m_relationshipCache, &FileRelationshipCache::relationshipAdded,
            this, [this](const QString& parent, const QString& child) {
                refreshVirtualNode(parent);
            });
    
    connect(m_relationshipCache, &FileRelationshipCache::relationshipRemoved,
            this, [this](const QString& parent, const QString& child) {
                refreshVirtualNode(parent);
            });
}

VirtualNodeManager::~VirtualNodeManager() {
    saveNodeStates();
}

QStandardItem* VirtualNodeManager::createProjectItem(const QString& sflpFileName) {
    FileNameManager* nameManager = FileNameManager::getInstance();
    QString displayName = nameManager->generateDisplayName(sflpFileName, FileType::Project);
    
    QStandardItem* item = createNodeInternal(sflpFileName, FileType::Project);
    item->setText(displayName);
    item->setToolTip(QString("Project file: %1").arg(sflpFileName));
    
    addNodeToCache(sflpFileName, item);
    
    logNodeOperation("createProjectItem", sflpFileName, true);
    emit nodeCreated(sflpFileName, FileType::Project);
    
    return item;
}

QStandardItem* VirtualNodeManager::createOperationItem(const QString& operationName) {
    FileNameManager* nameManager = FileNameManager::getInstance();
    QString displayName = nameManager->extractOperationDisplayName(operationName);
    
    QStandardItem* item = createNodeInternal(operationName, FileType::Operation);
    item->setText(displayName);
    item->setToolTip(QString("Operation: %1").arg(operationName));
    
    addNodeToCache(operationName, item);
    
    logNodeOperation("createOperationItem", operationName, true);
    emit nodeCreated(operationName, FileType::Operation);
    
    return item;
}

QStandardItem* VirtualNodeManager::createAnalysisItem(const QString& analysisName, AnalysisType type) {
    FileNameManager* nameManager = FileNameManager::getInstance();
    QString displayName = nameManager->extractAnalysisDisplayName(analysisName);
    
    FileType fileType;
    QString typeStr;
    
    switch (type) {
    case AnalysisType::DecayAnalysis:
        fileType = FileType::DecayAnalysis;
        typeStr = "Decay Analysis";
        break;
    case AnalysisType::SpectralAnalysis:
        fileType = FileType::SpectralAnalysis;
        typeStr = "Spectral Analysis";
        break;
    case AnalysisType::FluorescenceAnalysis:
        fileType = FileType::FluorescenceAnalysis;
        typeStr = "Fluorescence Analysis";
        break;
    }
    
    QStandardItem* item = createNodeInternal(analysisName, fileType);
    item->setText(displayName);
    item->setToolTip(QString("%1: %2").arg(typeStr, analysisName));
    
    addNodeToCache(analysisName, item);
    
    logNodeOperation("createAnalysisItem", analysisName, true);
    emit nodeCreated(analysisName, fileType);
    
    return item;
}

QStandardItem* VirtualNodeManager::createSplitItem(const QString& splitName) {
    FileNameManager* nameManager = FileNameManager::getInstance();
    QString displayName = nameManager->generateDisplayName(splitName, FileType::Split);
    
    QStandardItem* item = createNodeInternal(splitName, FileType::Split);
    item->setText(displayName);
    item->setToolTip(QString("Split file: %1").arg(splitName));
    
    addNodeToCache(splitName, item);
    
    logNodeOperation("createSplitItem", splitName, true);
    emit nodeCreated(splitName, FileType::Split);
    
    return item;
}

void VirtualNodeManager::buildVirtualTree(const QString& projectPath) {
    QMutexLocker locker(&m_cacheMutex);
    
    try {
        // 保存当前状态
        saveNodeStates();
        
        // 清空现有树结构
        m_treeModel->clear();
        m_treeModel->setHorizontalHeaderLabels(QStringList() << "File Name" << "File Size");
        m_nodeCache.clear();
        
        // 构建根节点
        QFileInfo projectInfo(projectPath);
        QString projectName = projectInfo.fileName();
        
        QStandardItem* rootItem = new QStandardItem(projectName);
        setNodeData(rootItem, projectPath, FileType::Workspace);
        m_treeModel->appendRow(rootItem);
        
        // 获取所有SFLP文件
        QDir directory(projectPath);
        QStringList sflpFiles = directory.entryList(QStringList() << "*.sflp", QDir::Files);
        
        // 为每个SFLP文件构建树结构
        for (const QString& fileName : sflpFiles) {
            QString filePath = directory.filePath(fileName);
            QStandardItem* fileItem = createProjectItem(filePath);
            rootItem->appendRow(fileItem);
            
            // 添加虚拟子节点
            addVirtualChildren(fileItem, filePath);
        }
        
        // 应用排序
        applySortingToAllNodes();
        
        // 应用过滤
        applyNodeFilter();
        
        // 恢复状态
        restoreNodeStates();
        
        // 展开根节点
        if (m_treeView) {
            m_treeView->expand(m_treeModel->indexFromItem(rootItem));
        }
        
        logNodeOperation("buildVirtualTree", projectPath, true);
        emit treeRebuilt();
        
    } catch (const std::exception& e) {
        qCritical() << "Exception in buildVirtualTree:" << e.what();
        logNodeOperation("buildVirtualTree", projectPath, false);
    }
}

void VirtualNodeManager::addVirtualChildren(QStandardItem* parentItem, const QString& sflpFileName) {
    if (!parentItem || sflpFileName.isEmpty()) {
        return;
    }
    
    try {
        // 获取操作子项
        QStringList operationChildren = m_relationshipCache->getChildren(sflpFileName);
        
        for (const QString& operationName : operationChildren) {
            FileNameManager* nameManager = FileNameManager::getInstance();
            FileType fileType = nameManager->identifyFileType(operationName);
            
            if (fileType == FileType::Operation) {
                QStandardItem* operationItem = createOperationItem(operationName);
                parentItem->appendRow(operationItem);
                
                // 添加分析子项
                QStringList analysisChildren = m_relationshipCache->getChildren(operationName);
                for (const QString& analysisName : analysisChildren) {
                    FileType analysisType = nameManager->identifyFileType(analysisName);
                    
                    if (analysisType == FileType::DecayAnalysis) {
                        QStandardItem* analysisItem = createAnalysisItem(analysisName, AnalysisType::DecayAnalysis);
                        operationItem->appendRow(analysisItem);
                    } else if (analysisType == FileType::SpectralAnalysis) {
                        QStandardItem* analysisItem = createAnalysisItem(analysisName, AnalysisType::SpectralAnalysis);
                        operationItem->appendRow(analysisItem);
                    } else if (analysisType == FileType::FluorescenceAnalysis) {
                        QStandardItem* analysisItem = createAnalysisItem(analysisName, AnalysisType::FluorescenceAnalysis);
                        operationItem->appendRow(analysisItem);
                    }
                }
            }
        }
        
    } catch (const std::exception& e) {
        qWarning() << "Error in addVirtualChildren:" << e.what();
    }
}

QStandardItem* VirtualNodeManager::findNodeByFileName(const QString& fileName) const {
    QMutexLocker locker(&m_cacheMutex);
    
    // 首先从缓存中查找
    if (m_nodeCache.contains(fileName)) {
        return m_nodeCache[fileName];
    }
    
    // 如果缓存中没有，递归搜索
    for (int i = 0; i < m_treeModel->rowCount(); ++i) {
        QStandardItem* rootItem = m_treeModel->item(i);
        if (rootItem) {
            QStandardItem* found = findNodeRecursive(rootItem, fileName);
            if (found) {
                // 添加到缓存
                const_cast<VirtualNodeManager*>(this)->addNodeToCache(fileName, found);
                return found;
            }
        }
    }
    
    return nullptr;
}

bool VirtualNodeManager::selectNode(const QString& fileName) {
    QStandardItem* item = findNodeByFileName(fileName);
    if (!item || !m_treeView) {
        return false;
    }
    
    QModelIndex index = m_treeModel->indexFromItem(item);
    if (index.isValid()) {
        m_treeView->selectionModel()->select(index, QItemSelectionModel::ClearAndSelect);
        m_treeView->scrollTo(index, QAbstractItemView::EnsureVisible);
        
        m_lastSelectedNode = fileName;
        
        FileType fileType = getNodeFileType(item);
        emit nodeSelected(fileName, fileType);
        
        return true;
    }
    
    return false;
}

QString VirtualNodeManager::getSelectedNodeFileName() const {
    if (!m_treeView) {
        return QString();
    }
    
    QModelIndexList selectedIndexes = m_treeView->selectionModel()->selectedIndexes();
    if (selectedIndexes.isEmpty()) {
        return QString();
    }
    
    QModelIndex index = selectedIndexes.first();
    QStandardItem* item = m_treeModel->itemFromIndex(index);
    
    if (item) {
        return getNodeFileName(item);
    }
    
    return QString();
}

void VirtualNodeManager::setNodeFilter(const QList<FileType>& visibleTypes) {
    m_visibleTypes = visibleTypes;
    applyNodeFilter();
    emit filterApplied(visibleTypes);
}

void VirtualNodeManager::clearNodeFilter() {
    m_visibleTypes.clear();
    applyNodeFilter();
    emit filterApplied(QList<FileType>());
}

void VirtualNodeManager::applyNodeFilter() {
    if (m_visibleTypes.isEmpty()) {
        // 显示所有节点
        for (int i = 0; i < m_treeModel->rowCount(); ++i) {
            QStandardItem* rootItem = m_treeModel->item(i);
            if (rootItem) {
                showAllNodes(rootItem);
            }
        }
    } else {
        // 隐藏不在可见类型列表中的节点
        QList<FileType> typesToHide;
        QList<FileType> allTypes = {FileType::Workspace, FileType::Project, FileType::Operation,
                                   FileType::DecayAnalysis, FileType::SpectralAnalysis, 
                                   FileType::FluorescenceAnalysis, FileType::Split};
        
        for (FileType type : allTypes) {
            if (!m_visibleTypes.contains(type)) {
                typesToHide.append(type);
            }
        }
        
        for (int i = 0; i < m_treeModel->rowCount(); ++i) {
            QStandardItem* rootItem = m_treeModel->item(i);
            if (rootItem) {
                hideNodesByType(rootItem, typesToHide);
            }
        }
    }
}

void VirtualNodeManager::sortNodes(QStandardItem* parentItem) {
    if (!parentItem) {
        // 对所有根级项目进行排序
        for (int i = 0; i < m_treeModel->rowCount(); ++i) {
            QStandardItem* rootItem = m_treeModel->item(i);
            if (rootItem) {
                sortNodeChildren(rootItem);
            }
        }
    } else {
        sortNodeChildren(parentItem);
    }
}

void VirtualNodeManager::applySortingToAllNodes() {
    sortNodes(nullptr);
}

void VirtualNodeManager::refreshVirtualNode(const QString& fileName) {
    if (fileName.isEmpty()) {
        return;
    }

    try {
        QMutexLocker locker(&m_cacheMutex);

        // 查找对应的节点
        QStandardItem* item = findNodeByFileName(fileName);
        if (!item) {
            logNodeOperation("refreshVirtualNode", fileName, false);
            return;
        }

        // 获取文件类型
        FileNameManager* nameManager = FileNameManager::getInstance();
        FileType fileType = nameManager->identifyFileType(fileName);

        // 如果是项目文件，刷新其虚拟子节点
        if (fileType == FileType::Project) {
            // 清除现有的子节点
            item->removeRows(0, item->rowCount());

            // 重新添加虚拟子节点
            addVirtualChildren(item, fileName);

            // 应用排序
            sortNodeChildren(item);

            // 应用过滤
            if (!m_visibleTypes.isEmpty()) {
                hideNodesByType(item, getTypesToHide());
            }

            // 恢复展开状态
            if (m_treeView) {
                QModelIndex index = m_treeModel->indexFromItem(item);
                if (index.isValid() && m_nodeExpandStates.value(fileName, false)) {
                    m_treeView->expand(index);
                }
            }
        }

        logNodeOperation("refreshVirtualNode", fileName, true);

    } catch (const std::exception& e) {
        qWarning() << "Exception in refreshVirtualNode:" << e.what();
        logNodeOperation("refreshVirtualNode", fileName, false);
    }
}

void VirtualNodeManager::removeVirtualNode(const QString& fileName) {
    if (fileName.isEmpty()) {
        return;
    }

    try {
        QMutexLocker locker(&m_cacheMutex);

        // 查找对应的节点
        QStandardItem* item = findNodeByFileName(fileName);
        if (!item) {
            logNodeOperation("removeVirtualNode", fileName, false);
            return;
        }

        // 从缓存中移除
        removeNodeFromCache(fileName);

        // 从树模型中移除
        QStandardItem* parentItem = item->parent();
        if (parentItem) {
            parentItem->removeRow(item->row());
        } else {
            // 如果是根级项目，从模型中移除
            m_treeModel->removeRow(item->row());
        }

        logNodeOperation("removeVirtualNode", fileName, true);
        emit nodeRemoved(fileName);

    } catch (const std::exception& e) {
        qWarning() << "Exception in removeVirtualNode:" << e.what();
        logNodeOperation("removeVirtualNode", fileName, false);
    }
}

// 私有方法实现
QStandardItem* VirtualNodeManager::createNodeInternal(const QString& fileName, FileType fileType) {
    QStandardItem* item = new QStandardItem();
    setNodeData(item, fileName, fileType);
    setNodeIcon(item, fileType);
    return item;
}

void VirtualNodeManager::addNodeToCache(const QString& fileName, QStandardItem* item) {
    QMutexLocker locker(&m_cacheMutex);
    m_nodeCache[fileName] = item;
}

void VirtualNodeManager::removeNodeFromCache(const QString& fileName) {
    QMutexLocker locker(&m_cacheMutex);
    m_nodeCache.remove(fileName);
}

QStandardItem* VirtualNodeManager::getNodeFromCache(const QString& fileName) const {
    QMutexLocker locker(&m_cacheMutex);
    return m_nodeCache.value(fileName, nullptr);
}

QStandardItem* VirtualNodeManager::findNodeRecursive(QStandardItem* parent, const QString& fileName) const {
    if (!parent) return nullptr;

    // 检查当前项目
    QString itemFileName = getNodeFileName(parent);
    if (itemFileName == fileName) {
        return parent;
    }

    // 递归搜索子项目
    for (int i = 0; i < parent->rowCount(); ++i) {
        QStandardItem* found = findNodeRecursive(parent->child(i), fileName);
        if (found) {
            return found;
        }
    }

    return nullptr;
}

void VirtualNodeManager::saveNodeStates() {
    m_nodeExpandStates.clear();
    m_lastSelectedNode = getSelectedNodeFileName();

    // 保存展开状态
    for (int i = 0; i < m_treeModel->rowCount(); ++i) {
        QStandardItem* rootItem = m_treeModel->item(i);
        if (rootItem) {
            saveNodeStatesRecursive(rootItem);
        }
    }
}

void VirtualNodeManager::restoreNodeStates() {
    // 恢复展开状态
    for (int i = 0; i < m_treeModel->rowCount(); ++i) {
        QStandardItem* rootItem = m_treeModel->item(i);
        if (rootItem) {
            restoreNodeStatesRecursive(rootItem);
        }
    }

    // 恢复选择
    if (!m_lastSelectedNode.isEmpty()) {
        selectNode(m_lastSelectedNode);
    }
}

void VirtualNodeManager::saveNodeStatesRecursive(QStandardItem* parent) {
    if (!parent || !m_treeView) return;

    QString fileName = getNodeFileName(parent);
    if (!fileName.isEmpty()) {
        QModelIndex index = m_treeModel->indexFromItem(parent);
        if (index.isValid()) {
            m_nodeExpandStates[fileName] = m_treeView->isExpanded(index);
        }
    }

    // 递归处理子项目
    for (int i = 0; i < parent->rowCount(); ++i) {
        saveNodeStatesRecursive(parent->child(i));
    }
}

void VirtualNodeManager::restoreNodeStatesRecursive(QStandardItem* parent) {
    if (!parent || !m_treeView) return;

    QString fileName = getNodeFileName(parent);
    if (!fileName.isEmpty() && m_nodeExpandStates.contains(fileName)) {
        QModelIndex index = m_treeModel->indexFromItem(parent);
        if (index.isValid()) {
            m_treeView->setExpanded(index, m_nodeExpandStates[fileName]);
        }
    }

    // 递归处理子项目
    for (int i = 0; i < parent->rowCount(); ++i) {
        restoreNodeStatesRecursive(parent->child(i));
    }
}

void VirtualNodeManager::hideNodesByType(QStandardItem* parent, const QList<FileType>& typesToHide) {
    if (!parent) return;

    FileType itemType = getNodeFileType(parent);

    // 检查是否需要隐藏此项目
    bool shouldHide = typesToHide.contains(itemType);

    if (shouldHide && m_treeView) {
        QModelIndex index = m_treeModel->indexFromItem(parent);
        if (index.isValid()) {
            m_treeView->setRowHidden(index.row(), index.parent(), true);
        }
    }

    // 递归处理子项目
    for (int i = 0; i < parent->rowCount(); ++i) {
        hideNodesByType(parent->child(i), typesToHide);
    }
}

void VirtualNodeManager::showAllNodes(QStandardItem* parent) {
    if (!parent || !m_treeView) return;

    // 显示项目
    QModelIndex index = m_treeModel->indexFromItem(parent);
    if (index.isValid()) {
        m_treeView->setRowHidden(index.row(), index.parent(), false);
    }

    // 递归处理子项目
    for (int i = 0; i < parent->rowCount(); ++i) {
        showAllNodes(parent->child(i));
    }
}

void VirtualNodeManager::sortNodeChildren(QStandardItem* parentItem) {
    if (!parentItem || parentItem->rowCount() == 0) {
        return;
    }

    // 收集子项目
    QList<QStandardItem*> children;
    for (int i = 0; i < parentItem->rowCount(); ++i) {
        children.append(parentItem->child(i));
    }

    // 自然排序
    std::sort(children.begin(), children.end(),
              [this](QStandardItem* a, QStandardItem* b) {
                  return naturalCompare(a->text(), b->text());
              });

    // 重新排列子项目
    parentItem->removeRows(0, parentItem->rowCount());
    for (QStandardItem* child : children) {
        parentItem->appendRow(child);
        // 递归排序子项目的子项目
        sortNodeChildren(child);
    }
}

bool VirtualNodeManager::naturalCompare(const QString& str1, const QString& str2) const {
    QRegularExpression re("(\\d+)");
    QRegularExpressionMatchIterator it1 = re.globalMatch(str1);
    QRegularExpressionMatchIterator it2 = re.globalMatch(str2);

    int pos1 = 0, pos2 = 0;

    while (it1.hasNext() && it2.hasNext()) {
        QRegularExpressionMatch match1 = it1.next();
        QRegularExpressionMatch match2 = it2.next();

        // 比较数字前的文本部分
        QString prefix1 = str1.mid(pos1, match1.capturedStart() - pos1);
        QString prefix2 = str2.mid(pos2, match2.capturedStart() - pos2);

        int cmp = prefix1.compare(prefix2, Qt::CaseInsensitive);
        if (cmp != 0) {
            return cmp < 0;
        }

        // 比较数字部分
        int num1 = match1.captured(1).toInt();
        int num2 = match2.captured(1).toInt();

        if (num1 != num2) {
            return num1 < num2;
        }

        pos1 = match1.capturedEnd();
        pos2 = match2.capturedEnd();
    }

    // 比较剩余部分
    return str1.mid(pos1).compare(str2.mid(pos2), Qt::CaseInsensitive) < 0;
}

QIcon VirtualNodeManager::getFileTypeIcon(FileType fileType) const {
    return m_iconCache.value(fileType, QIcon(":/icons/default_file.png"));
}

void VirtualNodeManager::loadIcons() {
    m_iconCache[FileType::Workspace] = QIcon(":/icons/workspace.png");
    m_iconCache[FileType::Project] = QIcon(":/icons/project_file.png");
    m_iconCache[FileType::Operation] = QIcon(":/icons/operation.png");
    m_iconCache[FileType::DecayAnalysis] = QIcon(":/icons/decay_analysis.png");
    m_iconCache[FileType::SpectralAnalysis] = QIcon(":/icons/spectral_analysis.png");
    m_iconCache[FileType::FluorescenceAnalysis] = QIcon(":/icons/fluorescence_analysis.png");
    m_iconCache[FileType::Split] = QIcon(":/icons/split_file.png");
}

void VirtualNodeManager::setNodeIcon(QStandardItem* item, FileType fileType) {
    if (item) {
        item->setIcon(getFileTypeIcon(fileType));
    }
}

void VirtualNodeManager::setNodeData(QStandardItem* item, const QString& fileName, FileType fileType) {
    if (item) {
        item->setData(fileName, Qt::UserRole + 1); // 存储完整文件名
        item->setData(static_cast<int>(fileType), Qt::UserRole); // 存储文件类型
    }
}

FileType VirtualNodeManager::getNodeFileType(QStandardItem* item) const {
    if (item) {
        return static_cast<FileType>(item->data(Qt::UserRole).toInt());
    }
    return FileType::Project;
}

QString VirtualNodeManager::getNodeFileName(QStandardItem* item) const {
    if (item) {
        return item->data(Qt::UserRole + 1).toString();
    }
    return QString();
}

void VirtualNodeManager::logNodeOperation(const QString& operation, const QString& fileName, bool success) {
    if (success) {
        qDebug() << "Node operation succeeded:" << operation << "on" << fileName;
    } else {
        qWarning() << "Node operation failed:" << operation << "on" << fileName;
    }
}

QList<FileType> VirtualNodeManager::getTypesToHide() const {
    QList<FileType> typesToHide;
    QList<FileType> allTypes = {FileType::Workspace, FileType::Project, FileType::Operation,
                               FileType::DecayAnalysis, FileType::SpectralAnalysis,
                               FileType::FluorescenceAnalysis, FileType::Split};

    for (FileType type : allTypes) {
        if (!m_visibleTypes.contains(type)) {
            typesToHide.append(type);
        }
    }

    return typesToHide;
}
