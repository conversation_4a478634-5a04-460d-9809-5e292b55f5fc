#include "AcquireTab.h"
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>
#include <QPushButton>
#include <QStackedWidget>
#include <QGroupBox>
#include <QLabel>
#include <QListWidget>
#include <QTreeWidget>
#include <QFrame>
#include <QFileDialog>
#include <QFileSystemModel>
#include <QTreeView>
#include <QLineEdit>
#include <QRadioButton>
#include <QSlider>
#include <QMessageBox>
#include <QFileDialog>
#include <QRubberBand>
#include <QAxObject>
#include <QFileDialog>
#include <QMessageBox>
#include <QMenu>
#include <QFile>
#include "ProjectFileManager.h"
#include "CustomizePlot.h"
#include "ThemeManager.h"
#include "HardwareManager.h"

// 移除了静态变量 plot2MaxY 和 plot3MaxY
// 改为在需要时直接计算最大值
AcquireTab::AcquireTab(QWidget *parent)
    : BaseTab(parent, TabType::Acquire)// 调用基类构造函数
    , m_pTcpMgr(nullptr)
    , m_pDataParser(nullptr)
    , m_iTotalReceivedFrameCnt(0)
    , m_iframeIntervalMSec(100)
    , m_dMinBinWidth(0)
{
    // 创建主布局，设置为左右分栏
    QHBoxLayout *mainLayout = new QHBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0); // 设置布局的边距为0，与Analysis画面保持一致
    mainLayout->setSpacing(0);                  // 设置布局元素之间的间距为0，与Analysis画面保持一致

    // 初始化图表并应用样式
    setupPlots();
    // 注意：工具栏初始化将在创建布局后进行
    // 左侧布局（文件目录和功能按钮切换）
    QVBoxLayout *leftLayout = new QVBoxLayout();
    leftLayout->setContentsMargins(0, 0, 0, 0); // 设置左侧布局的边距为0，与Analysis画面保持一致
    leftLayout->setSpacing(0); // 设置左侧布局的间距为0，与Analysis画面保持一致

    // 顶部按钮区域（文件目录和 acquire 页面切换）
    QHBoxLayout *topButtonLayout = new QHBoxLayout();
    topButtonLayout->setContentsMargins(0, 0, 0, 0); // 设置顶部按钮布局的边距为0
    topButtonLayout->setSpacing(2); // 设置顶部按钮布局的间距为2像素

    QPushButton *openFileButton = new QPushButton("WorkSpace");
    QPushButton *acquireButton = new QPushButton("Acquire");

    // 设置按钮的大小策略
    openFileButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    acquireButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);

    // 不设置固定宽度，使用QSS样式控制

    // 设置按钮的样式类
    openFileButton->setProperty("class", "StandardButton");
    acquireButton->setProperty("class", "StandardButton");

    // 设置按钮为可选中状态
    openFileButton->setCheckable(true);
    acquireButton->setCheckable(true);

    topButtonLayout->addWidget(openFileButton);
    topButtonLayout->addWidget(acquireButton);

    // 不添加拉伸因子，确保按钮占满整个布局

    leftLayout->addLayout(topButtonLayout);

    // 创建文件目录和 acquire 页面切换容器
    QStackedWidget *stackedWidget = new QStackedWidget();
    leftLayout->addWidget(stackedWidget);

    // 页面1：文件目录页面
    // 添加 OpenProjectWidget 到文件目录页面
    openProjectWidget = new OpenProjectWidget();
    stackedWidget->addWidget(openProjectWidget);

    // 连接 OpenProjectWidget 的 treeView 的 clicked 信号到 AcquireTab 的 onFileSelected 槽函数
    connect(openProjectWidget, &OpenProjectWidget::dataRowClicked, this, &AcquireTab::onFileSelected);


    // 页面2：acquire 功能按钮页面
    QWidget *acquirePage = new QWidget();

    QVBoxLayout *acquirePageLayout = new QVBoxLayout(acquirePage);

    // 使用 QTreeWidget 显示可折叠功能按钮
    QTreeWidget *acquireTreeWidget = createaquireTreeWidget();
    acquirePageLayout->addWidget(acquireTreeWidget);
    stackedWidget->addWidget(acquirePage);

    // 将左侧布局添加到主布局
    mainLayout->addLayout(leftLayout, 1); // 左侧占比1

    // 右侧布局（图像显示部分）
    QGridLayout *rightLayout = new QGridLayout();
    rightLayout->setColumnStretch(0, 1); // 设置第一列的伸缩因子为1
    rightLayout->setColumnStretch(1, 1); // 设置第二列的伸缩因子为1
    rightLayout->setRowStretch(0, 1);    // 设置第一行的伸缩因子为1
    rightLayout->setRowStretch(1, 1);    // 设置第二行的伸缩因子为1
    rightLayout->setSpacing(0);          // 设置布局元素之间的间距为0，与Analysis画面保持一致
    rightLayout->setContentsMargins(0, 0, 0, 0); // 设置布局的边距为0

    // 图像工具栏3
    // 注意：所有 plot 的初始化已移至 setupPlots() 函数中


    // Create containers with QHBoxLayout for plots and toolbars
    // 创建四个大小相同的容器，确保所有图表大小一致
    QWidget *plot1Container = new QWidget();
    plot1Container->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding); // 设置大小策略为可扩展
    QHBoxLayout *plot1Layout = new QHBoxLayout(plot1Container);
    plot1Layout->setContentsMargins(0, 0, 0, 0);
    plot1Layout->setSpacing(0);

    QWidget *plot2Container = new QWidget();
    plot2Container->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding); // 设置大小策略为可扩展
    QHBoxLayout *plot2Layout = new QHBoxLayout(plot2Container);
    plot2Layout->setContentsMargins(0, 0, 0, 0);
    plot2Layout->setSpacing(0);

    // 创建 Plot3 容器，包含切换按钮和堆叠窗口
    QWidget *plot3Container = new QWidget();
    plot3Container->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding); // 设置大小策略为可扩展
    // 确保与其他容器大小一致
    plot3Container->setMaximumHeight(plot1Container->maximumHeight());
    QVBoxLayout *plot3ContainerLayout = new QVBoxLayout(plot3Container);
    plot3ContainerLayout->setContentsMargins(0, 0, 0, 0);
    plot3ContainerLayout->setSpacing(0);

    // 创建切换按钮，使用与工具栏相同的样式
    m_pButton1 = new QPushButton("1");
    m_pButton2 = new QPushButton("2");

    // 设置按钮大小
    m_pButton1->setFixedSize(24, 24);
    m_pButton2->setFixedSize(24, 24);

    // 设置按钮为可选中状态
    m_pButton1->setCheckable(true);
    m_pButton2->setCheckable(true);
    m_pButton1->setChecked(true); // 默认选中 Spectral Curve

    // 初始化按钮样式
    updateButtonStyle();

    // 创建按钮的副本，用于 plot3_1
    m_pButton1_copy = new QPushButton("1");
    m_pButton2_copy = new QPushButton("2");

    // 设置副本按钮的大小和样式
    m_pButton1_copy->setFixedSize(24, 24);
    m_pButton2_copy->setFixedSize(24, 24);
    m_pButton1_copy->setCheckable(true);
    m_pButton2_copy->setCheckable(true);
    m_pButton1_copy->setChecked(true);

    // 设置按钮的父级，使其显示在图表内部
    m_pButton1->setParent(m_pPlot3);
    m_pButton2->setParent(m_pPlot3);
    m_pButton1_copy->setParent(plot3_1);
    m_pButton2_copy->setParent(plot3_1);

    // 设置按钮的位置，使其显示在左上角
    m_pButton1->move(10, 10);
    m_pButton2->move(40, 10); // 第二个按钮在第一个按钮的右边
    m_pButton1_copy->move(10, 10);
    m_pButton2_copy->move(40, 10);

    // 设置按钮的Z值，确保其显示在图表上方
    m_pButton1->raise();
    m_pButton2->raise();
    m_pButton1_copy->raise();
    m_pButton2_copy->raise();

    // 连接副本按钮的信号
    connect(m_pButton1_copy, &QPushButton::clicked, this, &AcquireTab::onButton1Clicked);
    connect(m_pButton2_copy, &QPushButton::clicked, this, &AcquireTab::onButton2Clicked);

    // 创建堆叠窗口
    m_pPlot3Stack = new QStackedWidget();
    // 设置固定的大小策略，避免挤压其他图表
    m_pPlot3Stack->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    // 设置最大高度，确保不会过度扩展
    m_pPlot3Stack->setMaximumHeight(m_pPlot1->maximumHeight());

    // 确保 plot3_1 和 m_pPlot3 有相同的尺寸和比例
    // 不使用setFixedSize，而是使用相同的大小策略
    plot3_1->setSizePolicy(m_pPlot3->sizePolicy());
    plot3_1->setMinimumSize(m_pPlot3->minimumSize());
    plot3_1->setMaximumSize(m_pPlot3->maximumSize());

    m_pPlot3Stack->addWidget(m_pPlot3);      // 索引 0: Spectral Curve
    m_pPlot3Stack->addWidget(plot3_1);    // 索引 1: Count Rate

    // 创建 m_pPlot3 和工具栏的水平布局
    QWidget *plotToolbarContainer = new QWidget();
    plotToolbarContainer->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding); // 设置容器的大小策略
    // 设置最大高度，确保与其他图表容器保持一致
    plotToolbarContainer->setMaximumHeight(m_pPlot1->maximumHeight());
    QHBoxLayout *plot3Layout = new QHBoxLayout(plotToolbarContainer);
    plot3Layout->setContentsMargins(0, 0, 0, 0);
    plot3Layout->setSpacing(0);

    QWidget *plot4Container = new QWidget();
    plot4Container->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding); // 设置大小策略为可扩展
    // 确保与其他容器大小一致
    plot4Container->setMaximumHeight(plot1Container->maximumHeight());
    QHBoxLayout *plot4Layout = new QHBoxLayout(plot4Container);
    plot4Layout->setContentsMargins(0, 0, 0, 0);
    plot4Layout->setSpacing(0);

    // Add plots to their containers
    plot1Layout->addWidget(m_pPlot1, 1); // 1 is the stretch factor
    plot2Layout->addWidget(m_pPlot2, 1);
    plot3Layout->addWidget(m_pPlot3Stack, 1); // 使用堆叠窗口，伸缩因子为1
    plot4Layout->addWidget(plot4, 1);

    // Setup plot toolbars
    setupPlotToolbars();

    // 将工具栏添加到布局中，使其与 plot 左右分开显示
    plot1Layout->addWidget(m_pPlot1Toolbar, 0); // 0 是伸缩因子，使工具栏不会被拉伸
    plot2Layout->addWidget(m_pPlot2Toolbar, 0);
    plot3Layout->addWidget(m_pPlot3Toolbar, 0);

    // 为 plot4 创建一个空的占位 widget，保持与其他 plot 相同的布局结构
    QWidget *dummyToolbar = new QWidget();
    // 在setupPlotToolbars之后调用，确保工具栏已经创建并有正确的大小
    dummyToolbar->setFixedWidth(m_pPlot1Toolbar->sizeHint().width());
    dummyToolbar->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Preferred);
    plot4Layout->addWidget(dummyToolbar, 0);

    // 直接将 plot+toolbar 容器添加到 m_pPlot3 主容器
    plot3ContainerLayout->addWidget(plotToolbarContainer, 1); // 1 是伸缩因子，使 plot 区域可以伸缩

    // 设置布局的内容边距
    plot3ContainerLayout->setContentsMargins(0, 0, 0, 0);
    plot3ContainerLayout->setSpacing(0);

    // 连接按钮信号
    connect(m_pButton1, &QPushButton::clicked, this, &AcquireTab::onButton1Clicked);
    connect(m_pButton2, &QPushButton::clicked, this, &AcquireTab::onButton2Clicked);

    // 连接主题变更信号
    connect(ThemeManager::instance(), &ThemeManager::themeChanged, this, &AcquireTab::onThemeChanged);

    // 应用当前主题样式，但不修改图形颜色
    m_pPlot1->applyThemeOnly();
    m_pPlot2->applyThemeOnly();
    m_pPlot3->applyThemeOnly();
    plot3_1->applyThemeOnly(); // 对 Count Rate 也应用主题样式
    plot4->applyThemeOnly();

    // Add containers to the main layout
    rightLayout->addWidget(plot1Container, 0, 0, 1, 1);
    rightLayout->addWidget(plot2Container, 0, 1, 1, 1);
    rightLayout->addWidget(plot3Container, 1, 0, 1, 1);
    rightLayout->addWidget(plot4Container, 1, 1, 1, 1);

    // 将右侧布局添加到主布局
    mainLayout->addLayout(rightLayout, 3); // 右侧占比2

    // 连接按钮和页面切换
    connect(openFileButton, &QPushButton::clicked, [openFileButton, acquireButton, stackedWidget]() {
        stackedWidget->setCurrentIndex(0); // 切换到文件目录页面
        openFileButton->setChecked(true);
        acquireButton->setChecked(false);
    });
    connect(acquireButton, &QPushButton::clicked, [openFileButton, acquireButton, stackedWidget]() {
        stackedWidget->setCurrentIndex(1); // 切换到 acquire 功能按钮页面
        openFileButton->setChecked(false);
        acquireButton->setChecked(true);
    });

    // 默认显示文件目录页面
    stackedWidget->setCurrentIndex(1);
    openFileButton->setChecked(false);
    acquireButton->setChecked(true);

    QHBoxLayout *bottomLayout1 = new QHBoxLayout();
    QHBoxLayout *bottomLayout2 = new QHBoxLayout();

    m_pPreViewPushBtn = new QPushButton("Preview");

    m_pAcquireDataPushButton = new QPushButton("Acquire");
    m_pAbortDataPushButton = new QPushButton("Abort");
    m_pResetBtn = new QPushButton("Reset");

    // 初始化按钮状态
    m_pPreViewPushBtn->setEnabled(false);
    m_pAcquireDataPushButton->setEnabled(false);
    m_pAbortDataPushButton->setEnabled(false);
    m_pResetBtn->setEnabled(false);

    connect(m_pResetBtn, &QPushButton::clicked, this, &AcquireTab::onResetBtnClicked);

    // 设置按钮的样式类
    m_pPreViewPushBtn->setProperty("class", "StandardButton");
    m_pAcquireDataPushButton->setProperty("class", "StandardButton");
    m_pAbortDataPushButton->setProperty("class", "StandardButton");
    m_pResetBtn->setProperty("class", "StandardButton");

    // 连接文件管理器的路径变化信号
    connect(ProjectFileManager::getInstance(), &ProjectFileManager::projectInfoChanged, this, &AcquireTab::updateButtonStates);

    connect(m_pAbortDataPushButton, &QPushButton::clicked, this, &AcquireTab::sendStopCommand);

    connect(m_pPreViewPushBtn, &QPushButton::clicked,
            this, [this]()
            { sendStartCommand(false); });

    connect(m_pAcquireDataPushButton, &QPushButton::clicked,
            this, [this]()
            { sendStartCommand(true); });

    bottomLayout1->addWidget(m_pAcquireDataPushButton);
    bottomLayout1->addWidget(m_pPreViewPushBtn);
    bottomLayout2->addWidget(m_pAbortDataPushButton);
    bottomLayout2->addWidget(m_pResetBtn);

    leftLayout->addLayout(bottomLayout1);
    leftLayout->addLayout(bottomLayout2);

    QHBoxLayout *testbottomLayout = new QHBoxLayout();

    QLabel *pSaveFileLabel = new QLabel();
    pSaveFileLabel->setText("保存文件");

    pLineSaveDataPath = new QLineEdit();

    testbottomLayout->addWidget(pSaveFileLabel);
    testbottomLayout->addWidget(pLineSaveDataPath);

    if (nullptr == m_pDataParser)
    {
        m_pDataParser = new TCPDataParser(this);

        connect(m_pDataParser, &TCPDataParser::ShowDataReady, this, &AcquireTab::updateUIByMeasureData);

        connect(m_pDataParser, &TCPDataParser::showDataIntegrityError, this, &AcquireTab::handleDataIntegrityError);

        // 连接信号到槽函数
        // connect(m_pDataParser, &TCPDataParser::updateTotalDataCount, this, &AcquireTab::updateTotalDataCount);
        // connect(m_pDataParser, &TCPDataParser::receiveRateChanged, this, &AcquireTab::updateReceiveRateDisplay);
        connect(m_pDataParser, &TCPDataParser::stopcondition, this, &AcquireTab::StopRecv);
       // connect(m_pDataParser, &TCPDataParser::maxAccumulatedValueChanged, this, &AcquireTab::updateReceivedFramephotonCnt);
        m_pDataParser->start();

    }
}




AcquireTab::~AcquireTab()
{
    qDebug() << "AcquireTab: Destructor started";

    // Clean up dynamically allocated resources

    // Disconnect signals to prevent callbacks after destruction
    if (m_pTcpMgr && m_pDataParser) {
        disconnect(m_pTcpMgr, &QTCPMgr::RecvByteSignal, m_pDataParser, &TCPDataParser::TCPDataReady);
        qDebug() << "AcquireTab: Disconnected TCP signals";
    }

    // 如果正在运行，发送停止命令
    if (m_pDataParser && m_pDataParser->isRunning()) {
        qDebug() << "AcquireTab: Sending stop command before cleanup";
        sendStopCommand();
    }

    // Delete data parser if we own it
    if (m_pDataParser) {
        qDebug() << "AcquireTab: Cleaning up data parser";

        // Set running flag to false and wake up the thread
        m_pDataParser->resetData(); // Reset data to clean state
        m_pDataParser->quit();      // Request thread termination

        // 使用较短的超时时间等待线程结束
        bool threadFinished = m_pDataParser->wait(500);
        if (!threadFinished) {
            qWarning() << "AcquireTab: Data parser thread did not finish in time, terminating";
            m_pDataParser->terminate();
            m_pDataParser->wait(100);
        }

        delete m_pDataParser;
        m_pDataParser = nullptr;
        qDebug() << "AcquireTab: Data parser cleaned up";
    }

    // Clean up plot resources
    removeCrosshairs(); // Clean up any crosshair items
    qDebug() << "AcquireTab: Crosshairs removed";

    // Note: QObjects with parents (like plots, buttons, etc.) will be automatically deleted
    // when the parent (this widget) is deleted, so we don't need to delete them manually


    m_totalCounts.clear();
    m_timePoints.clear();

    qDebug() << "AcquireTab: Destructor completed";
}

void AcquireTab::setTCPMgr(QTCPMgr *pmgr)
{
    m_pTcpMgr = pmgr;
    if (nullptr != m_pTcpMgr && nullptr != m_pDataParser)
        connect(m_pTcpMgr, &QTCPMgr::RecvByteSignal, m_pDataParser, &TCPDataParser::TCPDataReady);
    qInfo() << "AcquireTab::setTCPMgr out ,m_pTCPMgr = " << m_pTcpMgr;
}

// 辅助函数：创建功能按钮树

QTreeWidget *AcquireTab::createaquireTreeWidget()
{
    QVBoxLayout *layMain = new QVBoxLayout;
    QTreeWidget *treewidget = new QTreeWidget;
    treewidget->setHeaderHidden(true);
    layMain->addWidget(treewidget); // 创建树状容器放入垂直布局中

    // 设置单色仪参数布局
    QStringList items_para1;
    items_para1 << QString("Monochromator Parameters");
    QTreeWidgetItem *cellParent = new QTreeWidgetItem(items_para1);
    QTreeWidgetItem *cellChild = new QTreeWidgetItem();
    cellParent->addChild(cellChild);
    treewidget->insertTopLevelItem(treewidget->topLevelItemCount(), cellParent); // 设置树状容器的子节

    m_pMonoChromatorAcquire = new MonochromatorAcquire();

    treewidget->setItemWidget(cellChild, 0, m_pMonoChromatorAcquire); // 单色仪参数界面设置完成

    for (int i = 0; i < 1; i++)
    {
        QStringList items;
        items << QString("TDC Parameters");
        QTreeWidgetItem *cellParent = new QTreeWidgetItem(items);
        QTreeWidgetItem *cellChild = new QTreeWidgetItem(items);
        cellParent->addChild(cellChild);
        treewidget->insertTopLevelItem(treewidget->topLevelItemCount(), cellParent);

        QWidget *widget1 = new QWidget();
        QVBoxLayout *VLayout1 = new QVBoxLayout(widget1);

        // Resolution 部分
        QHBoxLayout *ResolutionLayout = new QHBoxLayout();
        QLabel *labResolution = new QLabel("Resolution");

        m_pResolutionSlider = new QSlider(Qt::Horizontal);

        m_pResolutionSlider->setEnabled(false); // 初始状态下禁用滑块，等待TDC配置获取值后启用

        m_pResolutionSpinBox = new QDoubleSpinBox();
        m_pResolutionSpinBox->setMaximum(10000000000);
        m_pResolutionSpinBox->setMinimumWidth(80);
        m_pResolutionSpinBox->setEnabled(false); // 初始状态下禁用，等待TDC配置获取值后启用

        m_pResolutionUnitLabel = new QLabel("ps"); // 单位标签


        ResolutionLayout->addWidget(labResolution);
        ResolutionLayout->addWidget(m_pResolutionSlider);
        ResolutionLayout->addWidget(m_pResolutionSpinBox);
        ResolutionLayout->addWidget(m_pResolutionUnitLabel);

        // Time Window 部分
        QHBoxLayout *TimeWindowLayout = new QHBoxLayout();
        QLabel *labTimeWindow = new QLabel("Time Window");
        editTimeWindowValue = new QLineEdit(); // 显示 Time Window 值
        editTimeWindowValue->setReadOnly(true);

        double resolution = m_dMinBinWidth * (1 << 0);//默认第0档
        double initialTimeWindowValue = resolution * m_iTimeChannelNumber;
        QString formattedInitialValue = QString::number(initialTimeWindowValue, 'g', 2);
        editTimeWindowValue->setText(formattedInitialValue);

        labTimeWindowUnit = new QLabel("us"); // 单位标签


        TimeWindowLayout->addWidget(labTimeWindow);
        TimeWindowLayout->addWidget(editTimeWindowValue);
        TimeWindowLayout->addWidget(labTimeWindowUnit);

        VLayout1->addLayout(ResolutionLayout);
        VLayout1->addLayout(TimeWindowLayout);

        treewidget->setItemWidget(cellChild, 0, widget1);
    }

    for (int i = 0; i < 1; i++)
    {
        QStringList items;
        items << QString("Termination Condition");
        QTreeWidgetItem *cellParent = new QTreeWidgetItem(items);
        QTreeWidgetItem *cellChild = new QTreeWidgetItem(items);

        cellParent->addChild(cellChild);
        treewidget->insertTopLevelItem(treewidget->topLevelItemCount(), cellParent);
        QWidget *widget1 = new QWidget();
        QVBoxLayout *VLayout1 = new QVBoxLayout(widget1);
        QHBoxLayout *Layout1 = new QHBoxLayout();
        m_pStoptimeCheckBox = new QCheckBox("Max Measure Span");
        m_pStoptimeUnitBox = new QComboBox;
        m_pStoptimeUnitBox->addItem("s");
        m_pStoptimeUnitBox->addItem("min");
        m_pStoptimeUnitBox->addItem("h");
        m_pStoptimeLineEdit = new QLineEdit;

        Layout1->addWidget(m_pStoptimeCheckBox);
        Layout1->addWidget(m_pStoptimeLineEdit);
        Layout1->addWidget(m_pStoptimeUnitBox);

        connect(m_pStoptimeCheckBox, &QCheckBox::stateChanged, this, &AcquireTab::handleStopTimeChange);
        connect(m_pStoptimeLineEdit, &QLineEdit::textChanged, this, &AcquireTab::handleStopTimeChange);
        connect(m_pStoptimeUnitBox, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &AcquireTab::handleStopTimeChange);

        m_pStopPhotonCheckBox = new QCheckBox("Max Photon Count");
        m_pStopPhotonCntLineEdit = new QLineEdit;

        connect(m_pStopPhotonCheckBox, &QCheckBox::stateChanged, this, &AcquireTab::handleStopPhotonChange);
        connect(m_pStopPhotonCntLineEdit, &QLineEdit::textChanged, this, &AcquireTab::handleStopPhotonChange);


        QHBoxLayout *Layout2 = new QHBoxLayout();
        Layout2->addWidget(m_pStopPhotonCheckBox);
        Layout2->addWidget(m_pStopPhotonCntLineEdit);
        VLayout1->addLayout(Layout1);
        VLayout1->addLayout(Layout2);
        treewidget->setItemWidget(cellChild, 0, widget1);
    }
    return treewidget;
}


void AcquireTab::handleStopTimeChange()
{
    bool ok;
    bool enabled = m_pStoptimeCheckBox->isChecked();
    int iStopTime = m_pStoptimeLineEdit->text().toInt(&ok);
    QString currentTimeUnit = m_pStoptimeUnitBox->currentText();

    if (enabled)
    {
        if (currentTimeUnit == "s")
        {
            iStopTime = iStopTime * 1000;
        }
        else if (currentTimeUnit == "min")
        {
            iStopTime = iStopTime * 60 * 1000;
        }
        else if (currentTimeUnit == "h")
        {
            iStopTime = iStopTime * 60 * 60 * 1000;
        }
        int iStopFrameCnt = iStopTime/m_iframeIntervalMSec;

        if(m_pDataParser)
        {
            m_pDataParser->setStopTimeCondition(iStopFrameCnt);
        }
    }
    else
    {
        if(m_pDataParser)
        {
            m_pDataParser->setStopTimeCondition(-1);
        }
    }

}

void AcquireTab::handleStopPhotonChange()
{
    bool ok;
    bool enabled = m_pStopPhotonCheckBox->isChecked();


    if(enabled && m_pDataParser)
    {
        int iStopPhotonCnt = m_pStopPhotonCntLineEdit->text().toInt(&ok);
        m_pDataParser->setStopPhotonCondition(iStopPhotonCnt);
    }
    else if(m_pDataParser)
    {
        m_pDataParser->setStopPhotonCondition(-1);
    }

}


void AcquireTab::updateUIByMeasureData(const std::vector<std::vector<int>> &AccumulateData,const std::vector<std::vector<int> > &Data, quint32 pulseCount)
{
    fillQCustomPlot(AccumulateData, Data,false, pulseCount);
    // updateAbortResetButtons();
}


void AcquireTab::updateAbortResetButtons()
{
    bool isRunning = m_pDataParser && m_pDataParser->isRunning();
    m_pAbortDataPushButton->setEnabled(isRunning);
    m_pResetBtn->setEnabled(isRunning);
}

void AcquireTab::updateButtonStates()
{
    bool hasValidPath = !ProjectFileManager::getInstance()->getFilePath().isEmpty();
    m_pPreViewPushBtn->setEnabled(hasValidPath);

    // 增加空指针检查
    if (!m_pAcquireDataPushButton)
    {
        qCritical() << "m_pAcquireDataPushButton is not initialized!";
        return;
    }
    m_pAcquireDataPushButton->setEnabled(hasValidPath);
    clearPlots();
}



void AcquireTab::setSerialStatus(LocalSerialStatus *serialStatus)
{
    this->m_pSerialStatus = serialStatus;
    if (nullptr != m_pSerialStatus)
    {
        m_pSerialPort = m_pSerialStatus->getSerialPort();
    }
}

void AcquireTab::sendStartCommand(bool isAcquire)
{
    m_iTotalReceivedFrameCnt = 0;
    if(m_pDataParser)
    {
        if(m_pDataParser->m_paused)
        {
            m_pDataParser->resume();
        }
    }

    // 根据硬件模式选择发送方式
    // if (HardwareManager::instance()->isRealHardware())
    // {
        // 真实硬件模式 - 上位机发送5501aa，FPGA开始发送数据
        if (m_pSerialPort == nullptr && nullptr != m_pSerialStatus)
            m_pSerialPort = m_pSerialStatus->initSerialPort();

        if (nullptr != m_pSerialPort && m_pSerialPort->isOpen())
        {
            if (isAcquire)
            {
                ProjectFileManager::getInstance()->startMeasurement();
                // 设置文件路径到 pLineSaveDataPath
                pLineSaveDataPath->setText(ProjectFileManager::getInstance()->getSavingHandler()->getFilePath());
            }
            else
            {
                ProjectFileManager::getInstance()->resetMeasurement();
            }

            //clearPlots();
            // Tcp queue clear

            // 发送串口命令
            QByteArray array;
            array.resize(3);
            array[0] = 0x55;
            array[1] = 0x01;
            array[2] = 0xaa;
            m_pSerialPort->write(array);


            clearPlots();
            // Tcp queue clear
            m_pDataParser->resetData();
            // 发送Start命令给DummyTcpServer
            m_pTcpMgr->SendInfo("START");

            m_pAbortDataPushButton->setEnabled(true);
            m_pResetBtn->setEnabled(true);


        }
        else
        {
            qDebug() << "数据发送失败 - 串口未打开";
        }
    //}
    // else
    // {
    //     // 模拟模式
    //     if (isAcquire)
    //     {
    //         ProjectFileManager::getInstance()->startMeasurement();
    //         // 设置文件路径到 pLineSaveDataPath
    //         pLineSaveDataPath->setText(ProjectFileManager::getInstance()->getSavingHandler()->getFilePath());
    //     }
    //     else
    //     {
    //         ProjectFileManager::getInstance()->resetMeasurement();
    //     }

    //     clearPlots();
    //     // Tcp queue clear
    //     m_pDataParser->resetData();

    //     // 发送Start命令给DummyTcpServer
    //     m_pTcpMgr->SendInfo("START");

    //     m_pAbortDataPushButton->setEnabled(true);
    //     m_pResetBtn->setEnabled(true);
    // }
}

void AcquireTab::sendStopCommand()
{
    // 根据硬件模式选择发送方式
    // if (HardwareManager::instance()->isRealHardware())
    // {
        // 真实硬件模式 - 上位机发送5502aa，FPGA停止发送数据
        if (m_pSerialPort == nullptr && nullptr != m_pSerialStatus)
            m_pSerialPort = m_pSerialStatus->initSerialPort();

        if (nullptr != m_pSerialPort && m_pSerialPort->isOpen())
        {
            QByteArray array;
            array.resize(3);
            array[0] = 0x55;
            array[1] = 0x02;
            array[2] = 0xaa;
            m_pSerialPort->write(array);

            // 获取当前处理程序的处理程序实例
            MeasureDataHandler *savingHandler = ProjectFileManager::getInstance()->getSavingHandler();
            if (savingHandler)
            {
                savingHandler->setDataArrayTotal(m_pDataParser->getAccumulatedData());
                ProjectFileManager::getInstance()->stopMeasurement();
            }
            m_pAbortDataPushButton->setEnabled(false);
            m_pResetBtn->setEnabled(false);
            m_pDataParser->resetData();
        }
        else
        {
            qDebug() << "数据发送停止失败 - 串口未打开";
        }
    //}
    // else
    // {
    //     // 模拟模式
    //     // 发送Stop命令给DummyTcpServer
    //     m_pTcpMgr->SendInfo("STOP");

    //     // 获取当前处理程序的处理程序实例
    //     MeasureDataHandler *savingHandler = ProjectFileManager::getInstance()->getSavingHandler();
    //     if (savingHandler)
    //     {
    //         savingHandler->setDataArrayTotal(m_pDataParser->getAccumulatedData());
    //         ProjectFileManager::getInstance()->stopMeasurement();
    //     }
    //     m_pAbortDataPushButton->setEnabled(false);
    //     m_pResetBtn->setEnabled(false);
    // }

}
void AcquireTab::setSpectralChannelNumber(int iSpectralChannelNumber) {
    m_iSpectralChannelNumber = iSpectralChannelNumber;
    if(m_pDataParser)
        m_pDataParser->setSpectralChannelNumber(m_iSpectralChannelNumber);
    return;
}

void AcquireTab::setTimeChannelNumber(int iTimeChannelNumber) {
    m_iTimeChannelNumber = iTimeChannelNumber;
    if(m_pDataParser)
        m_pDataParser->setTimeChannelNumber(m_iTimeChannelNumber);
    return;
}



void AcquireTab::onAdjustableLevelsChanged(int levels ,double TimeResolution, int timeChannels) {
    // 当TDC配置中的adjustableLevels值通过串口获取后，启用Resolution滑块

    if (levels > 0 && m_pResolutionSlider && m_pResolutionSpinBox)
    {
        qDebug() << "Received adjustableLevels:" << levels;

        // 更新滑块和数值框的最大值为从TDC获取的levels值
        m_pResolutionSlider->setRange(1, levels);


        // 启用滑块和数值框
        m_pResolutionSlider->setEnabled(true);
        //m_pResolutionSpinBox->setEnabled(true);


    }

    TimeChannelNums = timeChannels;
    connect(m_pResolutionSlider, &QSlider::valueChanged, [this, TimeResolution](int value)
            {
        // 计算当前分辨率（滑块值 * minTimeResolution）
        double resolution = (1<< (value-1)) * TimeResolution / 1000;

        // 更新显示的分辨率值和单位
        updateResolutionDisplay(resolution);

        // 设置 spinBox 的值为滑块值（阻止信号循环）
        // m_pResolutionSpinBox->blockSignals(true);
        // m_pResolutionSpinBox->setValue(value);
        // m_pResolutionSpinBox->blockSignals(false);

        // 将值的变化传给specFlim
        emit resolutionChanged(value);
    });

    // // 连接 spinBox 的值变化信号
    // connect(m_pResolutionSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), [this, TimeResolution](int value)
    //         {
    //     // 计算当前分辨率
    //     double resolution = value * TimeResolution / 1000;

    //     // 设置滑块值（阻止信号循环）
    //     m_pResolutionSlider->blockSignals(true);
    //     m_pResolutionSlider->setValue(value);
    //     m_pResolutionSlider->blockSignals(false);

    //     // 更新显示的分辨率值和单位
    //     updateResolutionDisplay(resolution);

    //     // 将值的变化传给specFlim
    //     emit resolutionChanged(value);
    // });

}

void AcquireTab::onAcqMonoReady(SignalMonoInfo info)
{
    qDebug() << "AcquireTab::onAcqMonoReady in, info.iMonoID = " << info.iMonoID;

    memcpy(&m_MonoInfo, &info, sizeof(SignalMonoInfo));
    if (m_pMonoChromatorAcquire)
        m_pMonoChromatorAcquire->setMonoInfo(m_MonoInfo);
}

void AcquireTab::setMonoInfo(SignalMonoInfo info)
{
    memcpy(&m_MonoInfo, &info, sizeof(SignalMonoInfo));
    qInfo() << " AcquireTab::setMonoInfo, id = " << info.iMonoID << "id--- = " << m_MonoInfo.iMonoID;

    if (m_pMonoChromatorAcquire)
        m_pMonoChromatorAcquire->setMonoInfo(m_MonoInfo);
}




void AcquireTab::StopRecv()
{

    sendStopCommand();

    return;
}

void AcquireTab::SetFrameIntervalTime(int iFrameIntervalTimeMS)
{
    m_iframeIntervalMSec = iFrameIntervalTimeMS;
}


// Plot3 切换按钮槽函数
void AcquireTab::onButton1Clicked()
{
    // 切换到 Spectral Curve
    m_pPlot3Stack->setCurrentIndex(0);

    // 更新所有按钮状态
    m_pButton1->setChecked(true);
    m_pButton2->setChecked(false);
    m_pButton1_copy->setChecked(true);
    m_pButton2_copy->setChecked(false);
}

void AcquireTab::onButton2Clicked()
{
    // 切换到 Count Rate
    m_pPlot3Stack->setCurrentIndex(1);

    // 更新所有按钮状态
    m_pButton1->setChecked(false);
    m_pButton2->setChecked(true);
    m_pButton1_copy->setChecked(false);
    m_pButton2_copy->setChecked(true);
}

// 响应主题变化
void AcquireTab::onThemeChanged(const QString& theme)
{
    // 更新按钮样式以匹配新主题
    updateButtonStyle();

    // 应用新样式到所有按钮
    m_pButton1->setStyleSheet(buttonStyle);
    m_pButton2->setStyleSheet(buttonStyle);
    m_pButton1_copy->setStyleSheet(buttonStyle);
    m_pButton2_copy->setStyleSheet(buttonStyle);
}

// 更新按钮样式以匹配当前主题
void AcquireTab::updateButtonStyle()
{
    // 获取当前主题
    QString theme = ThemeManager::instance()->getCurrentTheme();

    if (theme == "Dark") {
        // 暗色主题按钮样式 - 更浅色底，浅色字，进一步增强对比度
        buttonStyle = "QPushButton { border: 1px solid #888888; background-color: #666669; color: #FFFFFF; border-radius: 3px; font-weight: bold; }"
                      "QPushButton:hover { background-color: #77777A; border: 1px solid #AAAAAA; }"
                      "QPushButton:pressed { background-color: #555558; }"
                      "QPushButton:checked { background-color: #77777F; color: #FFDD00; border: 1px solid #FFDD00; font-weight: bold; }";
    } else {
        // 亮色主题按钮样式 - 浅色底，深色字
        buttonStyle = "QPushButton { border: 1px solid #AAAAAA; background-color: #F0F0F0; color: #333333; border-radius: 3px; font-weight: bold; }"
                      "QPushButton:hover { background-color: #FFFFFF; border: 1px solid #999999; }"
                      "QPushButton:pressed { background-color: #E0E0E0; }"
                      "QPushButton:checked { background-color: #E0E0E0; color: #0066CC; border: 1px solid #0066CC; font-weight: bold; }";
    }
}

void AcquireTab::fillQCustomPlot(const std::vector<std::vector<int> > &AccumulateData,  const std::vector<std::vector<int> > &Data,bool suppressReplot, quint32 pulseCount)
{
    try {
        // 首先调用基类的 fillQCustomPlot 方法处理 m_pPlot1, m_pPlot2, m_pPlot3
        BaseTab::fillQCustomPlot(AccumulateData, suppressReplot);

        m_iTotalReceivedFrameCnt++;

        // 检查数据有效性
        if (AccumulateData.empty() || AccumulateData[0].empty()) {
            return; // 基类已经输出了警告
        }

        // 获取数据维度
        int xA = AccumulateData.size();
        int yA = AccumulateData[0].size();

        // 为 plot3_1 (Count Rate) 准备数据
        prepareCountRateData(Data, xA, yA, static_cast<int>(pulseCount));
        prepareTotalCountsData(Data);

        if (!suppressReplot) {
            // 更新 plot3_1 显示计数率数据
            updateCountRatePlot();
            //更新plot4，显示总计数率
            updateTotalCountsPlot();
        }


        // 注意：mutableData 和 m_fillQCustomPlotData 已经在基类的 fillQCustomPlot 中设置
    } catch (const std::exception& e) {
        qCritical() << "Exception in AcquireTab::fillQCustomPlot: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in AcquireTab::fillQCustomPlot()";
    }
}

// 准备计数率数据
void AcquireTab::prepareCountRateData(const std::vector<std::vector<int>> &Data, int xA, int yA, int pulseCount)
{
    // 初始化波长和计数率数组
    m_wavelengths.resize(xA);
    m_countRates.resize(xA);

    if (pulseCount <= 0) {
        // 如果maxPhotonCount无效，直接返回
        return;
    }

    // 计算每个波长的计数率
    for (int row = 0; row < xA; ++row) {

        m_wavelengths[row] = row; // 波长通道

        double PerRowtotalCount = 0;
        for (int col = 0; col < yA; ++col) {
            PerRowtotalCount += Data[row][col];
        }

        // 使用帧头中的脉冲计数作为最大光子数进行百分比计算
        m_countRates[row] = (PerRowtotalCount / pulseCount*100);
    }

}

// 更新计数率图表
void AcquireTab::updateCountRatePlot()
{
    // 清除现有图形
    plot3_1->clearGraphs();
    plot3_1->clearItems();

    // 检查数据是否有效
    if (m_wavelengths.isEmpty() || m_countRates.isEmpty()) {
        return;
    }

    // 创建新图形
    plot3_1->addGraph();
    plot3_1->graph(0)->setData(m_wavelengths, m_countRates);
    plot3_1->graph(0)->setLineStyle(QCPGraph::lsLine);

    // 设置图形样式
    QPen pen;
    pen.setColor(QColor(0, 200, 0)); // 使用绿色区分于光谱曲线
    pen.setWidth(2);
    plot3_1->graph(0)->setPen(pen);

    // 添加填充区域
    QCPGraph* graph = plot3_1->graph(0);
    QCPItemRect* fillRect = new QCPItemRect(plot3_1);
    fillRect->topLeft->setType(QCPItemPosition::ptPlotCoords);
    fillRect->bottomRight->setType(QCPItemPosition::ptPlotCoords);
    fillRect->topLeft->setCoords(0, 100);
    fillRect->bottomRight->setCoords(m_wavelengths.size(), 0);
    fillRect->setBrush(QBrush(QColor(0, 200, 0, 50))); // 半透明绿色
    fillRect->setPen(Qt::NoPen);

    // 确保标题元素存在
    if (!m_title3_1) {
        plot3_1->plotLayout()->insertRow(0);
        m_title3_1 = new QCPTextElement(plot3_1, " Count Rate");
        plot3_1->plotLayout()->addElement(0, 0, m_title3_1);
    }

    // 设置轴标签和范围
    plot3_1->xAxis->setLabel("Wavelength (nm)");
    plot3_1->yAxis->setLabel("Count Rate (%)");
    plot3_1->xAxis->setRange(0, m_wavelengths.size());
    plot3_1->yAxis->setRange(0, 100); // 计数率为百分比，范围为 0-100%

    // 只应用主题样式，不修改图形颜色
    plot3_1->applyThemeOnly();
    // 重绘
    plot3_1->replot();

}

void AcquireTab::prepareTotalCountsData(const std::vector<std::vector<int>> &Data)
{

    // 计算当前帧的所有光子数总和
    double totalPhotonCount = 0;

    for (const auto &row : Data) {
        totalPhotonCount += std::accumulate(row.begin(), row.end(), 0.0);
    }


    // 使用固定大小的环形缓冲区存储数据
    constexpr int MAX_DATA_POINTS = 20;

    // 如果是第一帧或者需要重置
    if (m_totalCounts.isEmpty()) {
        m_totalCounts.reserve(MAX_DATA_POINTS);
        m_timePoints.reserve(MAX_DATA_POINTS);
    }

    // 添加新数据点
    m_totalCounts.append(totalPhotonCount);

    // 计算时间点（当前帧数 * 帧间隔时间）
    double timePoint = m_iTotalReceivedFrameCnt * m_iframeIntervalMSec / 1000.0; // 转换为秒
    m_timePoints.append(timePoint);

    // 如果超过最大点数，移除最早的数据点
    if (m_totalCounts.size() > MAX_DATA_POINTS) {
        m_totalCounts.removeFirst();
        m_timePoints.removeFirst();
    }


}




void AcquireTab::updateTotalCountsPlot()
{
    // 清除旧图形
    plot4->clearGraphs();
    plot4->clearItems();

    if (m_timePoints.isEmpty() || m_totalCounts.isEmpty() ||
        m_timePoints.size() != m_totalCounts.size()) {
        return;
    }

    // 创建新图形
    plot4->addGraph();
    plot4->graph(0)->setData(m_timePoints, m_totalCounts);
    plot4->graph(0)->setLineStyle(QCPGraph::lsNone);  // 隐藏连接线
    plot4->graph(0)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssCircle, 5));

    QPen pen;
    pen.setColor(QColor(255, 0, 0)); // 红色曲线
    pen.setWidth(2);
    plot4->graph(0)->setPen(pen);

    // 添加标题
    if (!m_title4) {
        plot4->plotLayout()->insertRow(0);
        m_title4 = new QCPTextElement(plot4, " Total Counts");
        plot4->plotLayout()->addElement(0, 0, m_title4);
    }

    // 设置坐标轴标签
    plot4->xAxis->setLabel("Time (s)");
    plot4->yAxis->setLabel("Total Photon Count");

    // 动态调整范围
    plot4->xAxis->setRange(m_timePoints.first(), m_timePoints.last() + 0.1);
    plot4->yAxis->setRangeLower(0); // 下限为0
    plot4->yAxis->rescale();       // 自动调整上限

    // 应用主题样式
    plot4->applyThemeOnly();

    // 重绘
    plot4->replot();
}


void AcquireTab::onResetBtnClicked()
{
    pLineSaveDataPath->setText("");

    clearPlots();
    ProjectFileManager::getInstance()->resetMeasurement();
}

// 清除所有图表的内容
void AcquireTab::clearPlots()
{
    try {
        // 首先调用基类的 clearPlots 方法清除 m_pPlot1, m_pPlot2, m_pPlot3
        BaseTab::clearPlots();

        // 清除 AcquireTab 特有的图表
        CustomizePlot* plotsArray[] = {plot3_1, plot4};

        // 清除图表内容，但不重绘
        for (auto& plot : plotsArray) {
            if (plot) {
                // 设置更新暂停，避免多次重绘
                plot->setUpdatesEnabled(false);
                plot->clearPlottables();
                plot->clearItems(); // 清除所有项目，包括文本项
                plot->rescaleAxes();
                // 重新启用更新
                plot->setUpdatesEnabled(true);
            }
        }

        // 最后只重绘一次
        for (auto& plot : plotsArray) {
            if (plot) {
                plot->replot();
            }
        }

        m_totalCounts.clear();
        m_timePoints.clear();

    } catch (const std::exception& e) {
        qCritical() << "Exception in AcquireTab::clearPlots: " << e.what();
        // 异常已经处理，不再抛出
    }
}

void AcquireTab::reset()
{
    try {
        // 首先调用基类的 reset 方法重置 m_pPlot1, m_pPlot2, m_pPlot3
        BaseTab::reset();

        // 创建一个临时数组来存储 AcquireTab 特有的图表
        CustomizePlot* plotsArray[] = {plot3_1, plot4};

        // 重置 AcquireTab 特有图表的坐标轴类型和范围
        for (auto& plot : plotsArray) {
            if (plot) {
                // 重置坐标轴类型为线性
                plot->xAxis->setScaleType(QCPAxis::stLinear);
                plot->yAxis->setScaleType(QCPAxis::stLinear);

                // 重置坐标轴刻度器
                plot->xAxis->setTicker(QSharedPointer<QCPAxisTicker>(new QCPAxisTicker));
                plot->yAxis->setTicker(QSharedPointer<QCPAxisTicker>(new QCPAxisTicker));

                // 重置坐标轴范围并重绘
                plot->rescaleAxes();
                plot->replot();
            }
        }

        // 确保 AcquireTab 特有的标题元素不为空

        if (!m_title3_1 && plot3_1) {
            plot3_1->plotLayout()->insertRow(0);
            m_title3_1 = new QCPTextElement(plot3_1, " Count Rate");
            plot3_1->plotLayout()->addElement(0, 0, m_title3_1);
            plot3_1->replot();
        }

        if (!m_title4 && plot4) {
            plot4->plotLayout()->insertRow(0);
            m_title4 = new QCPTextElement(plot4, " Total Counts");
            plot4->plotLayout()->addElement(0, 0, m_title4);
            plot4->replot();
        }

        m_totalCounts.clear();
        m_timePoints.clear();


    } catch (const std::exception& e) {
        qCritical() << "Exception in reset: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in reset()";
    }
}


// 更新光谱曲线图表 (m_pPlot3)
void AcquireTab::updateSpectralCurvePlot(int y)
{
    try {
        // 首先调用基类的 updateSpectralCurvePlot 方法处理 m_pPlot3
        BaseTab::updateSpectralCurvePlot(y);

        // 以下是 AcquireTab 特有的功能，处理 plot3_1 (Count Rate)

        // 更新计数率图表
        // 在鼠标移动时也显示计数率数据
        updateCountRatePlot();

        // 在计数率图表上显示当前选中的时间通道位置
        if (plot3_1 && !m_wavelengths.isEmpty() && y >= 0 && y < static_cast<int>(mutableData[0].size()))
        {
            // 添加垂直线指示当前选中的时间通道
            QCPItemStraightLine* vLine = new QCPItemStraightLine(plot3_1);
            vLine->setPen(QPen(Qt::red, 1, Qt::DashLine));
            vLine->point1->setCoords(y, 0);
            vLine->point2->setCoords(y, 100);

            // 重绘计数率图表
            plot3_1->replot();
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in AcquireTab::updateSpectralCurvePlot: " << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in AcquireTab::updateSpectralCurvePlot()";
    }
}

void AcquireTab::setupPlots()
{
    try {
        // 首先调用基类的 setupPlots 方法初始化 m_pPlot1, m_pPlot2, m_pPlot3
        BaseTab::setupPlots();

        // 初始化 AcquireTab 特有的图表
        if (plot3_1) { delete plot3_1; plot3_1 = nullptr; }
        if (plot4) { delete plot4; plot4 = nullptr; }

        // 初始化 AcquireTab 特有的图表
        plot3_1 = new CustomizePlot(this);
        plot4 = new CustomizePlot(this);

        // 设置图表类型
        plot3_1->setPlotStyle(PlotStyle::SpectralCurve);
        plot4->setPlotStyle(PlotStyle::TotalCounts);

        if (!plot3_1 || !plot4) {
            qCritical() << "Failed to create plot3_1 or plot4";
            throw std::runtime_error("Plot creation failed");
        }

        // 设置 AcquireTab 特有图表的通用属性
        CustomizePlot* plots[] = {plot3_1, plot4};
        for (CustomizePlot* plot : plots) {
            // 设置大小策略为强制扩展，使图表充分利用可用空间
            plot->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
            // 设置最小尺寸，确保图表不会被压缩得太小
            plot->setMinimumSize(300, 200);
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in AcquireTab::setupPlots: " << e.what();
        // 清理任何可能已创建的对象
        if (plot3_1) { delete plot3_1; plot3_1 = nullptr; }
        if (plot4) { delete plot4; plot4 = nullptr; }
        throw; // 重新抛出异常
    }

    try {
        // 设置 plot3_1 (计数率)
        if (plot3_1) {
            plot3_1->plotLayout()->insertRow(0);
            m_title3_1 = new QCPTextElement(plot3_1, " Count Rate");
            plot3_1->plotLayout()->addElement(0, 0, m_title3_1);
            plot3_1->xAxis->setLabel("Wavelength (nm)");
            plot3_1->yAxis->setLabel("Count Rate (%)");
        }

        // 设置 plot4 (总计数)
        if (plot4) {
            plot4->plotLayout()->insertRow(0);
            m_title4 = new QCPTextElement(plot4, " Total Counts");
            plot4->plotLayout()->addElement(0, 0, m_title4);
            plot4->xAxis->setLabel("X Axis");
            plot4->yAxis->setLabel("Y Axis");
        }

        // 在创建时应用当前主题样式，但不修改图形颜色
        if (plot3_1) plot3_1->applyCurrentThemeStyle(); // 初始化时使用applyCurrentThemeStyle是安全的，因为还没有图形
        if (plot4) plot4->applyCurrentThemeStyle(); // 初始化时使用applyCurrentThemeStyle是安全的，因为还没有图形
    } catch (const std::exception& e) {
        qCritical() << "Exception in setupPlots (plot configuration): " << e.what();
        throw; // 重新抛出异常
    }

    try {
        // 注意: m_pPlot3Stack 和切换按钮已在构造函数中创建
        // 这里只需要确保堆叠小部件显示正确的默认图表
        if (m_pPlot3Stack) {
            m_pPlot3Stack->setCurrentIndex(0);  // 默认显示 Spectral Curve
        }

        // 确保按钮状态正确
        if (m_pButton1 && m_pButton2) {
            m_pButton1->setChecked(true);
            m_pButton2->setChecked(false);
        }

        // 确保 plot3_1 和 m_pPlot3 有相同的尺寸和比例
        if (m_pPlot3 && plot3_1) {
            plot3_1->setSizePolicy(m_pPlot3->sizePolicy());
            plot3_1->setMinimumSize(m_pPlot3->minimumSize());
            plot3_1->setMaximumSize(m_pPlot3->maximumSize());
        }

        // 隐藏所有图表的关闭按钮
        QString hideCloseButtonStyle = "QTabBar::close-button { image: none; width: 0; height: 0; }";
        // 创建一个临时数组来存储所有图表
        CustomizePlot* plotsArray[] = {plot3_1, plot4};
        for (CustomizePlot* plot : plotsArray) {
            if (plot) {
                plot->setStyleSheet(hideCloseButtonStyle);
                plot->rescaleAxes();
            }
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in setupPlots (final configuration): " << e.what();
        // 异常已经处理，不再抛出
    }
}

void AcquireTab::onPanClicked(bool enabled)
{
    // 首先调用基类的 onPanClicked 方法处理 m_pPlot1, m_pPlot2, m_pPlot3
    BaseTab::onPanClicked(enabled);

    // 处理 AcquireTab 特有的 plot4
    if (plot4) {
        if (enabled) {
            plot4->setInteraction(QCP::iRangeDrag, true);
        } else {
            plot4->setInteraction(QCP::iRangeDrag, false);
        }
    }
}


bool AcquireTab::eventFilter(QObject *watched, QEvent *event)
{
    // 处理窗口大小变化事件
    if (event->type() == QEvent::Resize)
    {
        // 如果是 m_pPlot3/plot3_1 或其容器的大小变化
        if (watched == m_pPlot3 || watched == m_pPlot3->parentWidget() ||
            watched == plot3_1 || watched == plot3_1->parentWidget() ||
            watched == m_pPlot3Stack)
        {
            // 重新设置工具栏位置
            if (m_pPlot3Toolbar)
            {
                QWidget *currentPlot = m_pPlot3Stack->currentWidget();
                if (currentPlot)
                {
                    m_pPlot3Toolbar->move(currentPlot->width() - m_pPlot3Toolbar->width(), currentPlot->height() - m_pPlot3Toolbar->height());
                }
            }
        }
    }

    // 调用基类的 eventFilter 处理 m_pPlot1 和 m_pPlot2 的大小变化
    return BaseTab::eventFilter(watched, event);
}

void AcquireTab::setMinBinWidth(double dMinBinWidth)
{
    m_dMinBinWidth = dMinBinWidth;
}

// 更新分辨率显示和单位
void AcquireTab::updateResolutionDisplay(double resolution) {


    // 根据分辨率值自动调整单位
    QString unit = "ps";

    double TimWindows = resolution*TimeChannelNums;
    if (TimWindows <= 1000000)
    {
        editTimeWindowValue->setText(QString::number(TimWindows/1000)) ;
        labTimeWindowUnit->setText("ns");

    }
    else if (TimWindows >= 1000000 &&TimWindows <= 1000000000)
    {
        editTimeWindowValue->setText(QString::number(TimWindows/1000000)) ;
        labTimeWindowUnit->setText("us");
    }
    else if (TimWindows >= 1000000000 &&TimWindows <= 1000000000000)
    {
        editTimeWindowValue->setText(QString::number(TimWindows/1000000000)) ;
        labTimeWindowUnit->setText("ms");
    }
    else if (TimWindows >= 1000000000000 &&TimWindows <= 1000000000000000)
    {
        editTimeWindowValue->setText(QString::number(TimWindows/1000000000000)) ;
        labTimeWindowUnit->setText("s");
    }


    if (resolution <= 1000) {

        m_pResolutionSpinBox->setValue(resolution);
        unit = "ps";
    } else if (resolution>=1000 &&resolution <= 1000000) {
        // 如果大于等于1000 ps，转换为 ns
        //resolution /= 1000;
        m_pResolutionSpinBox->setValue(resolution/1000);
        unit = "us";
    }
    else if(resolution>=1000000 &&resolution <= 1000000000)
    {
       //resolution /= 1000000;
       m_pResolutionSpinBox->setValue(resolution/100000);
       unit = "ns";
    }
    qDebug()<<"分辨率為"<<resolution<<"ns";

    // 更新单位标签
    m_pResolutionUnitLabel->setText(unit);
}


void AcquireTab::handleDataIntegrityError()
{
    // 使用QMessageBox提醒用户数据不完整
    QMessageBox::warning(this, tr("数据错误"),
                         tr("检测到数据丢失！接收的数据帧不完整，可能影响成像质量。\n请检查网络连接或设备传输状态。"),
                         QMessageBox::Ok);

    // 可选：更新UI状态
    //m_pPreViewPushBtn->setEnabled(false);
    //m_pAcquireDataPushButton->setEnabled(false);

    // 可选：记录日志
    qInfo() << "Data integrity check failed: middle data size is less than expected";

    // 可以添加其他UI反馈，比如：
    // - 更新状态栏消息
    // - 改变指示灯颜色
    // - 记录日志等
}
