#undef min
#undef max

#include "TCPDataParser.h"
#include <QDebug>
#include <QDateTime> // 添加头文件用于获取时间戳
#include <QFuture>
#include <QtConcurrent/QtConcurrentMap>
#include <QMessageBox>
#include "ProjectFileManager.h"
#include "MeasureDataHandler.h"
#include <algorithm>

TCPDataParser::TCPDataParser(QObject *parent)
    : QThread(parent)
    , m_paused(false)
    , stopcount(0)
    , m_stopCountMutex()
    , m_fullData()
    , m_TCPQueue()
    , m_uiTotalDataLength(0)
    , m_running(true)
    , m_startTime(QDateTime::currentDateTime())
    , m_accumulatedData(128, std::vector<int>(4096, 0))
    , m_mutex()
    , m_waitCondition()
    , m_pauseCondition()
    , m_maxAccumulatedValue(0)
    , m_stopPhotonCondition(-1)
    , m_stopTimeCondition(-1)
    , m_iTotalFrameCnt(0)
    , m_bNeedStop(false)
    , m_iTimeChannelNumber(4096)
    , m_iSpectralChannelNumber(128)
    , m_mCurrentState(ParserState::SearchingHeader)
    , m_uiPulseCount(0)
{

}

TCPDataParser::~TCPDataParser()
{
    // 设置停止标志
    m_running = false;

    // 唤醒所有等待的条件变量，确保线程不会卡在等待状态
    {
        QMutexLocker locker(&m_mutex);
        m_waitCondition.wakeAll();
        m_pauseCondition.wakeAll();
    }

    // 等待线程结束
    if (isRunning()) {
        qDebug() << "TCPDataParser: Waiting for thread to finish...";
        // 使用较短的超时时间，避免程序退出时长时间等待
        bool finished = wait(1000);
        if (!finished) {
            qWarning() << "TCPDataParser: Thread did not finish in time, terminating...";
            terminate();
            wait();
        }
    }

    qDebug() << "TCPDataParser: Thread successfully stopped";
}

void TCPDataParser::Stop_condition(int &data)
{
    QMutexLocker locker(&m_stopCountMutex);
    stopcount = data;
    qDebug()<<stopcount;
}

void TCPDataParser::TCPDataReady(QByteArray data)
{
    QMutexLocker locker(&m_mutex);
    m_TCPQueue.enqueue(data);
    int dataSize = data.size(); // 计算数据长度
    //qInfo()<<"-----------dataSize = "<<dataSize;
    data.clear(); // 清空数据
    m_uiTotalDataLength += dataSize; // 更新总数据长度
    m_waitCondition.wakeOne();

    //locker.unlock();  ????
    // qDebug()<<"TCPDataParser::TCPDataReady data.size() = "<< data.size();
    // emit dataReady();
    QDateTime currentTime = QDateTime::currentDateTime();
    qint64 elapsedMs = m_startTime.msecsTo(currentTime);
    double rate = elapsedMs > 0 ? (m_uiTotalDataLength * 1000.0) / elapsedMs : 0;
    emit receiveRateChanged(rate);
}


void TCPDataParser::run()
{
    while (m_running)
    {
        QByteArray tcpData;

        {
            QMutexLocker locker(&m_mutex);

            // 检查暂停状态
            while (m_paused && m_running)
            {
                m_pauseCondition.wait(&m_mutex);
            }

            if (!m_running) break;

            while (m_TCPQueue.isEmpty() && m_running && !m_paused)
            {
                m_waitCondition.wait(&m_mutex);
            }

            if (!m_running || m_paused) continue;

            if (!m_TCPQueue.isEmpty())
            {
                tcpData = m_TCPQueue.dequeue();
            }
        }

        if (!tcpData.isEmpty())
        {
            parseData(tcpData);
        }
    }
}



void TCPDataParser::setStopTimeCondition(int stopTimeCondition)
{
    QMutexLocker locker(&m_mutex);
    m_stopTimeCondition = stopTimeCondition;
    qInfo()<<"TCPDataParser::setStopTimeCondition, m_stopTimeCondition"<<m_stopTimeCondition;
    m_bNeedStop = (m_stopTimeCondition != -1);
}

void TCPDataParser::setStopPhotonCondition(int stopPhoton)
{
    QMutexLocker locker(&m_mutex);
    m_stopPhotonCondition = stopPhoton;
    m_bNeedStop = (m_stopPhotonCondition != -1);

}

void TCPDataParser::parseData(QByteArray tcpData)
{
    // 如果缓冲区过大，清空以释放内存
    /*if (m_fullData.size() > MAX_BUFFER_SIZE) {
        qWarning() << "--------Buffer overflow detected, resetting buffer.";
        m_fullData.clear();
        m_mCurrentState = ParserState::SearchingHeader;
    }*/

    m_fullData.append(tcpData);
    int requiredSize = getFrameSize();

    // 根据当前状态处理数据
    switch (m_mCurrentState) {
    case ParserState::SearchingHeader:
        qInfo()<<"TCPDataParser::parseData, SearchingHeader";
        if (m_fullData.size() >= m_iFrameHeadLen) {
            int frameStartPos = findValidFrameHeaderPosition(m_fullData);
            qInfo()<<"TCPDataParser::parseData, frameStartPos = "<<frameStartPos;

            if (frameStartPos >= 0) {
                if (frameStartPos > 0) {
                    m_fullData.remove(0, frameStartPos); // 跳过无效数据
                }

                // 进入接收剩余数据状态
                m_mCurrentState = ParserState::ReceivingPayload;
                break;
            } else {
                // 未找到有效帧头 —— 删除大部分无用数据，加速后续查找
                int bytesToRemove = m_fullData.size() - m_iFrameHeadLen+1;
                if (bytesToRemove > 0) {
                    qWarning() << "No valid header found, removing" << bytesToRemove << "bytes to accelerate search.";
                    m_fullData.remove(0, bytesToRemove);
                } else {
                    // 缓冲区太小，无法滑动，仅删除一字节继续搜索
                    m_fullData.remove(0, 1);
                }
            }
        }
        break;

    case ParserState::ReceivingPayload:

        if (m_fullData.size() >= requiredSize) {
            QByteArray frameData = m_fullData.left(requiredSize);

            m_fullData.remove(0, requiredSize);
            processData(frameData);
            m_mCurrentState = ParserState::SearchingHeader;
    }
    break;

    default:
        qWarning() << "Unknown parser state";
        m_mCurrentState = ParserState::SearchingHeader;
        break;
    }

}

void TCPDataParser::processData(const QByteArray& frameData)
{
    qInfo()<<"TCPDataParser::processData in.";
    // 验证帧头
    //quint16 expectedFrameNumber = m_iTotalFrameCnt;
    quint16 receivedFrameNumber = (static_cast<quint8>(frameData[6]) << 8) |
                                  static_cast<quint8>(frameData[7]);

    m_uiPulseCount = getPulseCountFromHeader(frameData);

    QDateTime startTime2 = QDateTime::currentDateTime();

    // 提取中间数据
    int middleDataSize = getMiddleDataSize();
    QByteArray middleData = frameData.mid(16, middleDataSize);

    //quint32 pulseCount = (static_cast<quint8>(frameData[8]) << 24) |
    //                     (static_cast<quint8>(frameData[9]) << 16) |
    //                     (static_cast<quint8>(frameData[10]) << 8) |
    //                     static_cast<quint8>(frameData[11]);

    auto twoDimensionalData = dataTo2D(middleData, m_uiPulseCount);
    ProjectFileManager::getInstance()->createAndAppendDataFrame(receivedFrameNumber, m_uiPulseCount, twoDimensionalData);

    m_iTotalFrameCnt++;

    // 检查停止条件
    if (m_bNeedStop)
    {
        bool stopByPhoton = (m_stopPhotonCondition != -1 && m_maxAccumulatedValue >= m_stopPhotonCondition);
        bool stopByTime = (m_stopTimeCondition != -1 && m_iTotalFrameCnt >= m_stopTimeCondition);

        if (stopByPhoton || stopByTime)
        {
            qInfo() << "TCPDataParser::parseData, Stopping data parsing due to conditions met.";
            pause();

            emit stopcondition();
            m_maxAccumulatedValue = 0;
            m_iTotalFrameCnt = 0;
            m_TCPQueue.clear();

            return; // 提前返回，避免后续处理
        }
    }

    QDateTime endTime = QDateTime::currentDateTime();
    int msecs = startTime2.msecsTo(endTime);
    qInfo() << "处理一帧数据耗时: " << msecs << " 毫秒";
}

std::vector<std::vector<int>> TCPDataParser::dataTo2D(QByteArray arr, quint32 pulseCount)
{
    QDateTime startTime = QDateTime::currentDateTime();
    if(arr.size() % 2 != 0)
    {
        qDebug() << "TCPDataParser::dataTo2D error: Array size is not even.";
        return std::vector<std::vector<int>>();
    }

    std::vector<std::vector<int>> data2D(m_iSpectralChannelNumber, std::vector<int>(m_iTimeChannelNumber, 0)); // 初始化128x4096的二维数组

    // 使用指针来优化内存访问
    const quint8* dataPtr = reinterpret_cast<const quint8*>(arr.constData());
    int index = 0;

    for (int row = 0; row < m_iSpectralChannelNumber; ++row) {
        for (int col = 0; col < m_iTimeChannelNumber; ++col) {
            if (index >= arr.size() - 1) {
                break;
            }
            // 手动计算十进制值
            quint16 decimalValue = (static_cast<quint16>(dataPtr[index]) << 8) |
                                   static_cast<quint16>(dataPtr[index + 1]);
            data2D[row][col] = decimalValue;
            index += 2;
        }
    }

    // 累加 data2D 到 m_accumulatedData
    int currentMaxValue = 0; // 当前帧的最大累加值
    for (size_t i = 0; i < data2D.size(); ++i) {
        for (size_t j = 0; j < data2D[i].size(); ++j) {
            m_accumulatedData[i][j] += data2D[i][j];
            if(-1!=m_stopPhotonCondition)
                currentMaxValue = std::max(currentMaxValue, m_accumulatedData[i][j]); // 更新当前帧的最大累加值
        }
    }

    // 更新全局最大累加值
    if(-1!=m_stopPhotonCondition)
        m_maxAccumulatedValue = std::max(m_maxAccumulatedValue, currentMaxValue);
    //emit maxAccumulatedValueChanged(m_maxAccumulatedValue); // 发送最大累加值变化信号
    //qInfo()<<"TCPDataParser::dataTo2D, maxAccumulatedValueChanged = "<<m_maxAccumulatedValue;

    if(pulseCount>0)
    {
        m_maxAccumulatedValue = std::max(m_maxAccumulatedValue, static_cast<int>(pulseCount));
    }

    //QDateTime endTime = QDateTime::currentDateTime();
    //int msecs = startTime.msecsTo(endTime);
    //qDebug() << "dataTo2D耗时: " << msecs << " 毫秒";

    emit ShowDataReady(m_accumulatedData, data2D,pulseCount);
    return data2D;
}

void TCPDataParser::resetData() {
    QMutexLocker locker(&m_mutex);
    m_TCPQueue.clear();
    m_accumulatedData = std::vector<std::vector<int>>(128, std::vector<int>(4096, 0)); // 重新初始化 m_accumulatedData
    m_uiTotalDataLength = 0;
    m_fullData.clear();
    m_startTime = QDateTime::currentDateTime();
    m_maxAccumulatedValue = 0;
    m_iTotalFrameCnt = 0;
}

bool TCPDataParser::validateFrameHeader(const QByteArray& data)const
{
    // 检查数据长度是否足够包含完整帧头
    if (data.size() < m_iFrameHeadLen) {
        qWarning() << "Frame data too short to contain header";
        return false;
    }

    // 验证前6个字节是否为0x55
    for (int i = 0; i < 6; ++i) {
        if (static_cast<quint8>(data[i]) != 0x55) {
            qWarning() << QString("Header byte %1 is not 0x55").arg(i);
            return false;
        }
    }

    // 验证帧号（第7-8字节）
    //quint16 expectedFrameNumber = static_cast<quint16>(m_iTotalFrameCnt);
    //quint16 receivedFrameNumber = (static_cast<quint8>(data[6]) << 8) | static_cast<quint8>(data[7]);

   // if (receivedFrameNumber != expectedFrameNumber) {
   //     qWarning() << QString("Unexpected frame number: expected %1, got %2")
   //     .arg(expectedFrameNumber).arg(receivedFrameNumber);
   //     return false;
   // }

    // 验证脉冲计数（第9-12字节）
   // m_uiPulseCount = (static_cast<quint8>(data[8]) << 24) |
    //                     (static_cast<quint8>(data[9]) << 16) |
    //                     (static_cast<quint8>(data[10]) << 8) |
    //                      static_cast<quint8>(data[11]);



    // 验证最后4个字节是否为0x55
    for (int i = 12; i < 16; ++i) {
        if (static_cast<quint8>(data[i]) != 0x55) {
            qWarning() << QString("Footer byte %1 is not 0x55").arg(i - 12);
            return false;
        }
    }

    return true;
}

bool TCPDataParser::validateFrameFooter(const QByteArray& data, quint16 expectedFrameNumber)const
{
    // 检查数据长度是否足够包含帧尾
    if (data.size() < getFrameSize()) {
        qWarning() << "Frame data too short to contain footer";
        return false;
    }

    // 定位帧尾位置（位于数据块末尾）
    int footerOffset = data.size() - m_iFrameRearLen; // 最后8个字节是帧尾

    // 验证最后6个字节是否为0xAA
    for (int i = 0; i < 6; ++i) {
        if (static_cast<quint8>(data[footerOffset + i]) != 0xAA) {
            qWarning() << QString("Footer byte %1 is not 0xAA").arg(i);
            return false;
        }
    }

    // 验证最后2个字节的帧号
    quint16 receivedFrameNumber = (static_cast<quint8>(data[footerOffset + 6]) << 8) |
                                  static_cast<quint8>(data[footerOffset + 7]);

    return receivedFrameNumber == expectedFrameNumber;
}

quint32 TCPDataParser::getPulseCountFromHeader(const QByteArray& data) const {
    return (static_cast<quint8>(data[8]) << 24) |
           (static_cast<quint8>(data[9]) << 16) |
           (static_cast<quint8>(data[10]) << 8) |
           static_cast<quint8>(data[11]);
}


int TCPDataParser::findValidFrameHeaderPosition(const QByteArray& data) const
{
    qInfo() << "TCPDataParser::findValidFrameHeaderPosition in.";

    if (data.size() < m_iFrameHeadLen) {
        return -1; // 数据不足一个帧头长度
    }

    int i = 0;
    while ((i = data.indexOf(static_cast<char>(0x55), i)) != -1 &&
           i <= data.size() - m_iFrameHeadLen) {

        QByteArray possibleHeader = data.mid(i, m_iFrameHeadLen);
        if (validateFrameHeader(possibleHeader)) {
            qWarning() << "Found valid frame header at position" << i;
            return i;
        }

        ++i; // 继续查找下一个 0x55
    }


    return -1; // 未找到有效帧头
}

int TCPDataParser::getMiddleDataSize() const
{
    return m_iSpectralChannelNumber * 2 * m_iTimeChannelNumber;
}

int TCPDataParser::getFrameSize() const
{
    return getMiddleDataSize() + m_iFrameHeadLen + m_iFrameRearLen;
}



void TCPDataParser::pause()
{
    QMutexLocker locker(&m_mutex);
    m_paused = true;
}

void TCPDataParser::resume()
{
    QMutexLocker locker(&m_mutex);
    m_paused = false;
    m_pauseCondition.wakeAll();

    // 新增：恢复运行时初始化所有状态
    m_TCPQueue.clear();
    m_fullData.clear();
    m_uiTotalDataLength = 0;
    m_accumulatedData = std::vector<std::vector<int>>(128, std::vector<int>(4096, 0));
    m_maxAccumulatedValue = 0; // 重置最大累加值
    m_iTotalFrameCnt = 0; // 重置帧计数
    m_startTime = QDateTime::currentDateTime(); // 重新初始化开始时间
    m_mCurrentState = ParserState::SearchingHeader;
}

void TCPDataParser::clearBuffers()
{
    QMutexLocker locker(&m_mutex);
    m_TCPQueue.clear();
    m_fullData.clear();
    m_uiTotalDataLength = 0;
    // 清空累加数据以避免第二次预览时数据累加异常
    m_accumulatedData = std::vector<std::vector<int>>(128, std::vector<int>(4096, 0));
    m_maxAccumulatedValue = 0; // 重置最大累加值
}

quint16 TCPDataParser::getCurrentFrameCount() const
{
    //QMutexLocker locker(&m_mutex); // 使用互斥锁确保线程安全
    return m_iTotalFrameCnt;       // 返回当前帧数
}

void TCPDataParser::setSpectralChannelNumber(int iSpectralChnNum)
{
    //时间通道数*2*光谱通道数 = 总的数据数据段长度
    if (iSpectralChnNum != m_iSpectralChannelNumber) {
        resetAccumulatedData(iSpectralChnNum, m_iTimeChannelNumber);
        m_iSpectralChannelNumber = 128;//iSpectralChnNum;
    }
    return;
}

void TCPDataParser::setTimeChannelNumber(int iTimeChlNum)
{
    //时间通道数
    if (iTimeChlNum != m_iTimeChannelNumber) {
        resetAccumulatedData(m_iSpectralChannelNumber, iTimeChlNum);
        m_iTimeChannelNumber = 4096;//iTimeChlNum;
    }

    return;
}

void TCPDataParser::resetAccumulatedData(int spectralChannels, int timeChannels)
{
    QMutexLocker locker(&m_mutex); // 确保线程安全

    m_iSpectralChannelNumber = spectralChannels;
    m_iTimeChannelNumber = timeChannels;

    m_accumulatedData = std::vector<std::vector<int>>(spectralChannels,
                                                      std::vector<int>(timeChannels, 0));
}
