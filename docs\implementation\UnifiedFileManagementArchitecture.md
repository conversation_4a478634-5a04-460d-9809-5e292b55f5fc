# SpecFLIM统一文件管理系统架构设计文档

## 1. 概述

### 1.1 设计目标
本文档定义了SpecFLIM统一文件管理系统的详细架构设计，基于Save_ver2.md和ProcessFileSaveRequirements.md的业务需求，实现生产级的文件管理解决方案。

### 1.2 核心设计原则
- **统一接口**: 通过ProjectFileManager提供统一的文件操作接口
- **虚拟层级**: 支持操作数据和拟合数据作为虚拟子项的层级显示
- **Qt压缩**: 使用qCompress/qUncompress进行数据压缩存储
- **关系缓存**: 通过FileRelationshipCache管理文件关系和操作序列
- **生产质量**: 完整的错误处理、参数验证和内存管理

### 1.3 文件格式规范
- **.sflp文件**: 原始数据文件，二进制兼容.sfd格式
- **操作数据**: 存储为.sflp文件内的压缩数据段，无独立文件
- **拟合数据**: 存储为.sflp文件内的压缩数据段，_fit后缀标识
- **工作区文件**: .sflw格式，包含项目元数据

## 2. 核心架构组件

### 2.1 FileNameManager类设计

```cpp
class FileNameManager {
public:
    static FileNameManager* getInstance();
    
    // 项目前缀管理
    QString getProjectPrefix() const;
    void setProjectPrefix(const QString& prefix);
    bool validateProjectPrefix(const QString& prefix) const;
    
    // 工作区文件命名
    QString generateWorkspaceFileName() const;
    QString generateWorkspaceFileName(const QDate& date) const;
    
    // 工程文件命名
    QString generateProjectFileName(int sequence) const;
    QString generateProjectFileName(const QDate& date, int sequence) const;
    
    // 操作序列命名
    QString generateOperationName(const QString& baseFileName, 
                                 const OperationSequence& sequence) const;
    QString generateFittingName(const QString& baseFileName, 
                               int fittingSequence) const;
    
    // Split文件命名
    QString generateSplitFileName(const QString& originalFileName, 
                                 int splitSequence) const;
    
    // 显示名称生成
    QString generateDisplayName(const QString& fileName, 
                               FileType fileType) const;
    
    // 序列号管理
    int getNextProjectSequence() const;
    int getNextOperationSequence(const QString& fileName, 
                                OperationType operationType) const;
    int getNextFittingSequence(const QString& baseFileName) const;
    
    // 配置集成
    void loadConfigFromAppConfig();
    void saveConfigToAppConfig();
    
private:
    QString m_projectPrefix;
    QMap<QString, OperationCounters> m_operationCounters;
    AppConfig* m_appConfig;
    
    // 内部辅助方法
    QString formatDateString(const QDate& date) const;
    bool isValidFileName(const QString& fileName) const;
    OperationCounters parseExistingOperations(const QString& fileName) const;
};

// 支持数据结构
enum class OperationType {
    Alignment,  // al
    Crop,       // cr
    AddCurve    // ad
};

enum class FileType {
    Workspace,      // .sflw
    Project,        // .sflp
    Operation,      // 虚拟子项
    Fitting,        // _fit虚拟子项
    Split          // _split.sflp
};

struct OperationCounters {
    int alignmentCounter = 0;
    int cropCounter = 0;
    int addCurveCounter = 0;
    
    int getCounter(OperationType type) const;
    void incrementCounter(OperationType type);
    void setCounter(OperationType type, int value);
};

struct OperationSequence {
    QList<QPair<OperationType, int>> operations;
    
    QString toString() const;
    static OperationSequence fromString(const QString& sequence);
    void appendOperation(OperationType type, int sequence);
    bool isEmpty() const;
};
```

### 2.2 FileRelationshipCache类设计

```cpp
class FileRelationshipCache {
public:
    static FileRelationshipCache* getInstance();
    
    // 文件关系管理
    void addFileRelation(const QString& parentFile, const QString& childFile);
    void removeFileRelation(const QString& parentFile, const QString& childFile);
    QStringList getChildFiles(const QString& parentFile) const;
    QString getParentFile(const QString& childFile) const;
    
    // 虚拟节点管理
    QString generateVirtualNodeName(const QString& baseFileName, 
                                   const OperationSequence& sequence) const;
    void registerVirtualNode(const QString& virtualName, 
                            const QString& parentFile,
                            const QByteArray& nodeData);
    QByteArray getVirtualNodeData(const QString& virtualName) const;
    bool isVirtualNode(const QString& fileName) const;
    
    // 操作计数器管理
    void updateOperationCounter(const QString& fileName, 
                               OperationType type, 
                               int newValue);
    OperationCounters getOperationCounters(const QString& fileName) const;
    void initializeCountersFromFile(const QString& fileName);
    
    // 缓存管理
    void refreshCache();
    void clearCache();
    void saveToMetadata(const QString& projectPath);
    void loadFromMetadata(const QString& projectPath);
    
    // 文件类型识别
    FileType getFileType(const QString& fileName) const;
    bool isOperationFile(const QString& fileName) const;
    bool isFittingFile(const QString& fileName) const;
    
private:
    // 双向映射表实现O(1)查询
    QMap<QString, QString> m_parentMap;        // 子节点→父节点
    QMap<QString, QStringList> m_childrenMap; // 父节点→子节点列表
    
    // 虚拟节点数据存储
    QMap<QString, QByteArray> m_virtualNodeData;
    QMap<QString, QString> m_virtualNodeParents;
    
    // 操作计数器缓存
    QMap<QString, OperationCounters> m_operationCounters;
    
    // 内部辅助方法
    void updateMappingTables(const QString& parent, const QString& child);
    void removeMappingTables(const QString& parent, const QString& child);
    QString generateUniqueVirtualId() const;
    void parseFileOperations(const QString& fileName);
};
```

### 2.3 ProjectFileManager增强设计

```cpp
class ProjectFileManager : public QObject {
    Q_OBJECT
    
public:
    static ProjectFileManager* getInstance();
    
    // 统一文件操作接口
    bool saveOperationData(const QString& fileName, 
                          const OperationSequence& sequence,
                          const PlotDataCollection& plotData,
                          const OperationMetadata& metadata);
    
    bool saveFittingData(const QString& baseFileName,
                        int fittingSequence,
                        const FittingResults& results,
                        const FittingParameters& parameters);
    
    PlotDataCollection loadOperationData(const QString& operationName);
    FittingResults loadFittingData(const QString& fittingName);
    
    // 文件关系管理
    QStringList getChildFiles(const QString& parentFile) const;
    QString getParentFile(const QString& childFile) const;
    FileType getFileType(const QString& fileName) const;
    
    // 新格式文件支持
    bool saveToSflpFormat(const QString& fileName, 
                         const QByteArray& data,
                         const QString& dataSegmentName);
    QByteArray loadFromSflpFormat(const QString& fileName,
                                 const QString& dataSegmentName);
    
    // 压缩数据管理
    QByteArray compressData(const QByteArray& rawData) const;
    QByteArray decompressData(const QByteArray& compressedData) const;
    
    // 元数据管理
    void updateFileMetadata(const QString& fileName, 
                           const FileMetadata& metadata);
    FileMetadata getFileMetadata(const QString& fileName) const;
    
    // 错误处理和验证
    bool validateFileName(const QString& fileName) const;
    bool validateOperationSequence(const OperationSequence& sequence) const;
    QString getLastError() const;
    
signals:
    void operationDataSaved(const QString& operationName);
    void fittingDataSaved(const QString& fittingName);
    void fileRelationshipChanged();
    void errorOccurred(const QString& error);
    
private:
    FileRelationshipCache* m_relationCache;
    FileNameManager* m_nameManager;
    QString m_lastError;
    
    // 内部实现方法
    bool writeDataSegmentToFile(const QString& fileName,
                               const QString& segmentName,
                               const QByteArray& data);
    QByteArray readDataSegmentFromFile(const QString& fileName,
                                      const QString& segmentName);
    bool validateFileIntegrity(const QString& fileName) const;
    void updateOperationIndex(const QString& fileName,
                             const QString& operationName,
                             qint64 dataOffset,
                             qint64 dataSize);
};
```

## 3. 数据结构定义

### 3.1 核心数据结构

```cpp
// 绘图数据集合
struct PlotDataCollection {
    FluorescenceMapData fluorescenceMap;
    QVector<DecayCurveData> decayCurves;      // 仅已添加的曲线
    QVector<SpectralCurveData> spectralCurves; // 仅已添加的曲线
    TotalCountsData totalCounts;

    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
    bool isValid() const;
    qint64 calculateSize() const;
};

// 操作元数据
struct OperationMetadata {
    OperationType operationType;
    QDateTime timestamp;
    QString operationName;
    OperationSequence sequence;
    QMap<QString, QVariant> parameters;
    QString description;

    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
};

// 拟合结果数据
struct FittingResults {
    QString fittingType;        // "DecayAnalysis" 或 "SpectralAnalysis"
    QMap<QString, double> parameters;
    QVector<QPointF> fittedCurve;
    QVector<QPointF> residualCurve;
    QString analysisReport;
    double chiSquared;
    double rSquared;

    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
};

// 拟合参数
struct FittingParameters {
    QString modelType;
    QMap<QString, double> initialValues;
    QMap<QString, QPair<double, double>> limits; // min, max
    QMap<QString, bool> fixedParameters;

    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
};

// 文件元数据
struct FileMetadata {
    QString fileName;
    FileType fileType;
    QDateTime createdTime;
    QDateTime modifiedTime;
    qint64 fileSize;
    QString version;
    QMap<QString, QVariant> customProperties;

    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
};
```

### 3.2 SFLP文件格式结构

```cpp
// SFLP文件头结构
struct SflpFileHeader {
    quint32 magicNumber;        // 0x534C4650 ("SFLP")
    quint32 version;            // 文件格式版本
    quint32 headerSize;         // 文件头大小
    quint32 indexOffset;        // 索引表偏移
    quint32 indexSize;          // 索引表大小
    quint32 dataSegmentCount;   // 数据段数量
    quint64 totalFileSize;      // 文件总大小
    quint32 compressionType;    // 压缩类型 (0=无压缩, 1=Qt压缩)
    quint32 reserved[8];        // 保留字段

    bool isValid() const;
    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
};

// 数据段索引项
struct DataSegmentIndex {
    QString segmentName;        // 数据段名称
    quint64 offset;            // 数据段偏移
    quint64 compressedSize;    // 压缩后大小
    quint64 originalSize;      // 原始大小
    quint32 checksum;          // 数据校验和
    QDateTime timestamp;       // 创建时间

    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
};

// SFLP文件管理器
class SflpFileManager {
public:
    explicit SflpFileManager(const QString& fileName);
    ~SflpFileManager();

    // 文件操作
    bool openFile(QIODevice::OpenMode mode);
    void closeFile();
    bool isOpen() const;

    // 数据段操作
    bool writeDataSegment(const QString& segmentName,
                         const QByteArray& data);
    QByteArray readDataSegment(const QString& segmentName);
    bool removeDataSegment(const QString& segmentName);
    QStringList getDataSegmentNames() const;

    // 索引管理
    bool rebuildIndex();
    bool validateFile() const;
    qint64 getFileSize() const;

    // 错误处理
    QString getLastError() const;
    bool hasError() const;

private:
    QString m_fileName;
    QFile* m_file;
    SflpFileHeader m_header;
    QMap<QString, DataSegmentIndex> m_indexMap;
    QString m_lastError;

    bool readHeader();
    bool writeHeader();
    bool readIndex();
    bool writeIndex();
    quint32 calculateChecksum(const QByteArray& data) const;
};
```

## 4. 操作流程设计

### 4.1 操作保存流程

```cpp
// Process操作保存流程伪代码
class ProcessOperationSaver {
public:
    bool saveOperation(OperationType type,
                      const PlotDataCollection& plotData) {
        try {
            // 1. 生成操作序列
            QString currentFile = getCurrentFileName();
            OperationCounters counters =
                m_relationCache->getOperationCounters(currentFile);
            counters.incrementCounter(type);

            OperationSequence sequence = buildOperationSequence(counters);

            // 2. 生成操作名称
            QString operationName =
                m_nameManager->generateOperationName(currentFile, sequence);

            // 3. 创建操作元数据
            OperationMetadata metadata;
            metadata.operationType = type;
            metadata.timestamp = QDateTime::currentDateTime();
            metadata.operationName = operationName;
            metadata.sequence = sequence;

            // 4. 序列化数据
            QByteArray plotDataBytes = plotData.serialize();
            QByteArray metadataBytes = metadata.serialize();

            // 5. 压缩数据
            QByteArray compressedPlotData =
                m_projectManager->compressData(plotDataBytes);
            QByteArray compressedMetadata =
                m_projectManager->compressData(metadataBytes);

            // 6. 保存到SFLP文件
            bool success = m_projectManager->saveToSflpFormat(
                currentFile, compressedPlotData, operationName + "_data");
            success &= m_projectManager->saveToSflpFormat(
                currentFile, compressedMetadata, operationName + "_meta");

            if (success) {
                // 7. 更新文件关系缓存
                m_relationCache->addFileRelation(currentFile, operationName);
                m_relationCache->updateOperationCounter(currentFile, type,
                                                       counters.getCounter(type));

                // 8. 发送通知
                emit operationSaved(operationName);
                return true;
            }

        } catch (const std::exception& e) {
            m_lastError = QString("Operation save failed: %1").arg(e.what());
            emit errorOccurred(m_lastError);
        }

        return false;
    }

private:
    ProjectFileManager* m_projectManager;
    FileRelationshipCache* m_relationCache;
    FileNameManager* m_nameManager;
    QString m_lastError;

    OperationSequence buildOperationSequence(const OperationCounters& counters);
    QString getCurrentFileName() const;
};
```

### 4.2 拟合数据保存流程

```cpp
// Analysis拟合保存流程伪代码
class FittingDataSaver {
public:
    bool saveFittingResults(const QString& baseOperationName,
                           const FittingResults& results,
                           const FittingParameters& parameters) {
        try {
            // 1. 生成拟合序列号
            int fittingSequence =
                m_nameManager->getNextFittingSequence(baseOperationName);

            // 2. 生成拟合名称
            QString fittingName =
                m_nameManager->generateFittingName(baseOperationName,
                                                  fittingSequence);

            // 3. 序列化拟合数据
            QByteArray resultsBytes = results.serialize();
            QByteArray parametersBytes = parameters.serialize();

            // 4. 压缩数据
            QByteArray compressedResults =
                m_projectManager->compressData(resultsBytes);
            QByteArray compressedParameters =
                m_projectManager->compressData(parametersBytes);

            // 5. 获取父文件名
            QString parentFile =
                m_relationCache->getParentFile(baseOperationName);
            if (parentFile.isEmpty()) {
                parentFile = baseOperationName; // 直接基于原始文件的拟合
            }

            // 6. 保存到SFLP文件
            bool success = m_projectManager->saveToSflpFormat(
                parentFile, compressedResults, fittingName + "_results");
            success &= m_projectManager->saveToSflpFormat(
                parentFile, compressedParameters, fittingName + "_params");

            if (success) {
                // 7. 更新文件关系缓存
                m_relationCache->addFileRelation(baseOperationName, fittingName);

                // 8. 发送通知
                emit fittingSaved(fittingName);
                return true;
            }

        } catch (const std::exception& e) {
            m_lastError = QString("Fitting save failed: %1").arg(e.what());
            emit errorOccurred(m_lastError);
        }

        return false;
    }

private:
    ProjectFileManager* m_projectManager;
    FileRelationshipCache* m_relationCache;
    FileNameManager* m_nameManager;
    QString m_lastError;
};
```

### 4.3 文件加载流程

```cpp
// 数据加载流程伪代码
class DataLoader {
public:
    PlotDataCollection loadOperationData(const QString& operationName) {
        try {
            // 1. 获取父文件
            QString parentFile = m_relationCache->getParentFile(operationName);
            if (parentFile.isEmpty()) {
                throw std::runtime_error("Parent file not found");
            }

            // 2. 从SFLP文件读取压缩数据
            QByteArray compressedData =
                m_projectManager->loadFromSflpFormat(parentFile,
                                                    operationName + "_data");

            // 3. 解压缩数据
            QByteArray rawData = m_projectManager->decompressData(compressedData);

            // 4. 反序列化数据
            PlotDataCollection plotData;
            if (!plotData.deserialize(rawData)) {
                throw std::runtime_error("Failed to deserialize plot data");
            }

            // 5. 验证数据完整性
            if (!plotData.isValid()) {
                throw std::runtime_error("Invalid plot data");
            }

            return plotData;

        } catch (const std::exception& e) {
            qCritical() << "Failed to load operation data:" << e.what();
            return PlotDataCollection(); // 返回空数据
        }
    }

    FittingResults loadFittingData(const QString& fittingName) {
        try {
            // 1. 获取基础操作名称
            QString baseOperation = extractBaseOperationName(fittingName);
            QString parentFile = m_relationCache->getParentFile(baseOperation);

            // 2. 读取拟合结果
            QByteArray compressedResults =
                m_projectManager->loadFromSflpFormat(parentFile,
                                                    fittingName + "_results");
            QByteArray rawResults =
                m_projectManager->decompressData(compressedResults);

            // 3. 反序列化拟合结果
            FittingResults results;
            if (!results.deserialize(rawResults)) {
                throw std::runtime_error("Failed to deserialize fitting results");
            }

            return results;

        } catch (const std::exception& e) {
            qCritical() << "Failed to load fitting data:" << e.what();
            return FittingResults(); // 返回空结果
        }
    }

private:
    ProjectFileManager* m_projectManager;
    FileRelationshipCache* m_relationCache;

    QString extractBaseOperationName(const QString& fittingName);
};
```

## 5. UI集成设计

### 5.1 OpenProjectWidget增强

```cpp
// OpenProjectWidget文件树显示增强
class EnhancedOpenProjectWidget : public OpenProjectWidget {
public:
    void refreshFileTree() {
        try {
            // 1. 清空现有树结构
            m_treeModel->clear();

            // 2. 获取项目根目录
            QString projectPath = m_projectManager->getFilePath();
            if (projectPath.isEmpty()) return;

            // 3. 构建根节点
            QStandardItem* rootItem = createRootItem(projectPath);
            m_treeModel->appendRow(rootItem);

            // 4. 获取所有SFLP文件
            QStringList sflpFiles = getSflpFiles(projectPath);

            // 5. 为每个SFLP文件构建树结构
            for (const QString& fileName : sflpFiles) {
                QStandardItem* fileItem = createFileItem(fileName);
                rootItem->appendRow(fileItem);

                // 6. 添加操作子项
                addOperationChildren(fileItem, fileName);
            }

            // 7. 应用文件类型过滤
            applyFileTypeFilter();

            // 8. 展开树结构
            m_treeView->expandAll();

        } catch (const std::exception& e) {
            qCritical() << "Failed to refresh file tree:" << e.what();
        }
    }

private:
    void addOperationChildren(QStandardItem* parentItem,
                             const QString& fileName) {
        // 获取子文件列表（包括虚拟子项）
        QStringList childFiles =
            m_projectManager->getChildFiles(fileName);

        for (const QString& childName : childFiles) {
            FileType fileType = m_projectManager->getFileType(childName);

            QStandardItem* childItem = nullptr;

            switch (fileType) {
            case FileType::Operation:
                childItem = createOperationItem(childName);
                parentItem->appendRow(childItem);

                // 递归添加拟合子项
                addFittingChildren(childItem, childName);
                break;

            case FileType::Split:
                childItem = createSplitItem(childName);
                parentItem->appendRow(childItem);
                break;

            default:
                break;
            }
        }
    }

    void addFittingChildren(QStandardItem* parentItem,
                           const QString& operationName) {
        QStringList fittingFiles =
            m_projectManager->getChildFiles(operationName);

        for (const QString& fittingName : fittingFiles) {
            if (m_projectManager->getFileType(fittingName) == FileType::Fitting) {
                QStandardItem* fittingItem = createFittingItem(fittingName);
                parentItem->appendRow(fittingItem);
            }
        }
    }

    void applyFileTypeFilter() {
        // 根据当前Tab类型过滤显示
        TabType currentTab = getCurrentTabType();

        switch (currentTab) {
        case TabType::Acquire:
            hideItemsByType({FileType::Operation, FileType::Fitting});
            break;
        case TabType::Process:
            hideItemsByType({FileType::Fitting});
            break;
        case TabType::Analysis:
            // 显示所有类型
            break;
        }
    }

    QStandardItem* createFileItem(const QString& fileName);
    QStandardItem* createOperationItem(const QString& operationName);
    QStandardItem* createFittingItem(const QString& fittingName);
    QStandardItem* createSplitItem(const QString& splitName);
    void hideItemsByType(const QList<FileType>& typesToHide);
    TabType getCurrentTabType() const;
};
```

## 6. 错误处理和验证策略

### 6.1 参数验证框架

```cpp
class ValidationFramework {
public:
    // 文件名验证
    static ValidationResult validateFileName(const QString& fileName) {
        ValidationResult result;

        if (fileName.isEmpty()) {
            result.addError("File name cannot be empty");
            return result;
        }

        // 检查非法字符
        QRegularExpression invalidChars("[<>:\"/\\|?*]");
        if (invalidChars.match(fileName).hasMatch()) {
            result.addError("File name contains invalid characters");
        }

        // 检查长度限制
        if (fileName.length() > 255) {
            result.addError("File name too long (max 255 characters)");
        }

        return result;
    }

    // 操作序列验证
    static ValidationResult validateOperationSequence(
        const OperationSequence& sequence) {
        ValidationResult result;

        if (sequence.isEmpty()) {
            result.addError("Operation sequence cannot be empty");
            return result;
        }

        // 验证序列连续性
        QMap<OperationType, int> lastSequence;
        for (const auto& op : sequence.operations) {
            if (lastSequence.contains(op.first)) {
                if (op.second != lastSequence[op.first] + 1) {
                    result.addError("Operation sequence not continuous");
                }
            }
            lastSequence[op.first] = op.second;
        }

        return result;
    }

    // 数据完整性验证
    static ValidationResult validatePlotData(
        const PlotDataCollection& plotData) {
        ValidationResult result;

        if (!plotData.isValid()) {
            result.addError("Plot data is invalid");
        }

        if (plotData.calculateSize() == 0) {
            result.addError("Plot data is empty");
        }

        // 验证数据一致性
        if (!validateDataConsistency(plotData)) {
            result.addError("Plot data inconsistent");
        }

        return result;
    }

private:
    static bool validateDataConsistency(const PlotDataCollection& plotData);
};

struct ValidationResult {
    QStringList errors;
    QStringList warnings;

    bool isValid() const { return errors.isEmpty(); }
    void addError(const QString& error) { errors.append(error); }
    void addWarning(const QString& warning) { warnings.append(warning); }
    QString getErrorString() const { return errors.join("; "); }
};
```

### 6.2 异常处理策略

```cpp
// 自定义异常类
class FileManagementException : public std::exception {
public:
    explicit FileManagementException(const QString& message)
        : m_message(message.toStdString()) {}

    const char* what() const noexcept override {
        return m_message.c_str();
    }

    QString getMessage() const {
        return QString::fromStdString(m_message);
    }

private:
    std::string m_message;
};

class FileCorruptedException : public FileManagementException {
public:
    explicit FileCorruptedException(const QString& fileName)
        : FileManagementException(QString("File corrupted: %1").arg(fileName)) {}
};

class InsufficientSpaceException : public FileManagementException {
public:
    explicit InsufficientSpaceException(qint64 required, qint64 available)
        : FileManagementException(
            QString("Insufficient disk space: required %1 MB, available %2 MB")
            .arg(required / 1024 / 1024).arg(available / 1024 / 1024)) {}
};

// 错误处理管理器
class ErrorHandler {
public:
    static ErrorHandler* getInstance();

    void handleException(const std::exception& e,
                        const QString& context = QString());
    void handleFileError(const QString& fileName,
                        const QString& operation,
                        const QString& error);
    void handleValidationError(const ValidationResult& result,
                              const QString& context);

    // 错误恢复策略
    bool attemptFileRecovery(const QString& fileName);
    bool createBackup(const QString& fileName);
    bool restoreFromBackup(const QString& fileName);

    // 错误日志
    void logError(const QString& error, const QString& context);
    void logWarning(const QString& warning, const QString& context);
    QStringList getRecentErrors(int count = 10) const;

signals:
    void errorOccurred(const QString& error, const QString& context);
    void warningOccurred(const QString& warning, const QString& context);
    void recoveryAttempted(const QString& fileName, bool success);

private:
    QStringList m_errorLog;
    QStringList m_warningLog;
    QMutex m_logMutex;

    QString formatErrorMessage(const QString& error,
                              const QString& context) const;
};
```

## 7. 性能优化策略

### 7.1 缓存管理

```cpp
class CacheManager {
public:
    static CacheManager* getInstance();

    // 数据缓存
    void cacheOperationData(const QString& operationName,
                           const PlotDataCollection& data);
    PlotDataCollection getCachedOperationData(const QString& operationName);
    bool isOperationDataCached(const QString& operationName) const;

    void cacheFittingData(const QString& fittingName,
                         const FittingResults& results);
    FittingResults getCachedFittingData(const QString& fittingName);
    bool isFittingDataCached(const QString& fittingName) const;

    // 缓存策略
    void setMaxCacheSize(qint64 maxSize);
    void setMaxCacheItems(int maxItems);
    void enableLRUEviction(bool enable);

    // 缓存管理
    void clearCache();
    void evictLeastRecentlyUsed();
    qint64 getCurrentCacheSize() const;
    int getCurrentCacheItems() const;

    // 预加载策略
    void preloadRelatedData(const QString& fileName);
    void preloadRecentlyUsed();

private:
    struct CacheItem {
        QByteArray data;
        QDateTime lastAccessed;
        qint64 size;
        int accessCount;
    };

    QMap<QString, CacheItem> m_operationCache;
    QMap<QString, CacheItem> m_fittingCache;
    qint64 m_maxCacheSize;
    int m_maxCacheItems;
    bool m_lruEnabled;

    void updateAccessTime(const QString& key);
    void evictIfNecessary();
    QString findLeastRecentlyUsed() const;
};
```

### 7.2 异步操作管理

```cpp
class AsyncOperationManager : public QObject {
    Q_OBJECT

public:
    static AsyncOperationManager* getInstance();

    // 异步保存操作
    QFuture<bool> saveOperationAsync(const QString& fileName,
                                    const OperationSequence& sequence,
                                    const PlotDataCollection& plotData);

    QFuture<bool> saveFittingAsync(const QString& baseFileName,
                                  const FittingResults& results,
                                  const FittingParameters& parameters);

    // 异步加载操作
    QFuture<PlotDataCollection> loadOperationAsync(const QString& operationName);
    QFuture<FittingResults> loadFittingAsync(const QString& fittingName);

    // 批量操作
    QFuture<QStringList> batchSaveOperations(
        const QList<QPair<QString, PlotDataCollection>>& operations);

    // 进度监控
    void setProgressCallback(std::function<void(int)> callback);
    void cancelOperation(const QString& operationId);
    bool isOperationRunning(const QString& operationId) const;

signals:
    void operationStarted(const QString& operationId);
    void operationProgress(const QString& operationId, int progress);
    void operationCompleted(const QString& operationId, bool success);
    void operationCancelled(const QString& operationId);

private:
    QThreadPool* m_threadPool;
    QMap<QString, QFutureWatcher<bool>*> m_activeOperations;
    std::function<void(int)> m_progressCallback;

    QString generateOperationId() const;
    void cleanupOperation(const QString& operationId);
};

// 异步任务包装器
template<typename T>
class AsyncTask : public QRunnable {
public:
    AsyncTask(std::function<T()> task, QPromise<T>& promise)
        : m_task(task), m_promise(promise) {}

    void run() override {
        try {
            T result = m_task();
            m_promise.addResult(result);
            m_promise.finish();
        } catch (const std::exception& e) {
            m_promise.setException(std::make_exception_ptr(e));
            m_promise.finish();
        }
    }

private:
    std::function<T()> m_task;
    QPromise<T>& m_promise;
};
```

## 8. 集成点分析

### 8.1 与现有代码的集成策略

```cpp
// 渐进式集成适配器
class LegacyIntegrationAdapter {
public:
    // MeasureDataHandler适配
    static PlotDataCollection convertFromMeasureData(
        const MeasureDataHandler* handler);
    static bool convertToMeasureData(
        const PlotDataCollection& plotData,
        MeasureDataHandler* handler);

    // 旧格式文件支持
    static bool convertSfdToSflp(const QString& sfdFile,
                                const QString& sflpFile);
    static PlotDataCollection loadFromSfdFile(const QString& sfdFile);

    // AppConfig集成
    static void migrateConfigSettings();
    static QString getProjectPrefixFromConfig();
    static void updateConfigWithNewSettings();

    // 向后兼容性
    static bool isLegacyFile(const QString& fileName);
    static QString getLegacyDisplayName(const QString& fileName);
    static bool supportLegacyOperation(const QString& operation);
};

// 分阶段迁移管理器
class MigrationManager {
public:
    enum MigrationPhase {
        Phase1_FileNameManager,     // 实现FileNameManager
        Phase2_FileRelationCache,   // 实现FileRelationshipCache
        Phase3_ProjectFileManager,  // 增强ProjectFileManager
        Phase4_UIIntegration,       // UI层集成
        Phase5_LegacyCleanup       // 清理旧代码
    };

    bool executeMigrationPhase(MigrationPhase phase);
    bool validateMigrationPhase(MigrationPhase phase);
    void rollbackMigrationPhase(MigrationPhase phase);

    MigrationPhase getCurrentPhase() const;
    QStringList getPhaseRequirements(MigrationPhase phase) const;
    bool isPhaseComplete(MigrationPhase phase) const;

private:
    MigrationPhase m_currentPhase;
    QMap<MigrationPhase, bool> m_phaseStatus;

    bool executeFileNameManagerPhase();
    bool executeFileRelationCachePhase();
    bool executeProjectFileManagerPhase();
    bool executeUIIntegrationPhase();
    bool executeLegacyCleanupPhase();
};
```

## 9. 测试策略

### 9.1 单元测试框架

```cpp
// 测试基类
class FileManagementTestBase : public QObject {
    Q_OBJECT

protected:
    void SetUp() {
        // 创建临时测试目录
        m_testDir = QDir::temp().filePath("SpecFLIM_Test_" +
                                         QUuid::createUuid().toString());
        QDir().mkpath(m_testDir);

        // 初始化测试组件
        m_fileNameManager = FileNameManager::getInstance();
        m_relationCache = FileRelationshipCache::getInstance();
        m_projectManager = ProjectFileManager::getInstance();
    }

    void TearDown() {
        // 清理测试数据
        QDir(m_testDir).removeRecursively();

        // 重置单例状态
        m_relationCache->clearCache();
    }

    // 测试辅助方法
    QString createTestSflpFile(const QString& fileName);
    PlotDataCollection createTestPlotData();
    OperationSequence createTestOperationSequence();
    void verifyFileIntegrity(const QString& fileName);

protected:
    QString m_testDir;
    FileNameManager* m_fileNameManager;
    FileRelationshipCache* m_relationCache;
    ProjectFileManager* m_projectManager;
};

// 具体测试类示例
class FileNameManagerTest : public FileManagementTestBase {
    Q_OBJECT

private slots:
    void testProjectPrefixGeneration();
    void testOperationNameGeneration();
    void testFittingNameGeneration();
    void testSequenceNumberManagement();
    void testConfigIntegration();
    void testErrorHandling();
};

class FileRelationshipCacheTest : public FileManagementTestBase {
    Q_OBJECT

private slots:
    void testFileRelationManagement();
    void testVirtualNodeOperations();
    void testOperationCounterManagement();
    void testCachePersistence();
    void testPerformanceOptimization();
};
```

## 10. 实施计划

### 10.1 开发阶段规划

#### 阶段1: 基础架构实现 (2-3周)
**目标**: 实现核心文件管理组件
- 实现FileNameManager类
- 实现FileRelationshipCache类
- 实现基础数据结构(OperationSequence, FileMetadata等)
- 集成AppConfig配置管理
- 编写单元测试

**交付物**:
- FileNameManager完整实现
- FileRelationshipCache完整实现
- 配置文件集成
- 单元测试覆盖率>80%

#### 阶段2: 文件格式支持 (2-3周)
**目标**: 实现SFLP文件格式和压缩机制
- 实现SflpFileManager类
- 集成Qt压缩/解压缩
- 实现数据序列化/反序列化
- 文件完整性验证
- 性能优化

**交付物**:
- SFLP文件格式完整支持
- Qt压缩集成
- 文件完整性验证
- 性能基准测试

#### 阶段3: ProjectFileManager增强 (2-3周)
**目标**: 增强ProjectFileManager统一接口
- 实现操作数据保存/加载
- 实现拟合数据保存/加载
- 集成FileRelationshipCache
- 错误处理和恢复机制
- 异步操作支持

**交付物**:
- ProjectFileManager统一接口
- 完整的错误处理机制
- 异步操作支持
- 集成测试

#### 阶段4: UI层集成 (2-3周)
**目标**: 集成OpenProjectWidget和文件树显示
- 增强OpenProjectWidget文件树显示
- 实现虚拟节点显示
- 文件类型过滤
- 自然排序算法
- 用户交互优化

**交付物**:
- 增强的文件树显示
- 虚拟节点支持
- 文件类型过滤
- 用户体验优化

#### 阶段5: 系统集成和测试 (1-2周)
**目标**: 完整系统集成和测试
- 端到端测试
- 性能测试
- 兼容性测试
- 文档完善
- 部署准备

**交付物**:
- 完整系统集成
- 全面测试报告
- 性能优化报告
- 用户文档

### 10.2 风险评估和缓解策略

#### 高风险项
1. **数据迁移复杂性**
   - 风险: 现有数据格式转换可能导致数据丢失
   - 缓解: 实现完整的备份和回滚机制，分阶段迁移

2. **性能影响**
   - 风险: 新的文件格式可能影响读写性能
   - 缓解: 实施性能基准测试，优化关键路径

3. **向后兼容性**
   - 风险: 新系统可能破坏现有功能
   - 缓解: 保持旧格式读取支持，渐进式迁移

#### 中风险项
1. **UI集成复杂性**
   - 风险: 文件树显示可能影响用户体验
   - 缓解: 用户测试和反馈收集，迭代优化

2. **配置管理**
   - 风险: 配置迁移可能导致设置丢失
   - 缓解: 配置备份和验证机制

### 10.3 质量保证策略

#### 代码质量
- 代码审查: 所有代码变更需要同行审查
- 静态分析: 使用静态分析工具检查代码质量
- 编码规范: 遵循Qt和C++最佳实践
- 文档化: 所有公共接口需要完整文档

#### 测试策略
- 单元测试: 覆盖率要求>80%
- 集成测试: 验证组件间交互
- 性能测试: 确保性能不退化
- 兼容性测试: 验证向后兼容性

#### 部署策略
- 分阶段部署: 逐步启用新功能
- 监控机制: 实时监控系统状态
- 回滚计划: 快速回滚机制
- 用户培训: 提供用户培训和文档

## 11. 总结

本设计文档提供了SpecFLIM统一文件管理系统的完整架构设计，包括：

1. **核心组件设计**: FileNameManager、FileRelationshipCache、增强的ProjectFileManager
2. **数据结构定义**: 完整的数据结构和文件格式规范
3. **操作流程**: 详细的保存、加载和UI集成流程
4. **质量保证**: 错误处理、验证、性能优化和测试策略
5. **实施计划**: 分阶段的开发计划和风险缓解策略

该设计遵循生产级代码质量要求，提供完整的错误处理、参数验证和内存管理，确保系统的可靠性和可维护性。通过统一的接口设计和模块化架构，为SpecFLIM应用程序提供强大而灵活的文件管理能力。
