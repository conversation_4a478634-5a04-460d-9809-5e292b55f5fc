# Process文件保存功能需求文档

## 1. 功能概述和目标

### 1.1 功能概述
Process文件保存功能是SpecFLIM应用程序中的核心功能，负责管理Process标签页中的数据操作和保存。该功能基于统一文件管理系统，支持对齐(Alignment)、裁剪(Crop)、曲线添加(Add)三种操作，并为每种操作提供自动保存和版本管理机制。系统集成FileRelationshipCache进行文件关系管理，支持新格式文件的层级显示。

### 1.2 功能目标
- 提供透明的操作历史记录和版本管理
- 支持非破坏性的数据操作流程
- 实现操作的可追溯性和可恢复性
- 提供直观的用户界面和操作反馈
- 确保数据完整性和一致性
- 集成FileRelationshipCache系统进行统一文件关系管理
- 支持新格式文件（操作子项、拟合子项）的层级显示

## 2. 文件命名和格式规范

### 2.1 工作区文件命名
- **默认格式**: `{项目前缀}{YYMMDD}.sflw`
- **示例**: `ABC250524.sflw`
- **规则**:
  - 项目前缀从AppConfig配置文件动态读取
  - 日期部分根据当前日期自动生成(6位格式YYMMDD)

### 2.2 工程文件命名
- **默认格式**: `{项目前缀}{YYMMDD}_{序号}.sfld`
- **示例**: `ABC250524_001.sfld`, `ABC250524_002.sfld`
- **规则**:
  - 项目前缀从AppConfig配置文件动态读取
  - 日期部分根据当前日期自动生成(6位格式YYMMDD)
  - 序号从001开始，按创建顺序递增

### 2.3 操作保存命名规范
- **基本格式**: `{工程文件名}_{操作序列}`
- **操作代码**:
  - `al`: 对齐操作(Alignment)
  - `cr`: 裁剪操作(Crop)
  - `ad`: 曲线添加操作(Add curve)
- **示例**:
  - `ABC250524_001_al1`: 第一次对齐操作
  - `ABC250524_001_al1_cr1`: 对齐后的第一次裁剪操作
  - `ABC250524_001_al1_cr1_ad1`: 对齐、裁剪后的第一次曲线添加操作
- **特点**:
  - 操作数据作为工程文件内的数据段存储
  - 使用Qt压缩存储
  - 在文件树中显示为工程文件的虚拟子项


### 2.4 拟合数据存储规范
- **特点**:
  - 以_fit后缀标识
  - 作为工程文件内的数据段存储
  - 使用Qt压缩存储
  - 在文件树中显示为工程文件的虚拟子项
- **基本格式**: `{基础文件名}_{拟合序号}_fit`
- **示例**:
  - `ABC250524_001_al1_cr1_ad1_fit1`: 基于操作序列的拟合结果
  - `ABC250524_001_fit1`: 基于原始文件的拟合结果
- **特点**:
  - 以_fit后缀结尾
  - 使用Qt压缩存储


### 2.6 Split导出文件命名
- **格式**: `{原工程文件名}_split{序号}.sfld`
- **示例**: `ABC250524_001_split1.sfld`, `ABC250524_001_split2.sfld`
- **规则**: 序号从1开始，按导出顺序递增

### 2.5 项目前缀配置说明
- **配置来源**: 项目前缀从AppConfig配置文件中读取
- **配置参数**: 通过配置文件中的项目前缀参数动态获取
- **默认值**: 如果配置文件中未设置或读取失败，使用默认前缀
- **更新机制**: 支持运行时更新项目前缀配置

## 3. 详细功能需求

### 3.1 操作保存需求

#### 3.1.1 对齐操作保存
- **触发条件**: 点击Alignment区域的Apply按钮
- **保存内容**:
  - Fluorescence Map数据
  - Decay Curve数据(仅已添加的曲线)
  - Spectral Curve数据(仅已添加的曲线)
  - Total Counts数据
- **命名规则**: 在当前数据名称后添加`_al{序号}`
- **版本管理**: 对同一数据的多次对齐操作，序号递增

#### 3.1.2 裁剪操作保存
- **触发条件**: 点击Crop区域的Apply按钮
- **保存内容**:
  - Fluorescence Map数据(裁剪后)
  - Decay Curve数据(仅已添加的曲线，随Fluorescence Map变化)
  - Spectral Curve数据(仅已添加的曲线，随Fluorescence Map变化)
  - Total Counts数据(裁剪后重新计算)
- **命名规则**: 在当前数据名称后添加`_cr{序号}`
- **版本管理**: 对同一数据的多次裁剪操作，序号递增

#### 3.1.3 曲线添加操作保存
- **触发条件**: 点击Curve区域的Apply按钮
- **保存内容**:
  - Fluorescence Map数据
  - Decay Curve数据(包含新添加的曲线)
  - Spectral Curve数据(包含新添加的曲线)
  - Total Counts数据
- **命名规则**: 在当前数据名称后添加`_ad{序号}`
- **版本管理**: 对同一数据的多次曲线添加操作，序号递增
- **特殊要求**: 只保存已添加的曲线，不保存十字线实时显示的曲线

## 3.2 操作序列管理
### 3.2.1 序号维护机制
1. **独立计数器系统**:
   - 每个.sfld文件维护独立的操作类型计数器
   - 计数器状态随文件保存而持久化
   - 文件关闭时保留最后操作序号
   - 重新打开时恢复各操作类型最后序号
   - 每个操作类型(al/cr/ad)维护独立内存计数器
   - 通过FileRelationCache::updateOperationCounter实现计数器管理
   - 文件打开时从现有操作序列解析最大值初始化
   - 示例：现有`al3_cr2`序列初始化计数器为：
     - al:3, cr:2, ad:0
   - **sfld文件范围计数规则**:
     - 每个sfld文件维护独立的操作类型计数器
     - 计数器状态随文件保存而持久化
     - 文件关闭时保留最后操作序号
     - 重新打开时恢复各操作类型最后序号

2. **序列构建规则**:
   - 新操作继承当前完整操作序列
   - 追加操作时对应类型序号+1
   - 示例演化：
     - 初始：`al1`
     - +裁剪：`al1_cr1`
     - +曲线：`al1_cr1_ad1`

### 3.2.2 虚拟节点生成
1. **命名生成流程**:
   - 在FileRelationCache::generateVirtualName中实现：
   ```cpp
   // 生成操作序列字符串
   QString seq = currentFile.baseName() + '_' + 
                QString("al%1").arg(alCounter) +
                QString("_cr%1").arg(crCounter) +
                QString("_ad%1").arg(adCounter);
   ```

2. **存储管理**:
   - 每个虚拟节点关联UUID作为存储标识
   - 通过FileRelationCache::saveVirtualNode持久化存储操作序列
   - 存储格式示例：
   ```json
   {
     "operation_sequence": [
       {"type":"al", "timestamp":"20240530T142857", "seq":1},
       {"type":"cr", "timestamp":"20240530T142901", "seq":1}
     ]
   }
   ```

## 3.3 Split功能需求
### 3.2 Split功能需求

#### 3.2.1 Single导出功能
- **触发条件**: 点击Split区域Single部分的Export按钮
- **功能描述**: 导出当前选中的单帧数据为新的工程文件
- **保存内容**:
  - 选中帧的Fluorescence Map数据
  - 对应的Total Counts数据(仅选中帧的点)
- **不保存内容**: Decay Curve和Spectral Curve(设为空状态)
- **文件位置**: 与原工程文件相同目录
- **Workspace显示**: 在原文件下方显示
- **命名示例**: 从`ABC250524_001.sfld`导出为`ABC250524_001_split1.sfld`

#### 3.2.2 Range导出功能
- **触发条件**: 点击Split区域Range部分的Export按钮
- **功能描述**: 导出指定帧范围的合并数据为新的工程文件
- **保存内容**:
  - 指定范围帧的Fluorescence Map合并数据
  - 对应范围的Total Counts数据
- **不保存内容**: Decay Curve和Spectral Curve(设为空状态)
- **文件位置**: 与原工程文件相同目录
- **Workspace显示**: 在原文件下方显示
- **命名示例**: 从`ABC250524_001.sfld`导出为`ABC250524_001_split2.sfld`

### 3.3 虚拟节点生成与存储
### 3.3.1 实时构建规则
1. 在每次apply/save操作时动态生成虚拟节点文件名
2. 命名格式：`{工程文件名}_{操作序列}`
3. 示例：`ABC250524_001_al1_cr1`

### 3.3.2 树节点关联
1. 生成的虚拟节点与文件树中的对应项建立双向映射
2. 通过FileRelationCache维护映射关系
3. 点击虚拟节点时在plot中显示对应数据

### 3.3.3 UUID存储管理
1. 为每个虚拟节点分配唯一UUID
2. 通过UUID机制确保操作序列的持久化存储
3. 文件重新打开时通过UUID恢复操作序列

## 3.4 数据影响关系需求

#### 3.3.1 操作对数据的影响
- **对齐操作影响**: Fluorescence Map, Decay Curve, Spectral Curve
- **裁剪操作影响**: Fluorescence Map, Decay Curve, Spectral Curve, Total Counts
- **曲线添加操作影响**: Decay Curve, Spectral Curve

#### 3.3.2 数据联动关系
- Decay Curve和Spectral Curve始终与Fluorescence Map保持对应关系
- 已添加的曲线在Fluorescence Map变化后应相应更新
- Total Counts反映当前Fluorescence Map的总光子数统计

## 4. 用户界面需求

### 4.1 界面修改需求
- **删除元素**: 移除Process界面左下角的Save按钮
- **新增元素**: 在以下位置添加Apply按钮
  - Alignment区域: 水平方向右侧、垂直方向中间位置
  - Curve区域: 第三行右侧位置

### 4.2 Workspace显示需求
- **层级结构**:
  - 工程文件(.sfld)
    - 操作数据（虚拟子项）
    - 拟合数据（虚拟子项）
    - 操作保存数据(虚拟子项，显示操作名称)
      - 拟合子项(_fit后缀)
    - Split导出文件(.sfld，显示为同级)
- **显示内容**: 仅显示文件名，不显示文件大小
- **排序规则**: 按文件名中的数字序列自然排序(升序)
- **实现方式**:
  - 通过解析.sfld文件内部索引构建虚拟树形结构
  - 操作数据作为工程文件的虚拟子节点显示
  - 基于FileRelationshipCache系统管理文件关系
  - 使用ProjectFileManager统一接口
- **性能要求**: 支持大量操作历史的快速显示，索引解析时间应控制在合理范围内
- **文件关系管理**:
  - 使用ProjectFileManager::getChildFiles()获取子文件列表（包括虚拟子项）
  - 使用ProjectFileManager::getParentFile()获取父文件信息
- **文件类型过滤**: 根据当前Tab类型过滤显示的文件类型
  - AcquireTab: 仅显示.sfld原始文件
  - ProcessTab: 显示.sfld文件和操作数据（虚拟子项）
  - AnalysisTab: 显示所有支持的文件类型（包括拟合子项）

### 4.3 数据加载和显示需求
- **原工程文件**: 点击显示原始Fluorescence Map
- **操作保存数据**: 点击显示对应操作后的四个图表
- **Split导出文件**: 点击显示对应的Fluorescence Map和Total Counts

## 5. 操作限制和约束

### 5.1 Split模式限制
- 在Single或Range模式下，禁用对齐、裁剪、曲线添加操作
- 必须返回All模式才能继续进行数据操作
- 对Split导出的文件进行操作需要先加载该文件

### 5.2 数据完整性约束
- 每次操作必须保存完整的相关数据
- 操作序列必须保持连续性和可追溯性
- 文件命名必须遵循既定规范，确保唯一性

## 6. 错误处理和用户反馈需求

### 6.1 操作反馈
- 每次Apply操作后提供明确的成功反馈
- 在Workspace中实时更新操作结果
- 提供操作进度指示(如需要)

### 6.2 错误处理
- 文件保存失败时提供明确的错误信息
- 命名冲突时自动处理或提示用户
- 数据损坏时提供恢复机制或警告

## 7. 与其他模块的集成需求

### 7.1 文件管理集成
- 支持统一的文件格式和压缩机制
- 遵循既定的文件命名和存储规范
- 集成FileRelationshipCache系统进行文件关系管理
- 支持新格式文件类型的识别和处理

### 7.2 数据处理集成
- 通过ProjectFileManager统一管理所有文件操作
- 支持数据缓存和状态管理
- 提供数据变更通知机制

### 7.3 UI集成
- 与BaseTab集成，提供统一的用户界面模式
- 支持主题管理和样式一致性
- 提供标准的用户交互模式

### 7.4 配置管理集成
- 与AppConfig集成，动态读取项目前缀配置
- 支持配置文件的实时更新和重新加载
- 提供配置验证和默认值处理机制
- 确保文件命名的一致性和可配置性

## 8. 性能和质量要求

### 8.1 性能要求
- 操作保存应在合理时间内完成(< 5秒)
- 大数据文件的处理应提供进度反馈
- 内存使用应保持在合理范围内

### 8.2 质量要求
- 数据保存的准确性和完整性
- 操作的可靠性和稳定性
- 用户界面的响应性和易用性

## 9. 兼容性要求

### 9.1 文件格式兼容性
- 支持现有的.sfd文件格式
- 保持与旧版本文件的读取兼容性
- 新格式应使用Qt压缩机制

### 9.2 功能兼容性
- 与现有的Analysis功能保持兼容
- 支持现有的数据处理流程
- 保持与其他模块的接口兼容性

## 10. 技术实现要求

### 10.1 数据存储要求
- 所有操作和拟合数据均以数据段形式存储在.sfld文件内
- 使用统一索引表管理虚拟节点与数据段的映射关系
- **存储格式**: 操作保存数据（包括拟合数据）作为工程文件内的数据段存储，不创建独立文件
- **文件结构**: .sfld文件包含文件头、原始数据段、多个操作数据段和元数据段
- **索引机制**: 维护操作名称到数据段位置的映射表，支持快速定位
- **压缩机制**: 使用Qt压缩方法(qCompress/qUncompress)进行数据压缩
- **数据结构**: 保持与现有数据结构的兼容性
- **序列化**: 支持完整的数据序列化和反序列化
- **拟合子项存储**: 拟合数据存储为独立的_fit后缀

### 10.2 FileRelationshipCache集成要求
- **缓存结构**: 维护文件名到显示名称的双向映射
  - 父子关系管理: 使用两个映射表实现高效查询
    - `parentMap`(子节点→父节点): 快速定位任意子节点的父节点（O(1)时间复杂度）
    - `childrenMap`(父节点→子节点列表): 快速获取任意父节点的所有子节点（O(1)时间复杂度）
    - 双表同步更新: 新增/删除节点时同时更新两个映射表，确保数据一致性
- **关系管理**: 维护父子文件关系的映射表（包括虚拟子项）
- **性能优化**: 提供O(1)复杂度的文件关系查询
- **实时更新**: 支持文件关系的实时更新和缓存刷新
- **元数据持久化**: 将文件关系信息持久化到元数据文件
- **虚拟子项支持**: 支持操作数据作为虚拟子项的显示和管理

### 10.2 版本管理要求
- **操作序号**: 每种操作类型独立计数，支持同一基础数据的多次同类操作
- **操作链**: 支持复杂的操作链组合，如`ABC250524_001_al1_cr1_cr1_ad1_ad1`
- **分支管理**: 支持从任意操作点开始新的操作分支
- **历史追踪**: 维护完整的操作历史和依赖关系
- **命名一致性**: 所有操作链命名遵循统一的项目前缀和日期格式规范

### 10.3 内存管理要求
- **缓存策略**: 实现智能缓存机制，避免重复加载相同数据
- **内存优化**: 大数据处理时采用流式处理或分块处理
- **资源释放**: 及时释放不再使用的数据资源
- **Qt内存模式**: 遵循Qt内存管理最佳实践

### 10.4 并发和异步要求
- **异步保存**: 大数据保存操作应异步执行，避免界面阻塞
- **进度反馈**: 长时间操作提供进度条或状态指示
- **取消机制**: 支持用户取消长时间运行的操作
- **线程安全**: 确保多线程环境下的数据安全

## 11. 数据结构和接口要求

### 11.1 数据结构定义
- **ProcessedData结构**: 包含四种图表数据的完整结构
  - FluorescenceMapData: 荧光图数据
  - DecayCurveData: 衰减曲线数据(仅已添加曲线)
  - SpectralCurveData: 光谱曲线数据(仅已添加曲线)
  - TotalCountsData: 总计数数据
- **操作元数据**: 包含操作类型、时间戳、参数等信息
- **版本信息**: 包含操作序列、依赖关系等版本管理信息

### 11.2 接口设计要求
- **统一保存接口**: 提供统一的操作保存接口，支持不同操作类型
- **数据加载接口**: 支持按操作名称或路径加载特定操作数据
- **版本查询接口**: 支持查询操作历史和版本信息
- **数据验证接口**: 提供数据完整性验证功能
- **文件命名接口**: 提供统一的文件命名生成接口，支持AppConfig配置读取
- **配置管理接口**: 支持项目前缀配置的读取、验证和更新
- **文件解析接口**: 提供.sfld文件内部操作索引的解析和查询功能
- **UI数据接口**: 为Workspace树形显示提供操作列表和层级关系数据

### 11.3 UI实现技术要求
- **虚拟树形结构**: 实现基于文件内数据段的虚拟树形显示，操作数据显示为工程文件的子项
- **索引缓存机制**: 实现操作索引的智能缓存，避免重复解析.sfld文件
- **异步加载**: 支持大文件的异步索引解析，避免UI阻塞
- **增量更新**: 支持操作完成后的增量树形结构更新，无需重建整个树
- **内存优化**: 只在内存中维护操作索引信息，不预加载完整操作数据
- **实时同步**: 确保UI显示与文件内容的实时同步
- **基于缓存的文件树**: 基于FileRelationshipCache构建文件树显示
- **统一接口调用**: 使用ProjectFileManager统一接口获取文件关系和显示名称
- **文件类型识别**: 使用ProjectFileManager::getFileType()进行文件类型判断
- **自然排序**: 实现基于创建时间的自然排序算法
- **类型过滤**: 根据Tab类型过滤显示的文件类型

### 11.4 事件和通知要求
- **操作完成事件**: 每次操作保存完成后发送通知
- **数据变更事件**: 数据加载或切换时发送变更通知
- **错误事件**: 操作失败时发送详细错误信息
- **状态同步**: 与UI层保持状态同步

## 12. 用户体验要求

### 12.1 操作流程要求
- **一键操作**: 每个Apply按钮点击即完成操作和保存
- **即时反馈**: 操作完成后立即在Workspace中显示结果
- **状态指示**: 清晰显示当前数据状态和操作历史

### 12.2 可视化要求
- **树形结构**: Workspace中以树形结构显示操作层次，基于文件内数据段的虚拟层级
- **图标区分**: 不同类型的操作和文件使用不同图标
- **状态标识**: 显示数据是否已保存、是否有变更等状态
- **工具提示**: 提供详细的操作信息和时间戳
- **虚拟节点**: 操作数据节点为虚拟节点，不对应实际文件系统中的独立文件

