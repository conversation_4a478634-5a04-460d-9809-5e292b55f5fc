#ifndef UNIFIEDFILESTRUCTURES_H
#define UNIFIEDFILESTRUCTURES_H

#include <QObject>
#include <QString>
#include <QDateTime>
#include <QByteArray>
#include <QVector>
#include <QMap>
#include <QPointF>
#include <QVariant>
#include <QDataStream>

// Forward declarations
class FluorescenceMapData;
class DecayCurveData;
class SpectralCurveData;
class TotalCountsData;

// 枚举定义
enum class OperationType {
    Alignment,  // al
    Crop,       // cr
    AddCurve    // ad
};

enum class AnalysisType {
    DecayAnalysis,        // DecAna
    SpectralAnalysis,     // SpeAna
    FluorescenceAnalysis  // FluAna
};

enum class FileType {
    Workspace,            // .sflw
    Project,              // .sflp
    Operation,            // 虚拟子项
    DecayAnalysis,        // DecAna虚拟子项
    SpectralAnalysis,     // SpeAna虚拟子项
    FluorescenceAnalysis, // FluAna虚拟子项
    Split                 // _split.sflp
};

// Tab类型枚举 - 统一定义，避免重复
enum class TabType {
    Acquire,    // 对应原来的AcquireTab
    Process,    // 对应原来的ProcessTab
    Analysis    // 对应原来的AnalysisTab
};

// 操作计数器结构
struct OperationCounters {
    int alignmentCounter = 0;
    int cropCounter = 0;
    int addCurveCounter = 0;
    int decayAnalysisCounter = 0;
    int spectralAnalysisCounter = 0;
    int fluorescenceAnalysisCounter = 0;
    
    int getCounter(OperationType type) const;
    int getCounter(AnalysisType type) const;
    void incrementCounter(OperationType type);
    void incrementCounter(AnalysisType type);
    void setCounter(OperationType type, int value);
    void setCounter(AnalysisType type, int value);
    
    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
    void reset();
};

// 操作序列结构
struct OperationSequence {
    QVector<QPair<OperationType, int>> operations;
    
    QString toString() const;
    static OperationSequence fromString(const QString& sequence);
    void appendOperation(OperationType type, int sequence);
    bool isEmpty() const;
    void clear();
    
    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
};

// 绘图数据集合
struct PlotDataCollection {
    QByteArray fluorescenceMapData;
    QVector<QByteArray> decayCurveData;      // 仅已添加的曲线
    QVector<QByteArray> spectralCurveData;   // 仅已添加的曲线
    QByteArray totalCountsData;
    QMap<QString, QVariant> metadata;
    
    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
    bool isValid() const;
    qint64 calculateSize() const;
    void clear();
};

// 操作元数据
struct OperationMetadata {
    OperationType operationType;
    QDateTime timestamp;
    QString operationName;
    OperationSequence sequence;
    QMap<QString, QVariant> parameters;
    QString description;
    
    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
    bool isValid() const;
    void clear();
};

// 分析结果数据
struct AnalysisResults {
    AnalysisType analysisType;
    QMap<QString, double> parameters;
    QVector<QPointF> fittedCurve;
    QVector<QPointF> residualCurve;
    QString analysisReport;
    double chiSquared = 0.0;
    double rSquared = 0.0;
    QDateTime timestamp;
    QMap<QString, QVariant> additionalData;
    
    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
    bool isValid() const;
    void clear();
};

// 分析参数
struct AnalysisParameters {
    QString modelType;
    QMap<QString, double> initialValues;
    QMap<QString, QPair<double, double>> limits; // min, max
    QMap<QString, bool> fixedParameters;
    QMap<QString, QVariant> additionalSettings;
    
    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
    bool isValid() const;
    void clear();
};

// SFLP文件头结构
struct SflpFileHeader {
    quint32 magicNumber = 0x534C4650;  // "SFLP"
    quint32 version = 1;
    quint32 headerSize = sizeof(SflpFileHeader);
    quint32 indexOffset = 0;
    quint32 indexSize = 0;
    quint32 dataSegmentCount = 0;
    quint64 totalFileSize = 0;
    quint32 compressionType = 1;  // 1=Qt压缩
    quint32 reserved[16] = {0};   // 扩展保留字段
    
    bool isValid() const;
    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
    void reset();
};

// 数据段索引项
struct DataSegmentIndex {
    QString segmentName;
    quint64 offset = 0;
    quint64 compressedSize = 0;
    quint64 originalSize = 0;
    quint32 checksum = 0;
    QDateTime timestamp;
    
    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
    bool isValid() const;
    void clear();
};

// 工具函数
namespace UnifiedFileUtils {
    QString operationTypeToString(OperationType type);
    OperationType stringToOperationType(const QString& str);
    QString analysisTypeToString(AnalysisType type);
    AnalysisType stringToAnalysisType(const QString& str);
    QString fileTypeToString(FileType type);
    FileType stringToFileType(const QString& str);
    
    quint32 calculateChecksum(const QByteArray& data);
    bool validateFileName(const QString& fileName);
    QString sanitizeFileName(const QString& fileName);
}

#endif // UNIFIEDFILESTRUCTURES_H
