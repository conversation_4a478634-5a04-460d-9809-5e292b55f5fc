#include "OpenProjectWidget.h"
#include <QFileDialog>
#include <QMessageBox>
#include <QFile>
#include <QDataStream>
#include <QDateTime>
#include <QDebug>
#include <QFileInfo>
#include <QStandardItemModel>
#include <QStandardItem>
#include <QHeaderView> // 添加头文件
#include "ProjectFileManager.h"
#include "SfdViewerMainWindow.h"
#include <QTreeView> // 使用 QTreeView 替换 QTableView
#include <fstream>
#include <sstream>
#include <iostream>
#include <QLineEdit>
#include <QProgressDialog>
#include <QTimer>
#include <QMutexLocker>
#include "data/FileNameManager.h"

OpenProjectWidget::OpenProjectWidget(QWidget *parent)
    : QWidget(parent), projectFileManager(ProjectFileManager::getInstance())
{
    // 使用QTreeView和QStandardItemModel替代QTableWidget
    treeModel = new QStandardItemModel(this);
    treeModel->setColumnCount(2); // 初始化列数
    treeModel->setHorizontalHeaderLabels(QStringList() << "File Name" << "File Size"); // 初始化表头标签
    treeView = new QTreeView(this);
    treeView->setModel(treeModel);
    treeView->header()->setSectionResizeMode(QHeaderView::Stretch);
    treeView->setSelectionBehavior(QAbstractItemView::SelectRows);
    treeView->setSelectionMode(QAbstractItemView::SingleSelection);
    // 禁止编辑
    treeView->setEditTriggers(QAbstractItemView::NoEditTriggers);
    // 设置大小策略，确保它不会设置最小宽度
    treeView->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);
    treeView->setMinimumWidth(0); // 显式设置最小宽度为0

    // 隐藏垂直表头以不显示行号
    treeView->header()->setSectionHidden(0, false);

    createProjectButton = new QPushButton("New Workspace");
    openProjectButton = new QPushButton("Open Workspace");

    // 设置按钮的样式类，使其与WorkSpace/Process按钮保持一致
    createProjectButton->setProperty("class", "StandardButton");
    openProjectButton->setProperty("class", "StandardButton");

    // 设置按钮的大小策略，确保它们可以水平拉伸
    createProjectButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    openProjectButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);


    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    QHBoxLayout *buttonLayout1 = new QHBoxLayout();
    QHBoxLayout *buttonLayout2 = new QHBoxLayout();

    buttonLayout1->addWidget(createProjectButton);
    buttonLayout1->addWidget(openProjectButton);
    // 不添加拉伸因子，确保按钮占满整个布局


    mainLayout->addLayout(buttonLayout1);
    mainLayout->addLayout(buttonLayout2);
    mainLayout->addWidget(treeView);

    connect(createProjectButton, &QPushButton::clicked, this, &OpenProjectWidget::onCreateProject, Qt::QueuedConnection);
    connect(openProjectButton, &QPushButton::clicked, this, &OpenProjectWidget::onOpenProject, Qt::QueuedConnection);

    // 连接QTreeView的信号和槽
    // connect(treeView->selectionModel(), &QItemSelectionModel::currentChanged, this, &OpenProjectWidget::onCurrentItemChanged, Qt::QueuedConnection);

    appConfig = AppConfig::instance(); // 获取单例实例

    // 连接fileHandlersChanged信号以刷新表格
    connect(projectFileManager, &ProjectFileManager::fileHandlersChanged, this, &OpenProjectWidget::onShowLocalFiles, Qt::QueuedConnection);
    // 连接文件管理器的路径变化信号
    connect(projectFileManager, &ProjectFileManager::projectInfoChanged, this, &OpenProjectWidget::onShowLocalFiles, Qt::QueuedConnection);

    // 添加右键菜单
    treeView->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(treeView, &QTreeView::customContextMenuRequested, this, &OpenProjectWidget::showContextMenu, Qt::DirectConnection);
}


void OpenProjectWidget::showContextMenu(const QPoint &pos)
{
    QMenu menu(this);
    QAction *openTxtAction = menu.addAction("打开txt测试文件");
    QAction *openCsvAction = menu.addAction("打开csv测试文件");
    QAction *openDataAction = menu.addAction("打开数据文件");
    connect(openDataAction, &QAction::triggered, this, &OpenProjectWidget::onOpenDataFile);

    connect(openTxtAction, &QAction::triggered, this, &OpenProjectWidget::onOpenTxtFile);
    connect(openCsvAction, &QAction::triggered, this, &OpenProjectWidget::onOpenCsvFile);

    // 启用或禁用 openTxtAction 和 openCsvAction
    if (!projectFileManager->getFilePath().isEmpty()) {
        openTxtAction->setEnabled(true);
        openCsvAction->setEnabled(true);
    } else {
        openTxtAction->setEnabled(false);
        openCsvAction->setEnabled(false);
    }

    menu.exec(QCursor::pos());
}

void OpenProjectWidget::onOpenCsvFile()
{
    QString filePath = QFileDialog::getOpenFileName(this, tr("Open CSV File"), "", tr("CSV Files (*.csv);;All Files (*)"));
    if (filePath.isEmpty()) {
        std::cerr << "No file selected" << std::endl;
        return;
    }

    std::vector<std::vector<int>> Data;
    if (!readCSV(filePath, Data)) {
        QMessageBox::warning(this, tr("Error"), tr("Failed to load data"));
        return;
    } else {
        qDebug() << "Data loaded successfully from file: " << filePath;
    }

    // 保存数据到ProjectFileManager
    ProjectFileManager* fileManager = ProjectFileManager::getInstance();
    fileManager->startMeasurement();
    ProjectFileManager::getInstance()->getSavingHandler()->setDataArrayTotal(std::move(Data));
    fileManager->stopMeasurement();
}

bool OpenProjectWidget::readCSV(const QString &filePath, std::vector<std::vector<int> > &data) {
    QFile file(filePath);

    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "Error opening file:" << filePath;
        return false;
    }

    QTextStream in(&file);
    bool isFirstRow = true; // 跳过第一行
    while (!in.atEnd()) {
        QString line = in.readLine();
        if (isFirstRow) {
            isFirstRow = false;
            continue; // 跳过第一行
        }

        QStringList values = line.split(",");
        if (values.size() < 2) {
            continue; // 跳过列数不足的行
        }

        std::vector<int> row;
        for (int i = 1; i < values.size(); ++i) { // 从第二列开始读取
            bool ok;
            int num = values[i].toInt(&ok);
            if (ok) {
                row.push_back(num);
            }
        }

        if (!row.empty()) {
            data.push_back(row);
        }
    }

    file.close();
    return true;
}

bool OpenProjectWidget::loadData(const std::string &filePath, std::vector<std::vector<int> > &data)
{
    std::ifstream inputFile(filePath);
    if (!inputFile) {
        std::cerr << "Error opening file: " << filePath << std::endl;
        return false;
    }

    std::string line;
    while (std::getline(inputFile, line)) {
        std::vector<int> row;
        std::stringstream ss(line);
        unsigned int value;

        while (ss >> value) {
            row.push_back(value);
        }

        if (!row.empty()) {
            data.push_back(row);
        }
    }
    inputFile.close();
    return true;
}

void OpenProjectWidget::onOpenTxtFile()
{
    QString filePath = QFileDialog::getOpenFileName(this, tr("Open File"), AppConfig::instance()->getLastProjectPath(), tr("Text Files (*.txt);;All Files (*)"));
    if (filePath.isEmpty()) {
        std::cerr << "No file selected" << std::endl;
        return;
    }
    std::string stdFilePath = filePath.toStdString();
    std::vector<std::vector<int>> Data;
    if (!loadData(stdFilePath, Data)) {
        std::cerr << "Failed to load data from file: " << stdFilePath << std::endl;
        QMessageBox::warning(this, tr("Error"), tr("Failed to load data"));
        return;
    } else {
        std::cerr << "Data loaded successfully from file: " << stdFilePath << std::endl;
    }

    // 保存数据到ProjectFileManager
    ProjectFileManager* fileManager = ProjectFileManager::getInstance();
    fileManager->startMeasurement();
    ProjectFileManager::getInstance()->getSavingHandler()->setDataArrayTotal(std::move(Data));
    fileManager->stopMeasurement();
}


void OpenProjectWidget::onShowLocalFiles()
{
    qDebug() << "Refreshing tree view in onShowLocalFiles";

    try {
        // 检查模型和视图是否有效
        if (!treeModel || !treeView) {
            qCritical() << "Tree model or tree view is null in onShowLocalFiles";
            return;
        }

        // 断开 currentChanged 信号与槽的连接
        if (treeView->selectionModel()) {
            disconnect(treeView->selectionModel(), &QItemSelectionModel::currentChanged, this, &OpenProjectWidget::onCurrentItemChanged);
        }

        // 强制清除所有数据
        treeModel->clear(); // 清空模型数据
        treeModel->setHorizontalHeaderLabels(QStringList() << "File Name" << "File Size"); // 初始化表头标签

        // 确保树视图也被清空
        treeView->reset();
        treeView->update();
    } catch (const std::exception& e) {
        qCritical() << "Exception in onShowLocalFiles:" << e.what();
    } catch (...) {
        qCritical() << "Unknown exception in onShowLocalFiles";
    }
    QString projectPath = projectFileManager->getFilePath();
    if (projectPath.isEmpty()) {
        // 使用定时器延迟重新连接信号，确保选择模型已经初始化
        QTimer::singleShot(100, this, [this]() {
            if (treeView->selectionModel()) {
                connect(treeView->selectionModel(), &QItemSelectionModel::currentChanged, this, &OpenProjectWidget::onCurrentItemChanged, Qt::QueuedConnection);
            }
        });
        return;
    }

    QFileInfo fileInfo(projectPath);
    QString projectName = fileInfo.fileName();

    QStandardItem *rootItem = new QStandardItem(projectName);
    rootItem->setData(projectName, Qt::DisplayRole); // 设置 Qt::DisplayRole
    treeModel->appendRow(rootItem);

    QDir directory(projectPath);
    QStringList sfdFiles = directory.entryList(QStringList() << "*.sfd", QDir::Files);

    // 获取所有文件处理器
    const auto& fileHandlers = projectFileManager->getFileHandlers();

    // 按文件时间倒序排列
    QList<QPair<QString, QDateTime>> fileTimeList;
    for (const QString &fileName : sfdFiles) {  // 使用范围 for 循环替代 foreach
        QString filePath = directory.filePath(fileName);
        QFileInfo fileInfo(filePath);
        fileTimeList.append(qMakePair(fileName, fileInfo.birthTime()));
    }
    std::sort(fileTimeList.begin(), fileTimeList.end(), [](const QPair<QString, QDateTime> &a, const QPair<QString, QDateTime> &b) {
        return a.second > b.second;
    });

    // 遍历sfdFiles并添加到tree中
    for (const QPair<QString, QDateTime> &fileTimePair : fileTimeList) {  // 使用范围 for 循环替代 foreach
        QString fileName = fileTimePair.first;
        QString filePath = directory.filePath(fileName);
        QFileInfo fileInfo(filePath);

        qint64 size = fileInfo.size();

        // 查找以 fileName + .1/.2/.3/.n 为名字的文件，并累加大小
        QString baseName = fileInfo.completeBaseName();
        QStringList splitFiles = directory.entryList(QStringList() << baseName + ".*", QDir::Files);
        for (const QString &splitFile : splitFiles) {  // 使用范围 for 循环替代 foreach
            QFileInfo splitFileInfo(directory.filePath(splitFile));
            size += splitFileInfo.size();
        }

        QString sizeText = formatFileSize(size); // 使用共通函数

        QStandardItem *item = new QStandardItem(fileName);
        item->setData(fileName, Qt::DisplayRole); // 设置 Qt::DisplayRole
        QStandardItem *sizeItem = new QStandardItem(sizeText);
        sizeItem->setData(sizeText, Qt::DisplayRole); // 设置 Qt::DisplayRole
        rootItem->appendRow(QList<QStandardItem*>() << item << sizeItem);
    }

    // 遍历fileHandlers并添加不在sfdFiles中的文件到tree中
    for (const auto& fileHandler : fileHandlers) {
        QString fileName = fileHandler->getFilePath();
        if (!sfdFiles.contains(QFileInfo(fileName).fileName())) {
            qint64 size = fileHandler->calculateFileSize();
            QString sizeText = formatFileSize(size); // 使用共通函数

            QStandardItem *item = new QStandardItem(QFileInfo(fileName).fileName());
            item->setData(QFileInfo(fileName).fileName(), Qt::DisplayRole); // 设置 Qt::DisplayRole
            QStandardItem *sizeItem = new QStandardItem(sizeText);
            sizeItem->setData(sizeText, Qt::DisplayRole); // 设置 Qt::DisplayRole
            rootItem->appendRow(QList<QStandardItem*>() << item << sizeItem);
        }
    }

    // 调整列宽以适应内容
    treeView->resizeColumnToContents(0);
    treeView->resizeColumnToContents(1);
    treeView->expandAll();

    // 自动选择 MeasureDataHandler* ProjectFileManager::getSavingHandler() 返回的行
    MeasureDataHandler* savingHandler = projectFileManager->getSavingHandler();
    if (savingHandler) {
        QString savingFilePath = savingHandler->getFilePath();
        QFileInfo savingFileInfo(savingFilePath);
        QString savingFileName = savingFileInfo.fileName();

        for (int i = 0; i < rootItem->rowCount(); ++i) {
            QStandardItem* item = rootItem->child(i, 0);
            if (item && item->text() == savingFileName) {
                QModelIndex index = treeModel->indexFromItem(item);
                treeView->setCurrentIndex(index);
                break;
            }
        }
    }

    // 使用定时器延迟重新连接信号，确保数据已经加载完成
    QTimer::singleShot(100, this, [this]() {
        if (treeView->selectionModel()) {
            connect(treeView->selectionModel(), &QItemSelectionModel::currentChanged, this, &OpenProjectWidget::onCurrentItemChanged, Qt::QueuedConnection);
            qDebug() << "Signal reconnected in onShowLocalFiles";
        }
    });
}

void OpenProjectWidget::onCreateProject()
{
    QString path = QFileDialog::getExistingDirectory(this, "选择工程文件夹", appConfig->getLastProjectPath());

    if (!path.isEmpty()) {
        if (!path.endsWith(".sptw")) {
            QString newPath = path + ".sptw";
            // 尝试重命名文件夹
            QDir parentDir = QFileInfo(path).dir();
            newPath = parentDir.filePath(QFileInfo(newPath).fileName());
            if (QDir(newPath).exists()) {
                QMessageBox::StandardButton reply = QMessageBox::question(this, tr("文件夹已存在"), tr("目标文件夹已存在，是否覆盖？"), QMessageBox::Yes|QMessageBox::No);
                if (reply == QMessageBox::No) {
                    return;
                }
            }
            if (!parentDir.rename(QFileInfo(path).fileName(), newPath)) {
                QMessageBox::warning(this, tr("错误"), tr("无法重命名文件夹"));
                return;
            }
            path = newPath;
        }
        QDir dir(path);
        if (!dir.isEmpty()) {
            QMessageBox::warning(this, tr("警告"), tr("选择的文件夹必须为空。"));
            return;
        }
        projectFileManager->createNewProject(path);
        appConfig->setLastProjectPath(path);
    }
}

void OpenProjectWidget::onOpenProject()
{
    try {
        // 检查必要的对象是否存在
        if (!treeModel || !treeView || !appConfig || !projectFileManager) {
            qCritical() << "Required objects are null in onOpenProject";
            return;
        }

        QString path = QFileDialog::getExistingDirectory(this, "Open Project Folder", appConfig->getLastProjectPath());

        if (!path.isEmpty()) {
            qDebug() << "Opening project from path:" << path;

            // 在打开项目前断开信号连接，防止在打开项目时触发点击事件
            if (treeView->selectionModel()) {
                disconnect(treeView->selectionModel(), &QItemSelectionModel::currentChanged, this, &OpenProjectWidget::onCurrentItemChanged);
            }

            // 立即清除树视图中的所有节点
            treeModel->clear();
            treeModel->setHorizontalHeaderLabels(QStringList() << "File Name" << "File Size");
            treeView->reset();
            treeView->update();

            appConfig->setLastProjectPath(path);

            // 创建进度对话框的共享指针
            auto progressPtr = std::make_shared<QProgressDialog>("Loading project...", nullptr, 0, 0, this);
            progressPtr->setWindowModality(Qt::WindowModal);
            progressPtr->setCancelButton(nullptr); // 禁用取消按钮
            progressPtr->show();

            // 处理事件，确保进度对话框显示
            QCoreApplication::processEvents();

            // 连接加载完成信号
            connect(projectFileManager, &ProjectFileManager::projectLoadingFinished, this, [this, progressPtr]() {
                progressPtr->close();
                onShowLocalFiles(); // 更新文件列表

                // 重新连接信号
                if (treeView && treeView->selectionModel()) {
                    connect(treeView->selectionModel(), &QItemSelectionModel::currentChanged,
                            this, &OpenProjectWidget::onCurrentItemChanged, Qt::QueuedConnection);
                }
            }, Qt::QueuedConnection);

            // 连接错误信号
            connect(projectFileManager, &ProjectFileManager::projectLoadingError, this, [this, progressPtr](const QString &errorMessage) {
                progressPtr->close();
                QMessageBox::critical(this, "Error", errorMessage);
            }, Qt::QueuedConnection);

            // 连接警告信号
            connect(projectFileManager, &ProjectFileManager::projectLoadingWarning, this, [this, progressPtr](const QString &warningMessage) {
                progressPtr->close();
                QMessageBox::warning(this, "Warning", warningMessage);
            }, Qt::QueuedConnection);

            // 打开项目
            projectFileManager->openProject(path);
        }
    } catch (const std::exception& e) {
        qCritical() << "Exception in onOpenProject:" << e.what();
        QMessageBox::critical(this, "Error", QString("Exception occurred: %1").arg(e.what()));
    } catch (...) {
        qCritical() << "Unknown exception in onOpenProject";
        QMessageBox::critical(this, "Error", "Unknown exception occurred");
    }
}

void OpenProjectWidget::onSaveProject()
{
    // 创建进度对话框的共享指针
    auto progressPtr = std::make_shared<QProgressDialog>("Saving project...", nullptr, 0, 100, this);
    progressPtr->setWindowModality(Qt::WindowModal);
    progressPtr->setCancelButton(nullptr); // 禁用取消按钮
    progressPtr->show();

    // 连接保存进度信号
    connect(projectFileManager, &ProjectFileManager::saveProgressChanged, progressPtr.get(), &QProgressDialog::setValue);

    // 处理事件，确保进度对话框显示
    QCoreApplication::processEvents();

    // 异步保存项目
    QFuture<void> future = QtConcurrent::run([this]() {
        projectFileManager->saveProject(projectFileManager->getFilePath());
    });

    // 使用 QFutureWatcher 监控保存进度
    auto watcher = new QFutureWatcher<void>(this);
    watcher->setFuture(future);

    connect(watcher, &QFutureWatcher<void>::finished, this, [progressPtr, this, watcher]() {
        progressPtr->close();
        appConfig->addFilePath(projectFileManager->getFilePath());
        watcher->deleteLater(); // 清理 watcher
    });

    // 处理事件，确保进度对话框显示
    while (!future.isFinished()) {
        QCoreApplication::processEvents();
    }
}

void OpenProjectWidget::onCurrentItemChanged(const QModelIndex &current, const QModelIndex &previous)
{
    // 如果正在处理选择操作，则跳过以避免重复处理
    if (m_isProcessingSelection) {
        qDebug() << "Already processing selection in onCurrentItemChanged, skipping";
        return;
    }

    // 添加额外的安全检查
    if (!treeModel || !projectFileManager) {
        qWarning() << "Tree model or project file manager is null in onCurrentItemChanged.";
        return;
    }

    // 检查当前索引是否有效
    if (!current.isValid()) {
        qWarning() << "Invalid index encountered in onCurrentItemChanged.";
        return;
    }

    try {
        // 设置标志位，表示正在处理选择操作
        m_isProcessingSelection = true;

        // 检查是否是项目根节点
        QModelIndex parent = current.parent();
        if (!parent.isValid()) {
            // 这是项目根节点，不处理
            qDebug() << "Root item clicked, ignoring.";
            m_isProcessingSelection = false; // 重置标志位
            return;
        }

        // 获取文件名
        auto data = current.data(Qt::DisplayRole);
        if (!data.isValid()) {
            qWarning() << "Invalid data encountered in onCurrentItemChanged.";
            m_isProcessingSelection = false; // 重置标志位
            return;
        }

        auto fileName = data.toString();
        if (fileName.isEmpty()) {
            qWarning() << "Empty file name in onCurrentItemChanged.";
            m_isProcessingSelection = false; // 重置标志位
            return;
        }

        // 获取完整文件路径
        QString projectPath = projectFileManager->getFilePath();
        if (projectPath.isEmpty()) {
            qWarning() << "Project path is empty.";
            m_isProcessingSelection = false; // 重置标志位
            return;
        }

        QDir directory(projectPath);
        if (!directory.exists()) {
            qWarning() << "Project directory does not exist:" << projectPath;
            m_isProcessingSelection = false; // 重置标志位
            return;
        }

        // 检查文件是否存在
        QString filePath = directory.filePath(fileName);
        QFileInfo fileInfo(filePath);
        if (!fileInfo.exists() && !projectFileManager->getHandler(fileName)) {
            qWarning() << "File does not exist and no handler found:" << filePath;
            m_isProcessingSelection = false; // 重置标志位
            return;
        }

        // 发射信号，传递文件路径和文件名
        qDebug() << "Emitting dataRowClicked for file:" << fileName;
        emit dataRowClicked(current.row(), fileName);

        // 重置标志位
        m_isProcessingSelection = false;
    } catch (const std::exception& e) {
        qCritical() << "Exception in onCurrentItemChanged:" << e.what();
        m_isProcessingSelection = false; // 确保在异常情况下也重置标志位
    } catch (...) {
        qCritical() << "Unknown exception in onCurrentItemChanged";
        m_isProcessingSelection = false; // 确保在异常情况下也重置标志位
    }
}

void OpenProjectWidget::onOpenDataFile() {
    SfdViewerMainWindow *viewer = new SfdViewerMainWindow();
    viewer->show();
}

// 统一文件管理实现
void OpenProjectWidget::refreshFileTreeWithVirtualNodes() {
    QMutexLocker locker(&m_treeMutex);

    try {
        // 保存当前展开状态
        saveTreeState();

        // 清空现有树结构
        treeModel->clear();
        treeModel->setHorizontalHeaderLabels(QStringList() << "File Name" << "File Size");
        m_virtualNodeMap.clear();

        // 获取项目根目录
        QString projectPath = projectFileManager->getFilePath();
        if (projectPath.isEmpty()) {
            return;
        }

        // 构建根节点
        QFileInfo fileInfo(projectPath);
        QString projectName = fileInfo.fileName();

        QStandardItem* rootItem = new QStandardItem(projectName);
        rootItem->setData(projectName, Qt::DisplayRole);
        treeModel->appendRow(rootItem);

        // 获取所有SFLP文件
        QDir directory(projectPath);
        QStringList sflpFiles = directory.entryList(QStringList() << "*.sflp", QDir::Files);

        // 为每个SFLP文件构建树结构
        for (const QString& fileName : sflpFiles) {
            QString filePath = directory.filePath(fileName);
            QStandardItem* fileItem = createFileItem(filePath);
            rootItem->appendRow(fileItem);

            // 添加虚拟子节点
            addVirtualChildren(fileItem, filePath);
        }

        // 应用过滤
        applyCurrentFilter();

        // 恢复展开状态
        restoreTreeState();

        // 展开根节点
        treeView->expand(treeModel->indexFromItem(rootItem));

        logTreeOperation("refreshFileTreeWithVirtualNodes", true);
        emit fileTreeRefreshed();

    } catch (const std::exception& e) {
        qWarning() << "Tree operation error in refreshFileTreeWithVirtualNodes:" << e.what();
        logTreeOperation("refreshFileTreeWithVirtualNodes", false);
    }
}

void OpenProjectWidget::setFileTypeFilter(TabType currentTab) {
    m_currentTabType = currentTab;
    applyCurrentFilter();
    emit filterChanged(currentTab);
}

bool OpenProjectWidget::selectFileInTree(const QString& fileName) {
    QStandardItem* item = findItemByFileName(fileName);
    if (item) {
        selectItem(item);
        return true;
    }
    return false;
}

QString OpenProjectWidget::getCurrentSelectedFile() const {
    QModelIndexList selectedIndexes = treeView->selectionModel()->selectedIndexes();
    if (selectedIndexes.isEmpty()) {
        return QString();
    }

    QModelIndex index = selectedIndexes.first();
    QStandardItem* item = treeModel->itemFromIndex(index);

    if (item) {
        return item->data(Qt::UserRole + 1).toString();
    }

    return QString();
}

// 私有方法实现
void OpenProjectWidget::addVirtualChildren(QStandardItem* parentItem, const QString& sflpFileName) {
    if (!parentItem || sflpFileName.isEmpty()) {
        return;
    }

    try {
        // 获取操作子项
        QStringList operationChildren = projectFileManager->getChildFiles(sflpFileName);

        for (const QString& operationName : operationChildren) {
            FileType fileType = projectFileManager->getFileType(operationName);

            if (fileType == FileType::Operation) {
                QStandardItem* operationItem = createOperationItem(operationName);
                parentItem->appendRow(operationItem);

                // 添加分析子项
                QStringList analysisChildren = projectFileManager->getChildFiles(operationName);
                for (const QString& analysisName : analysisChildren) {
                    FileType analysisType = projectFileManager->getFileType(analysisName);

                    if (analysisType == FileType::DecayAnalysis) {
                        QStandardItem* analysisItem = createAnalysisItem(analysisName, AnalysisType::DecayAnalysis);
                        operationItem->appendRow(analysisItem);
                        m_virtualNodeMap[analysisName] = analysisItem;
                    } else if (analysisType == FileType::SpectralAnalysis) {
                        QStandardItem* analysisItem = createAnalysisItem(analysisName, AnalysisType::SpectralAnalysis);
                        operationItem->appendRow(analysisItem);
                        m_virtualNodeMap[analysisName] = analysisItem;
                    } else if (analysisType == FileType::FluorescenceAnalysis) {
                        QStandardItem* analysisItem = createAnalysisItem(analysisName, AnalysisType::FluorescenceAnalysis);
                        operationItem->appendRow(analysisItem);
                        m_virtualNodeMap[analysisName] = analysisItem;
                    }
                }

                // 注册虚拟节点
                m_virtualNodeMap[operationName] = operationItem;
            }
        }

    } catch (const std::exception& e) {
        qWarning() << "Error in addVirtualChildren:" << e.what();
    }
}

QStandardItem* OpenProjectWidget::createFileItem(const QString& fileName) {
    FileNameManager* nameManager = FileNameManager::getInstance();
    QString displayName = nameManager->generateDisplayName(fileName, FileType::Project);

    QStandardItem* item = new QStandardItem(displayName);
    item->setData(fileName, Qt::UserRole + 1); // 存储完整文件名
    item->setData(FileType::Project, Qt::UserRole);
    item->setIcon(getFileTypeIcon(FileType::Project));
    item->setToolTip(fileName);

    return item;
}

QStandardItem* OpenProjectWidget::createOperationItem(const QString& operationName) {
    FileNameManager* nameManager = FileNameManager::getInstance();
    QString displayName = nameManager->extractOperationDisplayName(operationName);

    QStandardItem* item = new QStandardItem(displayName);
    item->setData(operationName, Qt::UserRole + 1);
    item->setData(FileType::Operation, Qt::UserRole);
    item->setIcon(getFileTypeIcon(FileType::Operation));
    item->setToolTip(QString("Operation: %1").arg(operationName));

    return item;
}

QStandardItem* OpenProjectWidget::createAnalysisItem(const QString& analysisName, AnalysisType type) {
    FileNameManager* nameManager = FileNameManager::getInstance();
    QString displayName = nameManager->extractAnalysisDisplayName(analysisName);

    QStandardItem* item = new QStandardItem(displayName);
    item->setData(analysisName, Qt::UserRole + 1);

    FileType fileType;
    QString typeStr;
    if (type == AnalysisType::DecayAnalysis) {
        fileType = FileType::DecayAnalysis;
        typeStr = "Decay Analysis";
    } else if (type == AnalysisType::SpectralAnalysis) {
        fileType = FileType::SpectralAnalysis;
        typeStr = "Spectral Analysis";
    } else if (type == AnalysisType::FluorescenceAnalysis) {
        fileType = FileType::FluorescenceAnalysis;
        typeStr = "Fluorescence Analysis";
    }

    item->setData(fileType, Qt::UserRole);
    item->setIcon(getFileTypeIcon(fileType));
    item->setToolTip(QString("%1: %2").arg(typeStr, analysisName));

    return item;
}

QIcon OpenProjectWidget::getFileTypeIcon(FileType fileType) const {
    switch (fileType) {
    case FileType::Workspace:
        return QIcon(":/icons/workspace.png");
    case FileType::Project:
        return QIcon(":/icons/project_file.png");
    case FileType::Operation:
        return QIcon(":/icons/operation.png");
    case FileType::DecayAnalysis:
        return QIcon(":/icons/decay_analysis.png");
    case FileType::SpectralAnalysis:
        return QIcon(":/icons/spectral_analysis.png");
    case FileType::FluorescenceAnalysis:
        return QIcon(":/icons/fluorescence_analysis.png");
    case FileType::Split:
        return QIcon(":/icons/split_file.png");
    }
    return QIcon(":/icons/default_file.png");
}

void OpenProjectWidget::applyCurrentFilter() {
    // 简化的过滤实现
    // 根据当前Tab类型显示/隐藏相应的项目
    // 这里可以根据需要实现更复杂的过滤逻辑
}

void OpenProjectWidget::saveTreeState() {
    m_nodeExpandState.clear();
    m_lastSelectedFile = getCurrentSelectedFile();

    // 保存展开状态的简化实现
    // 可以根据需要实现更详细的状态保存
}

void OpenProjectWidget::restoreTreeState() {
    // 恢复展开状态的简化实现
    // 可以根据需要实现更详细的状态恢复

    // 恢复选择
    if (!m_lastSelectedFile.isEmpty()) {
        selectFileInTree(m_lastSelectedFile);
    }
}

QStandardItem* OpenProjectWidget::findItemByFileName(const QString& fileName) const {
    // 递归搜索项目
    for (int i = 0; i < treeModel->rowCount(); ++i) {
        QStandardItem* rootItem = treeModel->item(i);
        if (rootItem) {
            QStandardItem* found = findItemRecursive(rootItem, fileName);
            if (found) {
                return found;
            }
        }
    }
    return nullptr;
}

QStandardItem* OpenProjectWidget::findItemRecursive(QStandardItem* parent, const QString& fileName) const {
    if (!parent) return nullptr;

    // 检查当前项目
    QString itemFileName = parent->data(Qt::UserRole + 1).toString();
    if (itemFileName == fileName) {
        return parent;
    }

    // 递归搜索子项目
    for (int i = 0; i < parent->rowCount(); ++i) {
        QStandardItem* found = findItemRecursive(parent->child(i), fileName);
        if (found) {
            return found;
        }
    }

    return nullptr;
}

void OpenProjectWidget::selectItem(QStandardItem* item) {
    if (!item) return;

    QModelIndex index = treeModel->indexFromItem(item);
    if (index.isValid()) {
        treeView->selectionModel()->select(index, QItemSelectionModel::ClearAndSelect);
        treeView->scrollTo(index, QAbstractItemView::EnsureVisible);
    }
}

void OpenProjectWidget::logTreeOperation(const QString& operation, bool success) {
    if (success) {
        qDebug() << "Tree operation succeeded:" << operation;
    } else {
        qWarning() << "Tree operation failed:" << operation;
    }
}

void OpenProjectWidget::selectFileByName(const QString& fileName, bool emitSignal) {
    // 如果正在处理选择操作，则跳过以避免重复处理
    if (m_isProcessingSelection) {
        qDebug() << "Already processing selection, skipping selectFileByName for" << fileName;
        return;
    }

    // 设置标志位，表示正在处理选择操作
    m_isProcessingSelection = true;

    // 断开信号连接，无论是否需要发出信号
    disconnect(treeView->selectionModel(), &QItemSelectionModel::currentChanged, this, &OpenProjectWidget::onCurrentItemChanged);

    // 获取根节点
    QStandardItem* rootItem = treeModel->item(0);
    if (!rootItem) {
        // 重新连接信号
        connect(treeView->selectionModel(), &QItemSelectionModel::currentChanged, this, &OpenProjectWidget::onCurrentItemChanged, Qt::QueuedConnection);
        m_isProcessingSelection = false; // 重置标志位
        return;
    }

    // 在树中搜索文件
    bool found = false;
    QModelIndex foundIndex;
    for (int i = 0; i < rootItem->rowCount(); ++i) {
        QStandardItem* item = rootItem->child(i, 0);
        if (item && item->text() == fileName) {
            foundIndex = treeModel->indexFromItem(item);
            treeView->setCurrentIndex(foundIndex);
            found = true;
            break;
        }
    }

    // 重新连接信号
    connect(treeView->selectionModel(), &QItemSelectionModel::currentChanged, this, &OpenProjectWidget::onCurrentItemChanged, Qt::QueuedConnection);

    // 如果需要发出信号并且找到了文件，手动发出信号
    if (emitSignal && found) {
        qDebug() << "Emitting dataRowClicked signal for" << fileName;
        emit dataRowClicked(foundIndex.row(), fileName);
    }

    // 重置标志位
    m_isProcessingSelection = false;
}

QString OpenProjectWidget::formatFileSize(qint64 size) {
    if (size < 1024) {
        return QString::number(size) + " Byte";
    } else if (size < 1024 * 1024) {
        return QString::number(size / 1024.0, 'f', 2) + " KByte";
    } else if (size < 1024 * 1024 * 1024) {
        return QString::number(size / (1024.0 * 1024.0), 'f', 2) + " MByte";
    } else {
        return QString::number(size / (1024.0 * 1024.0 * 1024.0), 'f', 2) + " GByte";
    }
}

QString OpenProjectWidget::getSelectedFileName() {
    // 获取当前选中的文件名
    QModelIndex currentIndex = treeView->currentIndex();
    if (!currentIndex.isValid()) {
        return QString();
    }

    // 检查是否是项目根节点
    QModelIndex parent = currentIndex.parent();
    if (!parent.isValid()) {
        // 这是项目根节点，不处理
        return QString();
    }

    // 获取文件名
    auto data = currentIndex.data(Qt::DisplayRole);
    if (!data.isValid()) {
        return QString();
    }

    return data.toString();
}
