#include "ProjectFileManager.h"
#include <QtConcurrent>
#include <QMutexLocker>
#include <QFileInfo>
#include <QDir>
#include <QDebug>
#include <QDateTime>

ProjectFileManager::ProjectFileManager(QObject *parent) : QObject(parent), m_currentVersion(0),
m_lastDataFileNumber(0)
{
    m_fileHandlers = QMap<QString, MeasureDataHandler*>();
    m_currentHandler = nullptr;
    m_projectPath = ""; // 初始化文件路径为空
}

ProjectFileManager::~ProjectFileManager()
{
    // 清理资源
    qDeleteAll(m_fileHandlers);
    m_fileHandlers.clear();
}

bool ProjectFileManager::isProjectInEditing() const
{
    return !m_fileHandlers.isEmpty() || m_currentHandler != nullptr;
}

void ProjectFileManager::createNewProject(const QString &projectPath)
{
    m_projectPath = projectPath;
    m_currentVersion = 0;
    m_lastDataFileNumber = 0;
    qDeleteAll(m_fileHandlers);
    m_fileHandlers.clear();
    emit projectInfoChanged();
}

void ProjectFileManager::saveMetadataFile()
{
    QDir projectDir(m_projectPath);
    if (!projectDir.exists()) {
        projectDir.mkpath(".");
    }
    QFile file(projectDir.filePath("metadata.sfp"));
    if (!file.open(QIODevice::WriteOnly | QIODevice::Truncate)) {
        qWarning() << "Failed to create project file: " << file.fileName();
        return;
    }

    QDataStream out(&file);
    out.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    out << m_currentVersion;
    out << m_lastDataFileNumber;

    file.flush();
    file.close();
}

void ProjectFileManager::saveProject()
{
    saveProject(m_projectPath);
}

void ProjectFileManager::saveProject(const QString &filePath)
{
    m_projectPath = filePath;
    QDir projectDir(m_projectPath);
    saveMetadataFile();

    int totalFiles = m_fileHandlers.size();
    int currentFile = 0;

    for (auto it = m_fileHandlers.begin(); it != m_fileHandlers.end(); ++it) {
        if (!it.value()->saveProject()) {
            qCritical() << "Failed to serialize data for file:" << projectDir.filePath(it.key());
        }

        currentFile++;
        int progress = (currentFile * 100) / totalFiles;
        emit saveProgressChanged(progress);
    }
}

void ProjectFileManager::openProject(const QString &filePath)
{
    // 先清除前一个项目的所有数据
    qDebug() << "Clearing previous project data before opening new project";
    clearCurrentHandler(); // 清除当前处理器

    // 清除所有文件处理器
    qDeleteAll(m_fileHandlers);
    m_fileHandlers.clear();

    // 重置其他状态
    m_currentVersion = 0;
    m_lastDataFileNumber = 0;

    // 设置新项目路径
    m_projectPath = filePath;
    QDir projectDir(filePath);
    QFile metaFile(projectDir.filePath("metadata.sfp"));
    if (!metaFile.open(QIODevice::ReadOnly)) {
        qDebug() << "No metadata file found, creating new project";
        this->createNewProject(filePath);
        return;
    }

    QDataStream in(&metaFile);
    in.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    in >> m_currentVersion;
    in >> m_lastDataFileNumber;
    metaFile.close();

    // 通知项目信息已更改
    emit projectInfoChanged();

    QStringList sfdFiles = projectDir.entryList(QStringList() << "*.sfd", QDir::Files);
    qDebug() << "Found" << sfdFiles.size() << "SFD files in project directory";

    // 解析文件名中的数字并更新m_lastDataFileNumber
    int maxNumber = m_lastDataFileNumber;
    QRegularExpression re("_(\\d+)\\.sfd$");
    foreach (const QString &fileName, sfdFiles) {
        QRegularExpressionMatch match = re.match(fileName);
        if (match.hasMatch()) {
            bool ok;
            int number = match.captured(1).toInt(&ok);
            if (ok && number > maxNumber) {
                maxNumber = number;
            }
        }
    }
    m_lastDataFileNumber = maxNumber;

    // 设置加载状态标志
    m_isLoading = true;

    // 异步加载文件，但确保所有 UI 操作都通过信号传递到主线程
    QtConcurrent::run([this, projectDir, sfdFiles]() {
        try {
            int successCount = 0;
            int totalFiles = sfdFiles.size();

            for (const QString &fileName : sfdFiles) {
                try {
                    QFile dataFile(projectDir.filePath(fileName));
                    if (dataFile.open(QIODevice::ReadOnly)) {
                        MeasureDataHandler* handler = new MeasureDataHandler();
                        if (handler->openProject(projectDir.filePath(fileName))) {
                            // 使用 QMetaObject::invokeMethod 在主线程中安全地更新 UI
                            QMetaObject::invokeMethod(this, [this, fileName, handler]() {
                                m_fileHandlers.insert(fileName, handler);
                                emit fileHandlersChanged();
                            }, Qt::QueuedConnection);
                            successCount++;
                        } else {
                            delete handler;
                            qWarning() << "Failed to open project file:" << fileName;
                        }
                    }
                } catch (const std::exception& e) {
                    qCritical() << "Exception loading file" << fileName << ":" << e.what();
                } catch (...) {
                    qCritical() << "Unknown exception loading file" << fileName;
                }
            }

            // 使用 QMetaObject::invokeMethod 在主线程中安全地发出信号
            QMetaObject::invokeMethod(this, [this, successCount, totalFiles]() {
                if (successCount == 0 && totalFiles > 0) {
                    // 所有文件加载失败，发出错误信号
                    emit projectLoadingError("All project files failed to load");
                } else if (successCount < totalFiles) {
                    // 部分文件加载失败，发出警告信号
                    emit projectLoadingWarning(QString("Some project files failed to load (%1/%2)").arg(successCount).arg(totalFiles));
                }

                m_isLoading = false;
                emit projectLoadingFinished();
            }, Qt::QueuedConnection);
        } catch (const std::exception& e) {
            qCritical() << "Exception in project loading:" << e.what();

            // 使用 QMetaObject::invokeMethod 在主线程中安全地发出信号
            QMetaObject::invokeMethod(this, [this, e]() {
                m_isLoading = false;
                emit projectLoadingError(QString("Project loading exception: %1").arg(e.what()));
            }, Qt::QueuedConnection);
        } catch (...) {
            qCritical() << "Unknown exception in project loading";

            // 使用 QMetaObject::invokeMethod 在主线程中安全地发出信号
            QMetaObject::invokeMethod(this, [this]() {
                m_isLoading = false;
                emit projectLoadingError("Unknown exception during project loading");
            }, Qt::QueuedConnection);
        }
    });
}

QString ProjectFileManager::getFilePath() const
{
    return m_projectPath;
}

void ProjectFileManager::setFilePath(const QString &filePath)
{
    m_projectPath = filePath;
}

void ProjectFileManager::createNewVersion()
{
    ++m_currentVersion;
    for (auto handler : m_fileHandlers.values()) {
        handler->createSnapshot(m_currentVersion);
    }
}

void ProjectFileManager::switchToVersion(int version)
{
    if (version < 0 || version > m_currentVersion) {
        qWarning() << "Invalid version number:" << version;
        return;
    }

    for (auto handler : m_fileHandlers.values()) {
        handler->restoreSnapshot(version);
    }
}

// 静态成员初始化
QMutex ProjectFileManager::mutex;
ProjectFileManager* ProjectFileManager::instance = nullptr;

// 测量管理功能实现
MeasureDataHandler* ProjectFileManager::getSavingHandler() const {
    return m_currentHandler;
}

MeasureDataHandler* ProjectFileManager::getHandler(const QString &fileName) {
    // 检查文件名是否为空
    if (fileName.isEmpty()) {
        qWarning() << "FileName is empty, cannot get handler.";
        return nullptr;
    }

    // 检查文件处理器是否存在
    if (!m_fileHandlers.contains(fileName)) {
        qWarning() << "Handler for file" << fileName << "does not exist.";
        return nullptr;
    }

    return m_fileHandlers.value(fileName);
}

void ProjectFileManager::startMeasurement() {
    QDir projectDir(m_projectPath);
    QFile file(projectDir.filePath("metadata.sfp"));
    if (!file.exists()) {
        saveMetadataFile();
    }

    qDebug() << "Starting measurement. Clearing current handler and creating a new one.";
    clearCurrentHandler();

    qDebug() << "Appending current handler to file handlers and clearing it.";
    m_lastDataFileNumber++;
    QString fileName = AppConfig::instance()->readConfig("DataFileName", "SpecFlimData").toString()
        + "_" + QString::number(m_lastDataFileNumber) + ".sfd";

    m_currentHandler = new MeasureDataHandler();
    m_currentHandler->start(projectDir.filePath(fileName));
    m_currentHandler->saveProject();

    m_fileHandlers.insert(fileName, m_currentHandler);
    emit fileHandlersChanged();
}

void ProjectFileManager::stopMeasurement() {
    if (m_currentHandler) {
        // 调用stop方法记录结束时间
        m_currentHandler->stop();

        // 记录测量持续时间
        qint64 duration = m_currentHandler->getMeasurementDuration();
        qDebug() << "Measurement stopped. Duration:" << duration << "ms";

        // 保存当前处理器的引用
        MeasureDataHandler* handler = m_currentHandler;

        // 在主线程中设置 m_currentHandler 为 nullptr
        m_currentHandler = nullptr;

        // 在工作线程中保存项目
        QtConcurrent::run([handler]() {
            handler->saveProject();
            // 不要在工作线程中修改 m_currentHandler
        });
    } else {
        qDebug() << "Stop measurement called, but there is no current handler.";
    }
}

void ProjectFileManager::abortMeasurement() {
    qDebug() << "Aborting measurement. Calling stopMeasurement.";
    stopMeasurement();
}

void ProjectFileManager::resetMeasurement() {
    qDebug() << "Resetting measurement. Clearing current handler and starting a new measurement.";

    // 如果当前有测量在进行，先调用stop方法记录结束时间
    if (m_currentHandler && m_currentHandler->isSaving()) {
        m_currentHandler->stop();

        // 记录测量持续时间
        qint64 duration = m_currentHandler->getMeasurementDuration();
        qDebug() << "Measurement reset. Duration before reset:" << duration << "ms";
    }

    clearCurrentHandler();
}

void ProjectFileManager::clearCurrentHandler() {
    if (m_currentHandler && m_currentHandler->isSaving()) {
        // 获取文件路径
        QString filePath = m_currentHandler->filePath;
        // 提取文件名
        QFileInfo fileInfo(filePath);
        QString fileName = fileInfo.fileName();

        // 从 m_fileHandlers 中删除该项
        if (m_fileHandlers.contains(fileName)) {
            delete m_fileHandlers.take(fileName);
        }
        m_currentHandler = nullptr;
    }
}

ProjectFileManager* ProjectFileManager::getInstance(QObject *parent)
{
    QMutexLocker locker(&mutex);
    if (instance == nullptr) {
        instance = new ProjectFileManager(parent);
    }
    return instance;
}

// 统一文件管理实现
bool ProjectFileManager::saveOperationData(const QString& sflpFileName,
                                           const OperationSequence& sequence,
                                           const PlotDataCollection& plotData) {
    if (sflpFileName.isEmpty() || sequence.isEmpty() || !validateOperationData(plotData)) {
        qWarning() << "Invalid parameters for saveOperationData";
        return false;
    }

    try {
        // 1. 生成操作名称
        FileNameManager* nameManager = FileNameManager::getInstance();
        QString operationName = nameManager->generateOperationName(sflpFileName, sequence);

        if (operationName.isEmpty()) {
            qWarning() << "Failed to generate operation name";
            return false;
        }

        // 2. 序列化和压缩数据
        QByteArray plotDataBytes = plotData.serialize();
        if (plotDataBytes.isEmpty()) {
            qWarning() << "Failed to serialize operation data";
            return false;
        }

        QByteArray compressedPlotData = SflpFileManager::compressData(plotDataBytes);

        // 3. 保存到SFLP文件
        QString dataSegmentName = generateOperationSegmentName(operationName) + "_data";
        bool success = writeDataSegmentToSflp(sflpFileName, dataSegmentName, compressedPlotData);

        if (success) {
            // 4. 更新文件关系和计数器
            updateFileRelationship(sflpFileName, operationName);
            updateOperationCounter(sflpFileName, sequence.operations.last().first,
                                 sequence.operations.last().second);

            logFileOperation("saveOperationData", operationName, true);
            emit operationDataSaved(operationName);
            return true;
        } else {
            qWarning() << "Failed to save operation data to SFLP file";
        }

    } catch (const std::exception& e) {
        qCritical() << "Exception in saveOperationData:" << e.what();
        logFileOperation("saveOperationData", sflpFileName, false);
    }

    return false;
}
bool ProjectFileManager::saveAnalysisData(const QString& sflpFileName,
                                          const QString& baseOperationName,
                                          AnalysisType analysisType,
                                          const AnalysisResults& results) {
    if (sflpFileName.isEmpty() || baseOperationName.isEmpty() || !validateAnalysisData(results)) {
        qWarning() << "Invalid parameters for saveAnalysisData";
        return false;
    }

    try {
        // 1. 生成分析序列号和名称
        FileNameManager* nameManager = FileNameManager::getInstance();
        int sequence = nameManager->getNextAnalysisSequence(sflpFileName, analysisType);

        QString analysisName;
        if (analysisType == AnalysisType::DecayAnalysis) {
            analysisName = nameManager->generateDecayAnalysisName(baseOperationName, sequence);
        } else if (analysisType == AnalysisType::SpectralAnalysis) {
            analysisName = nameManager->generateSpectralAnalysisName(baseOperationName, sequence);
        } else if (analysisType == AnalysisType::FluorescenceAnalysis) {
            analysisName = nameManager->generateFluorescenceAnalysisName(baseOperationName, sequence);
        }

        if (analysisName.isEmpty()) {
            qWarning() << "Failed to generate analysis name";
            return false;
        }

        // 2. 序列化和压缩数据
        QByteArray resultsBytes = results.serialize();
        if (resultsBytes.isEmpty()) {
            qWarning() << "Failed to serialize analysis results";
            return false;
        }

        QByteArray compressedResults = SflpFileManager::compressData(resultsBytes);

        // 3. 保存到SFLP文件
        QString segmentName = generateAnalysisSegmentName(analysisName) + "_results";
        bool success = writeDataSegmentToSflp(sflpFileName, segmentName, compressedResults);

        if (success) {
            // 4. 更新文件关系和计数器
            updateFileRelationship(baseOperationName, analysisName);
            updateAnalysisCounter(sflpFileName, analysisType, sequence);

            logFileOperation("saveAnalysisData", analysisName, true);
            emit analysisDataSaved(analysisName);
            return true;
        } else {
            qWarning() << "Failed to save analysis data to SFLP file";
        }

    } catch (const std::exception& e) {
        qCritical() << "Exception in saveAnalysisData:" << e.what();
        logFileOperation("saveAnalysisData", sflpFileName, false);
    }

    return false;
}

// 文件关系管理实现
QStringList ProjectFileManager::getChildFiles(const QString& parentFile) const {
    QMutexLocker locker(&m_relationMutex);
    return m_childrenMap.value(parentFile, QStringList());
}

QString ProjectFileManager::getParentFile(const QString& childFile) const {
    QMutexLocker locker(&m_relationMutex);
    return m_parentMap.value(childFile, QString());
}

FileType ProjectFileManager::getFileType(const QString& fileName) const {
    FileNameManager* nameManager = FileNameManager::getInstance();
    return nameManager->identifyFileType(fileName);
}

// 操作计数器管理实现
OperationCounters ProjectFileManager::getOperationCounters(const QString& sflpFileName) const {
    QMutexLocker locker(&m_counterMutex);

    if (!m_sflpCounters.contains(sflpFileName)) {
        // 从文件加载计数器
        const_cast<ProjectFileManager*>(this)->loadCountersFromFile(sflpFileName);
    }

    return m_sflpCounters.value(sflpFileName, OperationCounters());
}

void ProjectFileManager::updateOperationCounter(const QString& sflpFileName,
                                               OperationType type, int newValue) {
    QMutexLocker locker(&m_counterMutex);

    if (!m_sflpCounters.contains(sflpFileName)) {
        loadCountersFromFile(sflpFileName);
    }

    m_sflpCounters[sflpFileName].setCounter(type, newValue);
    saveCountersToFile(sflpFileName);

    emit operationCounterUpdated(sflpFileName, type, newValue);
}

void ProjectFileManager::updateAnalysisCounter(const QString& sflpFileName,
                                              AnalysisType type, int newValue) {
    QMutexLocker locker(&m_counterMutex);

    if (!m_sflpCounters.contains(sflpFileName)) {
        loadCountersFromFile(sflpFileName);
    }

    m_sflpCounters[sflpFileName].setCounter(type, newValue);
    saveCountersToFile(sflpFileName);

    emit analysisCounterUpdated(sflpFileName, type, newValue);
}

// 私有方法实现
bool ProjectFileManager::writeDataSegmentToSflp(const QString& sflpFileName,
                                                const QString& segmentName,
                                                const QByteArray& data) {
    SflpFileManager* manager = getSflpManager(sflpFileName);
    if (!manager) {
        qWarning() << "Failed to get SFLP manager for file:" << sflpFileName;
        return false;
    }

    if (!manager->isOpen()) {
        if (!manager->openFile(QIODevice::ReadWrite)) {
            qWarning() << "Failed to open SFLP file:" << sflpFileName;
            releaseSflpManager(sflpFileName);
            return false;
        }
    }

    bool success = manager->writeDataSegment(segmentName, data);
    if (!success) {
        qWarning() << "Failed to write data segment:" << segmentName
                   << "Error:" << manager->getLastError();
    }

    return success;
}

QByteArray ProjectFileManager::readDataSegmentFromSflp(const QString& sflpFileName,
                                                       const QString& segmentName) {
    SflpFileManager* manager = getSflpManager(sflpFileName);
    if (!manager) {
        qWarning() << "Failed to get SFLP manager for file:" << sflpFileName;
        return QByteArray();
    }

    if (!manager->isOpen()) {
        if (!manager->openFile(QIODevice::ReadOnly)) {
            qWarning() << "Failed to open SFLP file:" << sflpFileName;
            releaseSflpManager(sflpFileName);
            return QByteArray();
        }
    }

    QByteArray data = manager->readDataSegment(segmentName);
    if (data.isEmpty() && manager->hasError()) {
        qWarning() << "Failed to read data segment:" << segmentName
                   << "Error:" << manager->getLastError();
    }

    return data;
}

void ProjectFileManager::updateFileRelationship(const QString& parent, const QString& child) {
    QMutexLocker locker(&m_relationMutex);

    // 添加父子关系
    if (!m_childrenMap[parent].contains(child)) {
        m_childrenMap[parent].append(child);
    }
    m_parentMap[child] = parent;

    emit fileRelationshipChanged();
}

SflpFileManager* ProjectFileManager::getSflpManager(const QString& sflpFileName) {
    QMutexLocker locker(&m_managerMutex);

    if (!m_sflpManagers.contains(sflpFileName)) {
        SflpFileManager* manager = new SflpFileManager(sflpFileName, this);
        m_sflpManagers[sflpFileName] = manager;

        // 连接信号
        connect(manager, &SflpFileManager::errorOccurred,
                this, [this, sflpFileName](const QString& error) {
                    emit sflpFileCorrupted(sflpFileName, error);
                });
    }

    return m_sflpManagers[sflpFileName];
}

void ProjectFileManager::releaseSflpManager(const QString& sflpFileName) {
    QMutexLocker locker(&m_managerMutex);

    if (m_sflpManagers.contains(sflpFileName)) {
        SflpFileManager* manager = m_sflpManagers[sflpFileName];
        manager->closeFile();
        // 不删除管理器，保持缓存以提高性能
    }
}

void ProjectFileManager::loadCountersFromFile(const QString& sflpFileName) {
    // 从SFLP文件加载计数器数据
    QString segmentName = "operation_counters";
    QByteArray counterData = readDataSegmentFromSflp(sflpFileName, segmentName);

    OperationCounters counters;
    if (!counterData.isEmpty()) {
        QByteArray rawData = SflpFileManager::decompressData(counterData);
        if (!rawData.isEmpty()) {
            counters.deserialize(rawData);
        }
    }

    m_sflpCounters[sflpFileName] = counters;
}

void ProjectFileManager::saveCountersToFile(const QString& sflpFileName) {
    if (!m_sflpCounters.contains(sflpFileName)) {
        return;
    }

    const OperationCounters& counters = m_sflpCounters[sflpFileName];
    QByteArray rawData = counters.serialize();
    QByteArray compressedData = SflpFileManager::compressData(rawData);

    QString segmentName = "operation_counters";
    writeDataSegmentToSflp(sflpFileName, segmentName, compressedData);
}

bool ProjectFileManager::validateOperationData(const PlotDataCollection& plotData) const {
    return plotData.isValid() && plotData.calculateSize() > 0;
}

bool ProjectFileManager::validateAnalysisData(const AnalysisResults& results) const {
    return results.isValid();
}

QString ProjectFileManager::extractBaseOperationName(const QString& analysisName) const {
    // 从分析名称中提取基础操作名称
    // 例如：从 "SFL240101_001_al1_cr2_DecAna_1" 提取 "SFL240101_001_al1_cr2"
    QRegularExpression re("^(.+)_(DecAna|SpeAna|FluAna)_\\d+$");
    QRegularExpressionMatch match = re.match(analysisName);

    if (match.hasMatch()) {
        return match.captured(1);
    }

    return analysisName;
}

QString ProjectFileManager::generateOperationSegmentName(const QString& operationName) const {
    return QString("op_%1").arg(operationName);
}

QString ProjectFileManager::generateAnalysisSegmentName(const QString& analysisName) const {
    return QString("ana_%1").arg(analysisName);
}

void ProjectFileManager::logFileOperation(const QString& operation,
                                         const QString& fileName,
                                         bool success) {
    if (success) {
        qDebug() << "File operation succeeded:" << operation << "on" << fileName;
    } else {
        qWarning() << "File operation failed:" << operation << "on" << fileName;
    }
}

PlotDataCollection ProjectFileManager::loadOperationData(const QString& operationName) {
    PlotDataCollection plotData;

    if (operationName.isEmpty()) {
        qWarning() << "Invalid operation name";
        return plotData;
    }

    try {
        // 1. 获取父文件
        QString parentFile = getParentFile(operationName);
        if (parentFile.isEmpty()) {
            qWarning() << "Parent file not found for operation:" << operationName;
            return plotData;
        }

        // 2. 从SFLP文件读取压缩数据
        QString segmentName = generateOperationSegmentName(operationName) + "_data";
        QByteArray compressedData = readDataSegmentFromSflp(parentFile, segmentName);

        if (compressedData.isEmpty()) {
            qWarning() << "Failed to load operation data from SFLP file";
            return plotData;
        }

        // 3. 解压缩数据
        QByteArray rawData = SflpFileManager::decompressData(compressedData);
        if (rawData.isEmpty()) {
            qWarning() << "Failed to decompress operation data";
            return plotData;
        }

        // 4. 反序列化数据
        if (!plotData.deserialize(rawData)) {
            qWarning() << "Failed to deserialize plot data";
            plotData.clear();
            return plotData;
        }

        logFileOperation("loadOperationData", operationName, true);

    } catch (const std::exception& e) {
        qCritical() << "Exception in loadOperationData:" << e.what();
        plotData.clear();
        logFileOperation("loadOperationData", operationName, false);
    }

    return plotData;
}

AnalysisResults ProjectFileManager::loadAnalysisData(const QString& analysisName) {
    AnalysisResults results;

    if (analysisName.isEmpty()) {
        qWarning() << "Invalid analysis name";
        return results;
    }

    try {
        // 1. 获取基础操作名称和父文件
        QString baseOperation = extractBaseOperationName(analysisName);
        QString parentFile = getParentFile(baseOperation);

        if (parentFile.isEmpty()) {
            qWarning() << "Parent file not found for analysis:" << analysisName;
            return results;
        }

        // 2. 读取分析结果
        QString segmentName = generateAnalysisSegmentName(analysisName) + "_results";
        QByteArray compressedResults = readDataSegmentFromSflp(parentFile, segmentName);

        if (compressedResults.isEmpty()) {
            qWarning() << "Failed to load analysis data from SFLP file";
            return results;
        }

        // 3. 解压缩数据
        QByteArray rawResults = SflpFileManager::decompressData(compressedResults);
        if (rawResults.isEmpty()) {
            qWarning() << "Failed to decompress analysis data";
            return results;
        }

        // 4. 反序列化分析结果
        if (!results.deserialize(rawResults)) {
            qWarning() << "Failed to deserialize analysis results";
            results.clear();
            return results;
        }

        logFileOperation("loadAnalysisData", analysisName, true);

    } catch (const std::exception& e) {
        qCritical() << "Exception in loadAnalysisData:" << e.what();
        results.clear();
        logFileOperation("loadAnalysisData", analysisName, false);
    }

    return results;
}

void ProjectFileManager::createAndAppendDataFrame(quint16 frameNumber, quint32 pulseCount, const std::vector<std::vector<int>> &data)
{
    if (m_currentHandler) {
        m_currentHandler->createAndAppendDataFrame(frameNumber, pulseCount, data);

        emit fileHandlersChanged();
    }
}
