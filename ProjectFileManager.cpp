#include "ProjectFileManager.h"
#include <QtConcurrent>

ProjectFileManager::ProjectFileManager(QObject *parent) : QObject(parent), m_currentVersion(0),
m_lastDataFileNumber(0)
{
    m_fileHandlers = QMap<QString, MeasureDataHandler*>();
    m_currentHandler = nullptr;
    m_projectPath = ""; // 初始化文件路径为空
}

ProjectFileManager::~ProjectFileManager()
{
    // 清理资源
    qDeleteAll(m_fileHandlers);
    m_fileHandlers.clear();
}

bool ProjectFileManager::isProjectInEditing() const
{
    return !m_fileHandlers.isEmpty() || m_currentHandler != nullptr;
}

void ProjectFileManager::createNewProject(const QString &projectPath)
{
    m_projectPath = projectPath;
    m_currentVersion = 0;
    m_lastDataFileNumber = 0;
    qDeleteAll(m_fileHandlers);
    m_fileHandlers.clear();
    emit projectInfoChanged();
}

void ProjectFileManager::saveMetadataFile()
{
    QDir projectDir(m_projectPath);
    if (!projectDir.exists()) {
        projectDir.mkpath(".");
    }
    QFile file(projectDir.filePath("metadata.sfp"));
    if (!file.open(QIODevice::WriteOnly | QIODevice::Truncate)) {
        qWarning() << "Failed to create project file: " << file.fileName();
        return;
    }

    QDataStream out(&file);
    out.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    out << m_currentVersion;
    out << m_lastDataFileNumber;

    file.flush();
    file.close();
}

void ProjectFileManager::saveProject()
{
    saveProject(m_projectPath);
}

void ProjectFileManager::saveProject(const QString &filePath)
{
    m_projectPath = filePath;
    QDir projectDir(m_projectPath);
    saveMetadataFile();

    int totalFiles = m_fileHandlers.size();
    int currentFile = 0;

    for (auto it = m_fileHandlers.begin(); it != m_fileHandlers.end(); ++it) {
        if (!it.value()->saveProject()) {
            qCritical() << "Failed to serialize data for file:" << projectDir.filePath(it.key());
        }

        currentFile++;
        int progress = (currentFile * 100) / totalFiles;
        emit saveProgressChanged(progress);
    }
}

void ProjectFileManager::openProject(const QString &filePath)
{
    // 先清除前一个项目的所有数据
    qDebug() << "Clearing previous project data before opening new project";
    clearCurrentHandler(); // 清除当前处理器

    // 清除所有文件处理器
    qDeleteAll(m_fileHandlers);
    m_fileHandlers.clear();

    // 重置其他状态
    m_currentVersion = 0;
    m_lastDataFileNumber = 0;

    // 设置新项目路径
    m_projectPath = filePath;
    QDir projectDir(filePath);
    QFile metaFile(projectDir.filePath("metadata.sfp"));
    if (!metaFile.open(QIODevice::ReadOnly)) {
        qDebug() << "No metadata file found, creating new project";
        this->createNewProject(filePath);
        return;
    }

    QDataStream in(&metaFile);
    in.setVersion(QDataStream::Qt_DefaultCompiledVersion);

    in >> m_currentVersion;
    in >> m_lastDataFileNumber;
    metaFile.close();

    // 通知项目信息已更改
    emit projectInfoChanged();

    QStringList sfdFiles = projectDir.entryList(QStringList() << "*.sfd", QDir::Files);
    qDebug() << "Found" << sfdFiles.size() << "SFD files in project directory";

    // 解析文件名中的数字并更新m_lastDataFileNumber
    int maxNumber = m_lastDataFileNumber;
    QRegularExpression re("_(\\d+)\\.sfd$");
    foreach (const QString &fileName, sfdFiles) {
        QRegularExpressionMatch match = re.match(fileName);
        if (match.hasMatch()) {
            bool ok;
            int number = match.captured(1).toInt(&ok);
            if (ok && number > maxNumber) {
                maxNumber = number;
            }
        }
    }
    m_lastDataFileNumber = maxNumber;

    // 设置加载状态标志
    m_isLoading = true;

    // 异步加载文件，但确保所有 UI 操作都通过信号传递到主线程
    QtConcurrent::run([this, projectDir, sfdFiles]() {
        try {
            int successCount = 0;
            int totalFiles = sfdFiles.size();

            for (const QString &fileName : sfdFiles) {
                try {
                    QFile dataFile(projectDir.filePath(fileName));
                    if (dataFile.open(QIODevice::ReadOnly)) {
                        MeasureDataHandler* handler = new MeasureDataHandler();
                        if (handler->openProject(projectDir.filePath(fileName))) {
                            // 使用 QMetaObject::invokeMethod 在主线程中安全地更新 UI
                            QMetaObject::invokeMethod(this, [this, fileName, handler]() {
                                m_fileHandlers.insert(fileName, handler);
                                emit fileHandlersChanged();
                            }, Qt::QueuedConnection);
                            successCount++;
                        } else {
                            delete handler;
                            qWarning() << "Failed to open project file:" << fileName;
                        }
                    }
                } catch (const std::exception& e) {
                    qCritical() << "Exception loading file" << fileName << ":" << e.what();
                } catch (...) {
                    qCritical() << "Unknown exception loading file" << fileName;
                }
            }

            // 使用 QMetaObject::invokeMethod 在主线程中安全地发出信号
            QMetaObject::invokeMethod(this, [this, successCount, totalFiles]() {
                if (successCount == 0 && totalFiles > 0) {
                    // 所有文件加载失败，发出错误信号
                    emit projectLoadingError("All project files failed to load");
                } else if (successCount < totalFiles) {
                    // 部分文件加载失败，发出警告信号
                    emit projectLoadingWarning(QString("Some project files failed to load (%1/%2)").arg(successCount).arg(totalFiles));
                }

                m_isLoading = false;
                emit projectLoadingFinished();
            }, Qt::QueuedConnection);
        } catch (const std::exception& e) {
            qCritical() << "Exception in project loading:" << e.what();

            // 使用 QMetaObject::invokeMethod 在主线程中安全地发出信号
            QMetaObject::invokeMethod(this, [this, e]() {
                m_isLoading = false;
                emit projectLoadingError(QString("Project loading exception: %1").arg(e.what()));
            }, Qt::QueuedConnection);
        } catch (...) {
            qCritical() << "Unknown exception in project loading";

            // 使用 QMetaObject::invokeMethod 在主线程中安全地发出信号
            QMetaObject::invokeMethod(this, [this]() {
                m_isLoading = false;
                emit projectLoadingError("Unknown exception during project loading");
            }, Qt::QueuedConnection);
        }
    });
}

QString ProjectFileManager::getFilePath() const
{
    return m_projectPath;
}

void ProjectFileManager::setFilePath(const QString &filePath)
{
    m_projectPath = filePath;
}

void ProjectFileManager::createNewVersion()
{
    ++m_currentVersion;
    for (auto handler : m_fileHandlers.values()) {
        handler->createSnapshot(m_currentVersion);
    }
}

void ProjectFileManager::switchToVersion(int version)
{
    if (version < 0 || version > m_currentVersion) {
        qWarning() << "Invalid version number:" << version;
        return;
    }

    for (auto handler : m_fileHandlers.values()) {
        handler->restoreSnapshot(version);
    }
}

// 静态成员初始化
QMutex ProjectFileManager::mutex;
ProjectFileManager* ProjectFileManager::instance = nullptr;

// 测量管理功能实现
MeasureDataHandler* ProjectFileManager::getSavingHandler() const {
    return m_currentHandler;
}

MeasureDataHandler* ProjectFileManager::getHandler(const QString &fileName) {
    // 检查文件名是否为空
    if (fileName.isEmpty()) {
        qWarning() << "FileName is empty, cannot get handler.";
        return nullptr;
    }

    // 检查文件处理器是否存在
    if (!m_fileHandlers.contains(fileName)) {
        qWarning() << "Handler for file" << fileName << "does not exist.";
        return nullptr;
    }

    return m_fileHandlers.value(fileName);
}

void ProjectFileManager::startMeasurement() {
    QDir projectDir(m_projectPath);
    QFile file(projectDir.filePath("metadata.sfp"));
    if (!file.exists()) {
        saveMetadataFile();
    }

    qDebug() << "Starting measurement. Clearing current handler and creating a new one.";
    clearCurrentHandler();

    qDebug() << "Appending current handler to file handlers and clearing it.";
    m_lastDataFileNumber++;
    QString fileName = AppConfig::instance()->readConfig("DataFileName", "SpecFlimData").toString()
        + "_" + QString::number(m_lastDataFileNumber) + ".sfd";

    m_currentHandler = new MeasureDataHandler();
    m_currentHandler->start(projectDir.filePath(fileName));
    m_currentHandler->saveProject();

    m_fileHandlers.insert(fileName, m_currentHandler);
    emit fileHandlersChanged();
}

void ProjectFileManager::stopMeasurement() {
    if (m_currentHandler) {
        // 调用stop方法记录结束时间
        m_currentHandler->stop();

        // 记录测量持续时间
        qint64 duration = m_currentHandler->getMeasurementDuration();
        qDebug() << "Measurement stopped. Duration:" << duration << "ms";

        // 保存当前处理器的引用
        MeasureDataHandler* handler = m_currentHandler;

        // 在主线程中设置 m_currentHandler 为 nullptr
        m_currentHandler = nullptr;

        // 在工作线程中保存项目
        QtConcurrent::run([handler]() {
            handler->saveProject();
            // 不要在工作线程中修改 m_currentHandler
        });
    } else {
        qDebug() << "Stop measurement called, but there is no current handler.";
    }
}

void ProjectFileManager::abortMeasurement() {
    qDebug() << "Aborting measurement. Calling stopMeasurement.";
    stopMeasurement();
}

void ProjectFileManager::resetMeasurement() {
    qDebug() << "Resetting measurement. Clearing current handler and starting a new measurement.";

    // 如果当前有测量在进行，先调用stop方法记录结束时间
    if (m_currentHandler && m_currentHandler->isSaving()) {
        m_currentHandler->stop();

        // 记录测量持续时间
        qint64 duration = m_currentHandler->getMeasurementDuration();
        qDebug() << "Measurement reset. Duration before reset:" << duration << "ms";
    }

    clearCurrentHandler();
}

void ProjectFileManager::clearCurrentHandler() {
    if (m_currentHandler && m_currentHandler->isSaving()) {
        // 获取文件路径
        QString filePath = m_currentHandler->filePath;
        // 提取文件名
        QFileInfo fileInfo(filePath);
        QString fileName = fileInfo.fileName();

        // 从 m_fileHandlers 中删除该项
        if (m_fileHandlers.contains(fileName)) {
            delete m_fileHandlers.take(fileName);
        }
        m_currentHandler = nullptr;
    }
}

ProjectFileManager* ProjectFileManager::getInstance(QObject *parent)
{
    QMutexLocker locker(&mutex);
    if (instance == nullptr) {
        instance = new ProjectFileManager(parent);
    }
    return instance;
}
void ProjectFileManager::createAndAppendDataFrame(quint16 frameNumber, quint32 pulseCount, const std::vector<std::vector<int>> &data)
{
    if (m_currentHandler) {
        m_currentHandler->createAndAppendDataFrame(frameNumber, pulseCount, data);

        emit fileHandlersChanged();
    }
}
