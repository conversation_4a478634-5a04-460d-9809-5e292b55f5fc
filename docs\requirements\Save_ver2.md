# SpecFLIM保存功能需求文档

**文档信息**

## 目录

1. 概述
2. 术语表
3. 文件命名和格式规范
4. Process功能需求
5. Analysis功能需求
6. Workspace功能需求
7. 用户界面需求
8. 操作限制和约束
9. 错误处理和用户反馈
10. 与其他模块的集成
11. 性能和质量要求
12. 兼容性要求
13. 技术实现要求
14. 数据结构和接口要求
15. 测试要求
16. 部署和维护要求

## 1. 概述

### 1.1 文档目的

本文档定义了SpecFLIM应用程序的保存功能需求，包括Acquire、Process和Analysis三个主要模块的数据保存机制。文档面向开发团队、测试团队和产品管理人员，提供详细的功能规范和技术要求。

### 1.2 功能概述

SpecFLIM保存功能是应用程序的核心功能，负责管理数据采集、处理和分析过程中的数据保存。该功能支持工作区管理、工程文件操作、数据处理操作保存和分析结果保存，并提供统一的文件命名规范和版本管理机制。

### 1.3 功能目标

- 提供透明的操作历史记录和版本管理
- 支持非破坏性的数据操作流程
- 实现操作的可追溯性和可恢复性
- 提供直观的用户界面和操作反馈
- 确保数据完整性和一致性

## 2. 术语表

| 术语                 | 定义                                                                 |
|----------------------|----------------------------------------------------------------------|
| 工作区(Workspace)     | 存储工程文件和相关数据的容器，扩展名为.sflw                          |
| 工程文件(Project File) | 存储原始数据的文件，扩展名为.sflp                                   |
| 操作数据(Operation Data) | 对工程文件进行处理后的数据，存储在工程文件内                         |
| 分析数据(Analysis Data) | 对操作数据进行分析后的结果，存储在工程文件内                         |
| 对齐(Alignment)       | 数据处理操作，用于对齐荧光图像                                       |
| 裁剪(Crop)           | 数据处理操作，用于裁剪荧光图像                                       |
| 曲线添加(Add Curve)   | 数据处理操作，用于添加衰减曲线或光谱曲线                             |
| 分割(Split)           | 数据处理操作，用于将多帧数据分割为单帧或范围帧                       |
| 虚拟子项(Virtual Item) | 在Workspace中显示但不作为独立文件存在的数据项                        |
| 操作序列(Operation Sequence) | 描述数据处理历史的操作代码序列                                     |

## 3. 文件命名和格式规范

### 3.1 工作区文件命名

- **默认格式**: {项目前缀}.sflw
- **示例**: ABC.sflw
- **规则**:
  - 项目前缀从AppConfig配置文件动态读取

### 3.2 工程文件命名

- **默认格式**: {项目前缀}_{序号}.sflp
- **示例**: ABC_001.sflp, ABC_002.sflp
- **规则**:
  - 项目前缀从AppConfig配置文件动态读取
  - 序号从001开始，按创建顺序递增

### 3.3 操作保存命名规范

- **基本格式**: {工程文件名}_{操作序列}
- **操作代码**:
  - al: 对齐操作(Alignment)
  - cr: 裁剪操作(Crop)
  - ad: 曲线添加操作(Add Curve)
- **示例**:
  - ABC_001_al1: 第一次对齐操作
  - ABC_001_al1_cr1: 对齐后的第一次裁剪操作
  - ABC_001_al1_cr1_ad1: 对齐、裁剪后的第一次曲线添加操作
- **操作序列生成规则**:
  - 新操作继承当前完整操作序列
  - 追加新操作时对应类型序号+1
- **示例演化**：初始→al1→al1_cr1→al1_cr1_ad1

### 3.4 Split导出文件命名

- **格式**: {原工程文件名}_spl{序号}.sflp
- **示例**: ABC_001_spl1.sflp, ABC_001_spl2.sflp
- **规则**: 序号从1开始，按导出顺序递增

### 3.5 分析数据命名规范

- **衰减曲线分析**:
  - 格式: DecAna_{序号}
  - 示例: DecAna_001, DecAna_002
- **光谱曲线分析**:
  - 格式: SpeAna_{序号}
  - 示例: SpeAna_001, SpeAna_002
- **规则**:
  - 序号从001开始，对同一操作数据，每分析完毕保存一次，按顺序递增

### 3.6 项目前缀配置说明

- **配置来源**: 项目前缀从AppConfig配置文件中读取
- **配置参数**: 通过配置文件中的项目前缀参数动态获取
- **默认值**: 如果配置文件中未设置或读取失败，使用默认前缀
- **更新机制**: 支持运行时更新项目前缀配置

## 4. Process功能需求

### 4.1 操作保存通用需求

- **保存内容**:
  - Fluorescence Map数据
  - Decay Curve数据(仅已添加的曲线，十字线实时显示的曲线不算)
  - Spectral Curve数据(仅已添加的曲线，十字线实时显示的曲线不算)
  - Total Counts数据
- **版本管理**: 对同一数据的多次同类操作，序号递增
- **存储方式**: 操作数据作为工程文件内的数据段存储，不创建独立文件
- **显示方式**: 在Workspace中显示为工程文件的虚拟子节点

### 4.2 对齐操作保存

- **触发条件**: 点击Alignment区域的Apply按钮
- **命名规则**: 在当前数据名称后添加_al{序号}
- **特殊要求**: 无

### 4.3 裁剪操作保存

- **触发条件**: 点击Crop区域的Apply按钮
- **命名规则**: 在当前数据名称后添加_cr{序号}
- **特殊要求**: 无

### 4.4 曲线添加操作保存

- **触发条件**: 点击Curve区域的Apply按钮
- **命名规则**: 在当前数据名称后添加_ad{序号}
- **特殊要求**: 无

### 4.5 Split功能需求

#### 4.5.1 Single导出功能

- **触发条件**: 点击Split区域Single部分的Export按钮
- **功能描述**: 导出当前选中的单帧数据为新的工程文件
- **保存内容**:
  - 选中帧的Fluorescence Map数据
  - 对应的Total Counts数据(仅选中帧的点)
- **不保存内容**: Decay Curve和Spectral Curve(设为空状态)
- **文件位置**: 与原工程文件相同目录
- **Workspace显示**: 在原文件下方显示
- **命名规则**: 从ABC_001.sflp导出为ABC_001_spl{序号}.sflp

#### 4.5.2 Range导出功能

- **触发条件**: 点击Split区域Range部分的Export按钮
- **功能描述**: 导出指定帧范围的合并数据为新的工程文件
- **保存内容**:
  - 指定范围帧的Fluorescence Map合并数据
  - 对应范围的Total Counts数据
- **不保存内容**: Decay Curve和Spectral Curve(设为空状态)
- **文件位置**: 与原工程文件相同目录
- **Workspace显示**: 在原文件下方显示
- **命名规则**: 从ABC_001.sflp导出为ABC_001_spl{序号}.sflp

#### 4.5.3 Split导出文件操作规则

- Split导出的文件可以作为独立工程文件进行所有操作
- 需要先加载该文件才能进行操作
- 操作序号从1开始独立计数

## 5. Analysis功能需求

### 5.1 分析保存通用需求

- **保存方式**: 点击左下角Save按键（左侧工具栏最下行左数第三个按键）
- **保存内容**:
  - 左侧工具栏全部内容（包括下拉框的选择、输入框的数字、方框内对号的勾选状况、初始参数的限制条件等所有的内容）
  - Fluorescence Map、Decay Curve、Spectral Curve（包括所有相关数据以及展示在图中的状态）
  - 分析区域（右下角整块区域，含残差曲线和分析报告）
- **保存要求**: 点击workspace内保存的相关数据，可完美复现保存时的界面状态
- **存储方式**: 分析数据作为操作数据的虚拟子节点存储，不创建独立文件

### 5.2 衰减曲线分析保存

- **适用场景**: Decay Curve的Fit后的界面
- **命名规则**: DecAna_{序号}，序号从001开始递增
- **特殊要求**: 保存Decay Analysis区域（右下角整块区域，含残差曲线和分析报告）

### 5.3 光谱曲线分析保存

- **适用场景**: Spectral Curve的Fit后的界面
- **命名规则**: SpeAna_{序号}，序号从001开始递增
- **特殊要求**: 保存Spectral Analysis区域（右下角整块区域，含残差曲线和分析报告）

### 5.4 分析报告导出

#### 5.4.1 导出方式

- 右键点击分析报告区域
- 从弹出菜单中选择导出格式
- 选择保存位置（默认与原文件同workspace）

#### 5.4.2 支持格式

- CSV (.csv)
- Excel (.xlsx)
- 文本 (.txt)

#### 5.4.3 导出内容

- 分析报告文本内容
- 残差曲线数据
- 工具栏设置（模型、初始参数、limits/fix限制等）
- 原始数据及拟合数据

## 6. Workspace功能需求

### 6.1 文件管理功能

#### 6.1.1 删除功能

- **触发条件**: 右键点击工程文件(2级)、操作数据(3级)或分析数据(4级)
- **菜单选项**: Delete、Save as
- **删除规则**:
  - 删除高级别项时，其包含的所有低级别项将一并删除
  - 删除工程文件 → 包含的操作数据和分析数据同时删除
  - 删除操作数据 → 包含的分析数据同时删除
- **批量操作**:
  - Ctrl+左键：多选
  - Shift+左键：连续选择
  - 右键菜单选择Delete，批量删除

#### 6.1.2 另存为功能

- **触发条件**: 右键点击工程文件(2级)、操作数据(3级)或分析数据(4级)
- **菜单选项**: Delete、Save as
- **功能**:
  - 工程文件：仅保存当前文件
  - 操作数据：保存当前数据及其直属工程文件
  - 分析数据：保存当前数据及其直属操作数据和工程文件
- **批量操作**:
  - Ctrl+左键：多选
  - Shift+左键：连续选择
  - 右键菜单选择Save as，另存为一个新工程文件
- **存储规则**:
  - 与原文件同目录
  - 在原文件下方显示
- **命名规范**:
  - 格式：原文件名_need{序号}.sflp
  - 示例：ABC_001_need1.sflp

### 6.2 Workspace显示需求

#### 层级结构:

- 工作区(.sflw)（1级）
- 工程文件(.sflp)（2级）
- 操作保存数据(无扩展名)（3级）
- 分析保存数据（无扩展名）（4级）
- Split导出文件(.sflp)（2级）

#### 显示内容:

- 显示文件名，显示文件大小；显示操作数据名，显示操作数据大小；显示分析数据名，显示分析数据大小

#### 排序规则:

- 文件按文件名中的数字序列自然排序(升序)
- 操作数据按保存时间升序
- 分析数据按保存时间升序

#### 实现方式:

- 通过解析.sflp文件内部索引构建虚拟树形结构，操作数据作为工程文件的虚拟子节点显示；分析数据作为操作数据的虚拟子节点显示

#### 性能要求:

- 支持大量操作、分析历史的快速显示，索引解析时间应控制在合理范围内

#### 文件类型过滤:

- AcquireTab: 仅显示.sflp原始文件
- ProcessTab: 显示.sflp文件和操作数据（虚拟子项）
- AnalysisTab: 显示所有支持的文件类型（包括分析数据）
- 根据当前活动的Tab自动切换显示的文件类型

### 6.3 虚拟树形结构说明

- 操作数据和分析数据在文件系统中不存在为独立文件，但在Workspace中显示为独立节点
- 用户可以像操作普通文件一样点击、右键这些虚拟节点
- 虚拟节点支持所有标准操作（加载、删除、另存为等）
- 虚拟节点显示实际数据大小和保存时间

### 6.4 数据加载和显示需求

- 原工程文件: 点击显示原始Fluorescence Map
- 操作保存数据: 点击显示对应操作后的四个图表
- Split导出文件: 点击显示对应的Fluorescence Map和Total Counts
- 分析保存数据: 点击显示分析时的完整界面状态

## 7. 用户界面需求

### 7.1 界面修改需求

- **删除元素**: 删除Process界面左下角的Save按钮
- **新增元素1**: 在以下位置添加Apply按钮
  - Alignment区域: 水平方向右侧、垂直方向中间位置
  - Crop区域: 水平方向右侧、垂直方向中间位置
  - Curve区域: 第三行右侧位置
- **新增元素2**: 在Split功能的Range选项界面中，添加Export按钮

### 7.2 保存触发机制

- 点击Apply按钮立即执行操作并自动保存结果
- 无需用户确认，操作完成即完成保存
- 不提供取消保存选项，但支持删除已保存的操作数据
- 不支持自动保存，必须通过Apply按钮触发

## 8. 操作限制和约束

### 8.1 Split模式限制

- 在Single或Range模式下，禁用对齐、裁剪、曲线添加操作
- 必须返回All模式才能继续进行数据操作
- 对Split导出的文件进行操作需要先加载该文件

### 8.2 数据完整性约束

- 每次操作必须保存完整的相关数据
- 操作序列必须保持连续性和可追溯性
- 文件命名必须遵循既定规范，确保唯一性

## 9. 错误处理和用户反馈

### 9.1 操作反馈

- 每次Apply操作后提供明确的成功反馈
- 在Workspace中实时更新操作结果
- 提供操作进度指示(如需要)

### 9.2 错误处理

- 文件保存失败时提供明确的错误信息
- 命名冲突时自动处理或提示用户
- 数据损坏时提供恢复机制或警告

## 10. 与其他模块的集成

### 10.1 文件管理集成

- 与ProjectFileManager集成，统一文件操作接口
- 支持统一的文件格式和压缩机制
- 遵循既定的文件命名和存储规范

### 10.2 数据处理集成

- 与ProcessAnalysisDataHandler集成，确保数据一致性
- 支持数据缓存和状态管理
- 提供数据变更通知机制

### 10.3 UI集成

- 与BaseTab集成，提供统一的用户界面模式
- 支持主题管理和样式一致性
- 提供标准的用户交互模式

### 10.4 配置管理集成

- 与AppConfig集成，动态读取项目前缀配置
- 支持配置文件的实时更新和重新加载
- 提供配置验证和默认值处理机制
- 确保文件命名的一致性和可配置性

## 11. 性能和质量要求

### 11.1 性能要求

- 操作保存应在合理时间内完成(< 5秒)
- 大数据文件的处理应提供进度反馈
- 内存使用应保持在合理范围内

### 11.2 质量要求

- 数据保存的准确性和完整性
- 操作的可靠性和稳定性
- 用户界面的响应性和易用性
- 错误处理的健壮性和友好性

### 11.3 响应时间要求

- 常规操作保存响应时间 < 2秒
- 大型数据集操作保存响应时间 < 10秒
- 文件加载响应时间 < 3秒
- 虚拟树结构显示响应时间 < 1秒

## 12. 兼容性要求

### 12.1 文件格式兼容性

- 支持现有的.sflp文件格式
- 保持与旧版本.sfd文件的读取兼容性
- 新格式应使用Qt压缩机制

### 12.2 功能兼容性

- 与现有的Analysis功能保持兼容
- 支持现有的数据处理流程
- 保持与其他模块的接口兼容性

## 13. 技术实现要求

### 13.1 数据存储要求

- **存储格式**: 操作保存数据作为工程文件内的数据段存储，不创建独立文件
- **文件结构**: .sflp文件包含文件头、原始数据段、多个操作数据段和元数据段
- **索引机制**: 维护操作名称到数据段位置的映射表，支持快速定位
- **压缩机制**: 使用Qt压缩方法(qCompress/qUncompress)进行数据压缩
- **数据结构**: 保持与现有数据结构的兼容性
- **序列化**: 支持完整的数据序列化和反序列化
- **增量存储**: 支持只存储相对于父操作的变化数据，提高存储效率

### 13.2 版本管理要求

- **操作序号**: 每种操作类型独立计数，支持同一基础数据的多次同类操作
- **操作链**: 支持复杂的操作链组合，如ABC_001_al1_cr1_ad1
- **分支管理**: 支持从任意操作点开始新的操作分支
- **历史追踪**: 维护完整的操作历史和依赖关系
- **命名一致性**: 所有操作链命名遵循统一的项目前缀和日期格式规范

### 13.3 内存管理要求

- **缓存策略**: 实现智能缓存机制，避免重复加载相同数据
- **内存优化**: 大数据处理时采用流式处理或分块处理
- **资源释放**: 及时释放不再使用的数据资源
- **Qt内存模式**: 遵循Qt内存管理最佳实践

### 13.4 并发和异步要求

- **异步保存**: 大数据保存操作应异步执行，避免界面阻塞
- **进度反馈**: 长时间操作提供进度条或状态指示
- **取消机制**: 支持用户取消长时间运行的操作
- **线程安全**: 确保多线程环境下的数据安全

## 14. 数据结构和接口要求

### 14.1 数据结构定义

- **ProcessedData结构**: 包含四种图表数据的完整结构
  - FluorescenceMapData: 荧光图数据
  - DecayCurveData: 衰减曲线数据(仅已添加曲线)
  - SpectralCurveData: 光谱曲线数据(仅已添加曲线)
  - TotalCountsData: 总计数数据
  - 操作元数据: 包含操作类型、时间戳、参数等信息
  - 版本信息: 包含操作序列、依赖关系等版本管理信息
  - 分析数据结构: 包含分析参数、结果和状态信息

### 14.2 接口设计要求

- **统一保存接口**: 提供统一的操作保存接口，支持不同操作类型
- **数据加载接口**: 支持按操作名称或路径加载特定操作数据
- **数据查询接口**: 支持按条件查询操作历史和分析结果
- **事件通知接口**: 提供数据变更和操作完成的事件通知机制

### 14.3 API设计原则

- **简洁性**: 接口设计简洁明了，易于理解和使用
- **一致性**: 所有接口遵循一致的命名和参数传递规范
- **可扩展性**: 接口设计支持未来功能扩展，无需大规模重构
- **错误处理**: 所有接口提供明确的错误处理和异常机制
- **文档化**: 所有接口提供完整的文档和使用示例
