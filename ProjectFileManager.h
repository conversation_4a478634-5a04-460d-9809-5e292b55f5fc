#pragma once

#include <QObject>
#include <QList>
#include <QMap>
#include <QMutex>
#include "MeasureDataHandler.h"
#include "AppConfig.h"
#include "data/UnifiedFileStructures.h"
#include "data/FileNameManager.h"
#include "data/SflpFileManager.h"

class ProjectFileManager : public QObject
{
    Q_OBJECT

private:
    explicit ProjectFileManager(QObject *parent = nullptr);
    static QMutex mutex;
    ~ProjectFileManager();
    static ProjectFileManager* instance;

public:
    ProjectFileManager(ProjectFileManager &other) = delete;
    void operator=(const ProjectFileManager &) = delete;
    static ProjectFileManager* getInstance(QObject *parent = nullptr);

    void createNewProject(const QString &projectPath);
    void openProject(const QString &projectPath);
    void saveProject();
    void saveProject(const QString &filePath);

    QString getFilePath() const;
    void setFilePath(const QString &filePath);

    bool isProjectInEditing() const;
    bool isProjectLoading() const { return m_isLoading; }

    // 测量管理相关功能
    MeasureDataHandler* getSavingHandler() const;
    MeasureDataHandler* getHandler(const QString &fileName);

    void startMeasurement();
    void stopMeasurement();
    void abortMeasurement();
    void resetMeasurement();

    // 新增方法
    const QMap<QString, MeasureDataHandler*>& getFileHandlers() const { return m_fileHandlers; }
    void createAndAppendDataFrame(quint16 frameNumber, quint32 pulseCount, const std::vector<std::vector<int>> &data);

    // 统一文件管理接口
    bool saveOperationData(const QString& sflpFileName,
                          const OperationSequence& sequence,
                          const PlotDataCollection& plotData);
    bool saveAnalysisData(const QString& sflpFileName,
                         const QString& baseOperationName,
                         AnalysisType analysisType,
                         const AnalysisResults& results);
    PlotDataCollection loadOperationData(const QString& operationName);
    AnalysisResults loadAnalysisData(const QString& analysisName);

    // 文件关系管理
    QStringList getChildFiles(const QString& parentFile) const;
    QString getParentFile(const QString& childFile) const;
    FileType getFileType(const QString& fileName) const;

    // 操作计数器管理
    OperationCounters getOperationCounters(const QString& sflpFileName) const;
    void updateOperationCounter(const QString& sflpFileName,
                               OperationType type, int newValue);
    void updateAnalysisCounter(const QString& sflpFileName,
                              AnalysisType type, int newValue);
private:
    void clearCurrentHandler();

    // 版本管理相关功能
    void createNewVersion();
    void switchToVersion(int version);

signals:
    void fileHandlersChanged();
    void projectInfoChanged();
    void projectLoadingFinished();
    void projectLoadingError(const QString &errorMessage);
    void projectLoadingWarning(const QString &warningMessage);
    void saveProgressChanged(int progress);

    // 统一文件管理信号
    void operationDataSaved(const QString& operationName);
    void analysisDataSaved(const QString& analysisName);
    void fileRelationshipChanged();
    void operationCounterUpdated(const QString& sflpFileName, OperationType type, int newValue);
    void analysisCounterUpdated(const QString& sflpFileName, AnalysisType type, int newValue);
    void sflpFileCreated(const QString& sflpFileName);
    void sflpFileCorrupted(const QString& sflpFileName, const QString& error);

private:
    QString m_projectPath; // 工程文件路径
    QMap<QString, MeasureDataHandler*> m_fileHandlers;
    int m_currentVersion;
    int m_lastDataFileNumber; // 记录当前Project中数据文件的最后编号
    MeasureDataHandler* m_currentHandler; // 当前测量处理器
    bool m_isLoading = false; // 项目加载状态标志
    void saveMetadataFile(); // 新增函数声明

    // 统一文件管理私有成员
    QMap<QString, QStringList> m_childrenMap; // 父文件→子项列表
    QMap<QString, QString> m_parentMap;       // 子项→父文件
    QMap<QString, OperationCounters> m_sflpCounters; // 操作计数器缓存
    QMap<QString, SflpFileManager*> m_sflpManagers; // SFLP文件管理器缓存
    mutable QMutex m_relationMutex;
    mutable QMutex m_counterMutex;
    mutable QMutex m_managerMutex;

    // 内部实现方法
    bool writeDataSegmentToSflp(const QString& sflpFileName,
                                const QString& segmentName,
                                const QByteArray& data);
    QByteArray readDataSegmentFromSflp(const QString& sflpFileName,
                                      const QString& segmentName);
    void updateFileRelationship(const QString& parent, const QString& child);
    SflpFileManager* getSflpManager(const QString& sflpFileName);
    void releaseSflpManager(const QString& sflpFileName);
    void loadCountersFromFile(const QString& sflpFileName);
    void saveCountersToFile(const QString& sflpFileName);
    bool validateOperationData(const PlotDataCollection& plotData) const;
    bool validateAnalysisData(const AnalysisResults& results) const;
    QString extractBaseOperationName(const QString& analysisName) const;
    QString generateOperationSegmentName(const QString& operationName) const;
    QString generateAnalysisSegmentName(const QString& analysisName) const;
    void logFileOperation(const QString& operation, const QString& fileName, bool success);
};