#ifndef VIRTUALNODEMANAGER_H
#define VIRTUALNODEMANAGER_H

#include <QObject>
#include <QStandardItem>
#include <QStandardItemModel>
#include <QTreeView>
#include <QMap>
#include <QMutex>
#include <QIcon>
#include "UnifiedFileStructures.h"

class FileRelationshipCache;
class FileNameManager;

/**
 * @brief 虚拟节点管理UI辅助类
 * 
 * 负责OpenProjectWidget中虚拟节点的管理，包括：
 * - 操作节点的创建和显示
 * - 分析节点的创建和显示
 * - 节点的图标和样式管理
 * - 节点的展开/折叠状态管理
 * - 节点的过滤和搜索
 * 
 * 这个类将虚拟节点管理从OpenProjectWidget中分离出来，
 * 提供专门的UI节点管理功能和更好的代码组织。
 */
class VirtualNodeManager : public QObject
{
    Q_OBJECT

public:
    explicit VirtualNodeManager(QTreeView* treeView, QStandardItemModel* model, QObject* parent = nullptr);
    ~VirtualNodeManager();

    // 节点创建
    QStandardItem* createProjectItem(const QString& sflpFileName);
    QStandardItem* createOperationItem(const QString& operationName);
    QStandardItem* createAnalysisItem(const QString& analysisName, AnalysisType type);
    QStandardItem* createSplitItem(const QString& splitName);
    
    // 树结构构建
    void buildVirtualTree(const QString& projectPath);
    void addVirtualChildren(QStandardItem* parentItem, const QString& sflpFileName);
    void refreshVirtualNode(const QString& fileName);
    void removeVirtualNode(const QString& fileName);
    
    // 节点查找
    QStandardItem* findNodeByFileName(const QString& fileName) const;
    QStandardItem* findNodeByType(FileType fileType, const QString& parentName = QString()) const;
    QList<QStandardItem*> findNodesByPattern(const QString& pattern) const;
    
    // 节点状态管理
    void saveNodeStates();
    void restoreNodeStates();
    void expandNode(const QString& fileName, bool expand = true);
    void expandAllNodes();
    void collapseAllNodes();
    
    // 节点过滤
    void setNodeFilter(const QList<FileType>& visibleTypes);
    void clearNodeFilter();
    void applyNodeFilter();
    
    // 节点选择
    bool selectNode(const QString& fileName);
    QString getSelectedNodeFileName() const;
    QStringList getSelectedNodeFileNames() const;
    
    // 节点样式
    void setNodeIcon(QStandardItem* item, FileType fileType);
    void setNodeDisplayName(QStandardItem* item, const QString& fileName, FileType fileType);
    void updateNodeAppearance(QStandardItem* item, FileType fileType);
    
    // 节点排序
    void sortNodes(QStandardItem* parentItem = nullptr);
    void applySortingToAllNodes();
    
    // 统计信息
    int getNodeCount() const;
    int getNodeCountByType(FileType fileType) const;
    QMap<FileType, int> getNodeStatistics() const;

signals:
    void nodeCreated(const QString& fileName, FileType fileType);
    void nodeRemoved(const QString& fileName);
    void nodeSelected(const QString& fileName, FileType fileType);
    void nodeExpanded(const QString& fileName, bool expanded);
    void treeRebuilt();
    void filterApplied(const QList<FileType>& visibleTypes);

private:
    QTreeView* m_treeView;
    QStandardItemModel* m_treeModel;
    FileRelationshipCache* m_relationshipCache;
    
    // 节点缓存
    QMap<QString, QStandardItem*> m_nodeCache;
    mutable QMutex m_cacheMutex;
    
    // 状态管理
    QMap<QString, bool> m_nodeExpandStates;
    QList<FileType> m_visibleTypes;
    QString m_lastSelectedNode;
    
    // 内部方法
    QStandardItem* createNodeInternal(const QString& fileName, FileType fileType);
    void addNodeToCache(const QString& fileName, QStandardItem* item);
    void removeNodeFromCache(const QString& fileName);
    QStandardItem* getNodeFromCache(const QString& fileName) const;
    
    // 树遍历
    QStandardItem* findNodeRecursive(QStandardItem* parent, const QString& fileName) const;
    void collectNodesRecursive(QStandardItem* parent, QList<QStandardItem*>& nodes) const;
    void saveNodeStatesRecursive(QStandardItem* parent);
    void restoreNodeStatesRecursive(QStandardItem* parent);
    
    // 过滤实现
    void hideNodesByType(QStandardItem* parent, const QList<FileType>& typesToHide);
    void showAllNodes(QStandardItem* parent);
    bool shouldShowNode(QStandardItem* item) const;
    
    // 排序实现
    void sortNodeChildren(QStandardItem* parentItem);
    bool naturalCompare(const QString& str1, const QString& str2) const;
    
    // 图标管理
    QIcon getFileTypeIcon(FileType fileType) const;
    void loadIcons();
    QMap<FileType, QIcon> m_iconCache;
    
    // 工具方法
    FileType getNodeFileType(QStandardItem* item) const;
    QString getNodeFileName(QStandardItem* item) const;
    void setNodeData(QStandardItem* item, const QString& fileName, FileType fileType);
    void logNodeOperation(const QString& operation, const QString& fileName, bool success);
    
    // 禁用拷贝构造和赋值
    VirtualNodeManager(const VirtualNodeManager&) = delete;
    VirtualNodeManager& operator=(const VirtualNodeManager&) = delete;
};

/**
 * @brief 节点状态信息
 */
struct NodeState {
    QString fileName;
    FileType fileType;
    bool expanded;
    bool visible;
    int sortOrder;
    
    QByteArray serialize() const;
    bool deserialize(const QByteArray& data);
    bool isValid() const;
};

/**
 * @brief 树状态管理器
 */
class TreeStateManager {
public:
    static bool saveTreeState(const QString& stateFileName, const QMap<QString, NodeState>& states);
    static QMap<QString, NodeState> loadTreeState(const QString& stateFileName);
    static bool validateTreeState(const QMap<QString, NodeState>& states);
};

#endif // VIRTUALNODEMANAGER_H
