#include "SflpFileOperations.h"
#include <QMutexLocker>
#include <QDebug>
#include <QFileInfo>
#include <QDateTime>

SflpFileOperations::SflpFileOperations(QObject* parent)
    : QObject(parent)
{
}

SflpFileOperations::~SflpFileOperations() {
    cleanupManagers();
}

bool SflpFileOperations::writeOperationData(const QString& sflpFileName,
                                           const QString& operationName,
                                           const PlotDataCollection& plotData) {
    if (sflpFileName.isEmpty() || operationName.isEmpty() || !validateOperationData(plotData)) {
        setError("Invalid parameters for writeOperationData");
        return false;
    }
    
    try {
        // 序列化和压缩数据
        QByteArray rawData = plotData.serialize();
        if (rawData.isEmpty()) {
            setError("Failed to serialize plot data");
            return false;
        }
        
        QByteArray compressedData = compressData(rawData);
        if (compressedData.isEmpty()) {
            setError("Failed to compress plot data");
            return false;
        }
        
        // 写入数据段
        QString segmentName = generateOperationSegmentName(operationName);
        bool success = writeDataSegment(sflpFileName, segmentName, compressedData);
        
        if (success) {
            logOperation("writeOperationData", operationName, true);
            emit operationDataWritten(sflpFileName, operationName);
        } else {
            setError("Failed to write operation data segment");
        }
        
        return success;
        
    } catch (const std::exception& e) {
        setError(QString("Exception in writeOperationData: %1").arg(e.what()));
        logOperation("writeOperationData", operationName, false);
        return false;
    }
}

bool SflpFileOperations::writeAnalysisData(const QString& sflpFileName,
                                          const QString& analysisName,
                                          const AnalysisResults& results) {
    if (sflpFileName.isEmpty() || analysisName.isEmpty() || !validateAnalysisData(results)) {
        setError("Invalid parameters for writeAnalysisData");
        return false;
    }
    
    try {
        // 序列化和压缩数据
        QByteArray rawData = results.serialize();
        if (rawData.isEmpty()) {
            setError("Failed to serialize analysis results");
            return false;
        }
        
        QByteArray compressedData = compressData(rawData);
        if (compressedData.isEmpty()) {
            setError("Failed to compress analysis results");
            return false;
        }
        
        // 写入数据段
        QString segmentName = generateAnalysisSegmentName(analysisName);
        bool success = writeDataSegment(sflpFileName, segmentName, compressedData);
        
        if (success) {
            logOperation("writeAnalysisData", analysisName, true);
            emit analysisDataWritten(sflpFileName, analysisName);
        } else {
            setError("Failed to write analysis data segment");
        }
        
        return success;
        
    } catch (const std::exception& e) {
        setError(QString("Exception in writeAnalysisData: %1").arg(e.what()));
        logOperation("writeAnalysisData", analysisName, false);
        return false;
    }
}

PlotDataCollection SflpFileOperations::readOperationData(const QString& sflpFileName,
                                                        const QString& operationName) {
    PlotDataCollection plotData;
    
    if (sflpFileName.isEmpty() || operationName.isEmpty()) {
        setError("Invalid parameters for readOperationData");
        return plotData;
    }
    
    try {
        // 读取数据段
        QString segmentName = generateOperationSegmentName(operationName);
        QByteArray compressedData = readDataSegment(sflpFileName, segmentName);
        
        if (compressedData.isEmpty()) {
            setError("Failed to read operation data segment");
            return plotData;
        }
        
        // 解压缩数据
        QByteArray rawData = decompressData(compressedData);
        if (rawData.isEmpty()) {
            setError("Failed to decompress operation data");
            return plotData;
        }
        
        // 反序列化数据
        if (!plotData.deserialize(rawData)) {
            setError("Failed to deserialize plot data");
            plotData.clear();
            return plotData;
        }
        
        // 验证数据完整性
        if (!plotData.isValid()) {
            setError("Invalid plot data loaded");
            plotData.clear();
            return plotData;
        }
        
        logOperation("readOperationData", operationName, true);
        
    } catch (const std::exception& e) {
        setError(QString("Exception in readOperationData: %1").arg(e.what()));
        plotData.clear();
        logOperation("readOperationData", operationName, false);
    }
    
    return plotData;
}

AnalysisResults SflpFileOperations::readAnalysisData(const QString& sflpFileName,
                                                    const QString& analysisName) {
    AnalysisResults results;
    
    if (sflpFileName.isEmpty() || analysisName.isEmpty()) {
        setError("Invalid parameters for readAnalysisData");
        return results;
    }
    
    try {
        // 读取数据段
        QString segmentName = generateAnalysisSegmentName(analysisName);
        QByteArray compressedData = readDataSegment(sflpFileName, segmentName);
        
        if (compressedData.isEmpty()) {
            setError("Failed to read analysis data segment");
            return results;
        }
        
        // 解压缩数据
        QByteArray rawData = decompressData(compressedData);
        if (rawData.isEmpty()) {
            setError("Failed to decompress analysis data");
            return results;
        }
        
        // 反序列化数据
        if (!results.deserialize(rawData)) {
            setError("Failed to deserialize analysis results");
            results.clear();
            return results;
        }
        
        // 验证数据完整性
        if (!results.isValid()) {
            setError("Invalid analysis results loaded");
            results.clear();
            return results;
        }
        
        logOperation("readAnalysisData", analysisName, true);
        
    } catch (const std::exception& e) {
        setError(QString("Exception in readAnalysisData: %1").arg(e.what()));
        results.clear();
        logOperation("readAnalysisData", analysisName, false);
    }
    
    return results;
}

bool SflpFileOperations::writeOperationCounters(const QString& sflpFileName,
                                               const OperationCounters& counters) {
    if (sflpFileName.isEmpty()) {
        setError("Invalid sflp file name");
        return false;
    }
    
    try {
        QByteArray rawData = counters.serialize();
        QByteArray compressedData = compressData(rawData);
        
        QString segmentName = generateCountersSegmentName();
        return writeDataSegment(sflpFileName, segmentName, compressedData);
        
    } catch (const std::exception& e) {
        setError(QString("Exception in writeOperationCounters: %1").arg(e.what()));
        return false;
    }
}

OperationCounters SflpFileOperations::readOperationCounters(const QString& sflpFileName) {
    OperationCounters counters;
    
    if (sflpFileName.isEmpty()) {
        setError("Invalid sflp file name");
        return counters;
    }
    
    try {
        QString segmentName = generateCountersSegmentName();
        QByteArray compressedData = readDataSegment(sflpFileName, segmentName);
        
        if (!compressedData.isEmpty()) {
            QByteArray rawData = decompressData(compressedData);
            if (!rawData.isEmpty()) {
                counters.deserialize(rawData);
            }
        }
        
    } catch (const std::exception& e) {
        setError(QString("Exception in readOperationCounters: %1").arg(e.what()));
    }
    
    return counters;
}

bool SflpFileOperations::validateSflpFile(const QString& sflpFileName) {
    SflpFileManager* manager = getManager(sflpFileName);
    if (!manager) {
        setError("Failed to get SFLP manager");
        return false;
    }
    
    bool isValid = manager->validateFile();
    emit fileValidationCompleted(sflpFileName, isValid);
    
    return isValid;
}

QStringList SflpFileOperations::getDataSegmentNames(const QString& sflpFileName) {
    SflpFileManager* manager = getManager(sflpFileName);
    if (!manager) {
        setError("Failed to get SFLP manager");
        return QStringList();
    }
    
    return manager->getDataSegmentNames();
}

QByteArray SflpFileOperations::compressData(const QByteArray& rawData) {
    return SflpFileManager::compressData(rawData);
}

QByteArray SflpFileOperations::decompressData(const QByteArray& compressedData) {
    return SflpFileManager::decompressData(compressedData);
}

QString SflpFileOperations::getLastError() const {
    QMutexLocker locker(&m_errorMutex);
    return m_lastError;
}

bool SflpFileOperations::hasError() const {
    QMutexLocker locker(&m_errorMutex);
    return !m_lastError.isEmpty();
}

void SflpFileOperations::clearError() {
    QMutexLocker locker(&m_errorMutex);
    m_lastError.clear();
}

// 私有方法实现
SflpFileManager* SflpFileOperations::getManager(const QString& sflpFileName) {
    QMutexLocker locker(&m_cacheMutex);

    if (!m_managerCache.contains(sflpFileName)) {
        SflpFileManager* manager = new SflpFileManager(sflpFileName, this);
        m_managerCache[sflpFileName] = manager;

        // 连接错误信号
        connect(manager, &SflpFileManager::errorOccurred,
                this, [this, sflpFileName](const QString& error) {
                    emit errorOccurred(sflpFileName, error);
                });
    }

    return m_managerCache[sflpFileName];
}

void SflpFileOperations::releaseManager(const QString& sflpFileName) {
    QMutexLocker locker(&m_cacheMutex);

    if (m_managerCache.contains(sflpFileName)) {
        SflpFileManager* manager = m_managerCache[sflpFileName];
        manager->closeFile();
    }
}

void SflpFileOperations::cleanupManagers() {
    QMutexLocker locker(&m_cacheMutex);

    for (auto it = m_managerCache.begin(); it != m_managerCache.end(); ++it) {
        it.value()->closeFile();
        it.value()->deleteLater();
    }
    m_managerCache.clear();
}

QString SflpFileOperations::generateOperationSegmentName(const QString& operationName) const {
    return QString("op_%1").arg(operationName);
}

QString SflpFileOperations::generateAnalysisSegmentName(const QString& analysisName) const {
    return QString("ana_%1").arg(analysisName);
}

QString SflpFileOperations::generateCountersSegmentName() const {
    return "operation_counters";
}

bool SflpFileOperations::writeDataSegment(const QString& sflpFileName,
                                         const QString& segmentName,
                                         const QByteArray& data) {
    SflpFileManager* manager = getManager(sflpFileName);
    if (!manager) {
        return false;
    }

    if (!manager->isOpen()) {
        if (!manager->openFile(QIODevice::ReadWrite)) {
            setError(QString("Failed to open SFLP file: %1").arg(sflpFileName));
            return false;
        }
    }

    bool success = manager->writeDataSegment(segmentName, data);
    if (!success) {
        setError(QString("Failed to write data segment: %1").arg(manager->getLastError()));
    }

    return success;
}

QByteArray SflpFileOperations::readDataSegment(const QString& sflpFileName,
                                              const QString& segmentName) {
    SflpFileManager* manager = getManager(sflpFileName);
    if (!manager) {
        return QByteArray();
    }

    if (!manager->isOpen()) {
        if (!manager->openFile(QIODevice::ReadOnly)) {
            setError(QString("Failed to open SFLP file: %1").arg(sflpFileName));
            return QByteArray();
        }
    }

    QByteArray data = manager->readDataSegment(segmentName);
    if (data.isEmpty() && manager->hasError()) {
        setError(QString("Failed to read data segment: %1").arg(manager->getLastError()));
    }

    return data;
}

void SflpFileOperations::setError(const QString& error) {
    QMutexLocker locker(&m_errorMutex);
    m_lastError = error;
    qWarning() << "SflpFileOperations error:" << error;
}

void SflpFileOperations::logOperation(const QString& operation, const QString& fileName, bool success) {
    if (success) {
        qDebug() << "SFLP operation succeeded:" << operation << "on" << fileName;
    } else {
        qWarning() << "SFLP operation failed:" << operation << "on" << fileName;
    }
}

bool SflpFileOperations::validateOperationData(const PlotDataCollection& plotData) const {
    return plotData.isValid() && plotData.calculateSize() > 0;
}

bool SflpFileOperations::validateAnalysisData(const AnalysisResults& results) const {
    return results.isValid();
}

bool SflpFileOperations::validateSegmentName(const QString& segmentName) const {
    return !segmentName.isEmpty() && segmentName.length() <= 255;
}
