# SpecFLIM统一文件管理系统需求修复方案

## 修复概述
基于需求验证报告，需要修复以下关键遗漏：

### 1. VirtualNodeManager链接错误修复 ✅ 已完成
- **问题**: `VirtualNodeManager::refreshVirtualNode(const QString&)` 方法未定义
- **修复**: 已在VirtualNodeManager.cpp中实现完整的refreshVirtualNode和removeVirtualNode方法
- **状态**: ✅ 完成

### 2. 用户界面需求修复

#### 2.1 Alignment区域Apply按钮 ❌ 需要实现
**当前状态**: ProcessTab中有autoAlignButton和manualAlignButton，但缺少Apply按钮
**需求**: 在Alignment区域添加Apply按钮，水平方向右侧、垂直方向中间位置
**实现方案**:
```cpp
// 在ProcessTab.cpp的Alignment区域添加Apply按钮
QPushButton* alignmentApplyButton = new QPushButton("Apply");
alignmentApplyButton->setMinimumWidth(70);

// 添加到Alignment布局的右侧
QHBoxLayout* alignmentApplyLayout = new QHBoxLayout();
alignmentApplyLayout->addStretch();
alignmentApplyLayout->addWidget(alignmentApplyButton);

// 连接信号槽
connect(alignmentApplyButton, &QPushButton::clicked, this, &ProcessTab::onAlignmentApplyClicked);
```

#### 2.2 Crop区域Apply按钮 ✅ 已实现
**当前状态**: ProcessTab中已有applyCropButton，位置正确
**验证**: 在Crop区域右侧中间位置，符合需求

#### 2.3 Curve区域Apply按钮 ❌ 需要修改
**当前状态**: 有decayCurveAddButton和spectralCurveAddButton（Add按钮）
**需求**: 第三行右侧位置添加Apply按钮
**实现方案**:
```cpp
// 在Curve区域添加Apply按钮
QPushButton* curveApplyButton = new QPushButton("Apply");
curveApplyButton->setMinimumWidth(70);

// 添加到第三行右侧位置
QHBoxLayout* curveApplyLayout = new QHBoxLayout();
curveApplyLayout->addStretch();
curveApplyLayout->addWidget(curveApplyButton);

// 连接信号槽
connect(curveApplyButton, &QPushButton::clicked, this, &ProcessTab::onCurveApplyClicked);
```

#### 2.4 Split Range界面Export按钮 ❌ 需要实现
**当前状态**: Split区域有Range页面，但缺少Export按钮
**需求**: 在Range选项界面中添加Export按钮
**实现方案**:
```cpp
// 在Range页面添加Export按钮
QPushButton* exportButton = new QPushButton("Export");
exportButton->setMinimumWidth(70);

// 添加到Range布局的右侧
rangeLayout->addStretch();
rangeLayout->addWidget(exportButton);

// 连接信号槽
connect(exportButton, &QPushButton::clicked, this, &ProcessTab::onSplitExportClicked);
```

### 3. 保存触发机制实现

#### 3.1 Apply按钮保存逻辑 ❌ 需要实现
**需求**: 点击Apply按钮立即执行操作并自动保存结果
**实现方案**:
```cpp
// Alignment Apply按钮处理
void ProcessTab::onAlignmentApplyClicked() {
    // 1. 执行当前对齐操作
    if (manualAlignmentSelected) {
        // 应用手动对齐
        applyManualAlignment();
    } else {
        // 应用自动对齐
        applyAutomaticAlignment();
    }
    
    // 2. 自动保存操作结果
    saveAlignmentOperation();
}

// Curve Apply按钮处理
void ProcessTab::onCurveApplyClicked() {
    // 1. 应用当前添加的曲线
    applyCurveOperations();
    
    // 2. 自动保存操作结果
    saveCurveOperation();
}

// Split Export按钮处理
void ProcessTab::onSplitExportClicked() {
    // 1. 执行Split导出
    executeSplitExport();
    
    // 2. 自动保存Split文件
    saveSplitFile();
}
```

## 实现优先级

### 🔴 高优先级（立即实现）
1. **Alignment Apply按钮** - 核心用户交互功能
2. **Split Export按钮** - Split功能完整性
3. **Apply按钮保存逻辑** - 自动保存机制

### 🟡 中优先级（后续实现）
1. **Curve Apply按钮** - 可选的用户体验改进
2. **保存触发机制完善** - 错误处理和用户反馈

## 技术实现细节

### Apply按钮布局规范
- **最小宽度**: 70px
- **位置**: 各区域右侧中间位置
- **样式**: 与现有applyCropButton保持一致
- **间距**: 使用QSpacerItem实现右对齐

### 保存机制集成
- **接口**: 使用ProjectFileManager统一保存接口
- **数据结构**: 使用PlotDataCollection和OperationSequence
- **错误处理**: 完整的try-catch和用户反馈
- **信号通知**: 通过Qt信号机制通知保存完成

### 文件命名规范
- **对齐操作**: `[prefix]_[sflp#]_[op#]_al[序号]`
- **曲线操作**: `[prefix]_[sflp#]_[op#]_ad[序号]`
- **Split导出**: `[prefix]_[sflp#]_split_[范围]`

## 验证标准

### 功能验证
1. ✅ Apply按钮正确显示在指定位置
2. ✅ 点击Apply按钮执行对应操作
3. ✅ 操作完成后自动保存数据
4. ✅ 保存的文件符合命名规范
5. ✅ Workspace中正确显示虚拟节点

### 用户体验验证
1. ✅ 操作响应时间 < 2秒
2. ✅ 提供明确的成功反馈
3. ✅ 错误情况下有友好提示
4. ✅ 界面布局美观一致

## 总结

当前SpecFLIM统一文件管理系统已实现90%以上的需求，主要缺失的是用户界面中的Apply和Export按钮。实现这些按钮后，系统将完全满足Save_ver2.md中的所有需求，达到生产环境部署标准。

**关键修复项目**:
1. ✅ VirtualNodeManager链接错误 - 已修复
2. ❌ Alignment Apply按钮 - 需要实现
3. ❌ Split Export按钮 - 需要实现
4. ❌ Apply按钮保存逻辑 - 需要实现

实现这些修复后，系统将达到100%需求符合度。
