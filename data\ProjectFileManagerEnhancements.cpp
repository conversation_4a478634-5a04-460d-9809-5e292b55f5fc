#include "ProjectFileManagerEnhancements.h"
#include <QMutexLocker>
#include <QFileInfo>
#include <QDir>
#include <QDebug>

// 这个文件展示了需要添加到现有ProjectFileManager类中的增强功能实现
// 实际实现时，应该将这些方法直接添加到ProjectFileManager.cpp中

bool ProjectFileManagerEnhancements::saveOperationData(const QString& sflpFileName, 
                                                       const OperationSequence& sequence,
                                                       const PlotDataCollection& plotData) {
    if (sflpFileName.isEmpty() || sequence.isEmpty() || !validateOperationData(plotData)) {
        qWarning() << "Invalid parameters for saveOperationData";
        return false;
    }
    
    try {
        // 1. 生成操作名称
        FileNameManager* nameManager = FileNameManager::getInstance();
        QString operationName = nameManager->generateOperationName(sflpFileName, sequence);
        
        if (operationName.isEmpty()) {
            qWarning() << "Failed to generate operation name";
            return false;
        }
        
        // 2. 创建操作元数据
        OperationMetadata metadata;
        metadata.operationType = sequence.operations.last().first;
        metadata.timestamp = QDateTime::currentDateTime();
        metadata.operationName = operationName;
        metadata.sequence = sequence;
        metadata.description = QString("Operation: %1").arg(operationName);
        
        // 3. 序列化和压缩数据
        QByteArray plotDataBytes = plotData.serialize();
        QByteArray metadataBytes = metadata.serialize();
        
        if (plotDataBytes.isEmpty() || metadataBytes.isEmpty()) {
            qWarning() << "Failed to serialize operation data";
            return false;
        }
        
        QByteArray compressedPlotData = compressData(plotDataBytes);
        QByteArray compressedMetadata = compressData(metadataBytes);
        
        // 4. 保存到SFLP文件
        QString dataSegmentName = generateOperationSegmentName(operationName) + "_data";
        QString metaSegmentName = generateOperationSegmentName(operationName) + "_meta";
        
        bool success = saveToSflpFormat(sflpFileName, compressedPlotData, dataSegmentName);
        success &= saveToSflpFormat(sflpFileName, compressedMetadata, metaSegmentName);
        
        if (success) {
            // 5. 更新文件关系和计数器
            updateFileRelationship(sflpFileName, operationName);
            updateOperationCounter(sflpFileName, metadata.operationType, 
                                 sequence.operations.last().second);
            
            logFileOperation("saveOperationData", operationName, true);
            emit operationDataSaved(operationName);
            return true;
        } else {
            qWarning() << "Failed to save operation data to SFLP file";
        }
        
    } catch (const std::exception& e) {
        qCritical() << "Exception in saveOperationData:" << e.what();
        logFileOperation("saveOperationData", sflpFileName, false);
    }
    
    return false;
}

bool ProjectFileManagerEnhancements::saveAnalysisData(const QString& sflpFileName,
                                                      const QString& baseOperationName,
                                                      AnalysisType analysisType,
                                                      const AnalysisResults& results) {
    if (sflpFileName.isEmpty() || baseOperationName.isEmpty() || !validateAnalysisData(results)) {
        qWarning() << "Invalid parameters for saveAnalysisData";
        return false;
    }
    
    try {
        // 1. 生成分析序列号和名称
        FileNameManager* nameManager = FileNameManager::getInstance();
        int sequence = nameManager->getNextAnalysisSequence(sflpFileName, analysisType);
        
        QString analysisName;
        if (analysisType == AnalysisType::DecayAnalysis) {
            analysisName = nameManager->generateDecayAnalysisName(baseOperationName, sequence);
        } else {
            analysisName = nameManager->generateSpectralAnalysisName(baseOperationName, sequence);
        }
        
        if (analysisName.isEmpty()) {
            qWarning() << "Failed to generate analysis name";
            return false;
        }
        
        // 2. 序列化和压缩数据
        QByteArray resultsBytes = results.serialize();
        if (resultsBytes.isEmpty()) {
            qWarning() << "Failed to serialize analysis results";
            return false;
        }
        
        QByteArray compressedResults = compressData(resultsBytes);
        
        // 3. 保存到SFLP文件
        QString segmentName = generateAnalysisSegmentName(analysisName) + "_results";
        bool success = saveToSflpFormat(sflpFileName, compressedResults, segmentName);
        
        if (success) {
            // 4. 更新文件关系和计数器
            updateFileRelationship(baseOperationName, analysisName);
            updateAnalysisCounter(sflpFileName, analysisType, sequence);
            
            logFileOperation("saveAnalysisData", analysisName, true);
            emit analysisDataSaved(analysisName);
            return true;
        } else {
            qWarning() << "Failed to save analysis data to SFLP file";
        }
        
    } catch (const std::exception& e) {
        qCritical() << "Exception in saveAnalysisData:" << e.what();
        logFileOperation("saveAnalysisData", sflpFileName, false);
    }
    
    return false;
}

PlotDataCollection ProjectFileManagerEnhancements::loadOperationData(const QString& operationName) {
    PlotDataCollection plotData;
    
    if (operationName.isEmpty()) {
        qWarning() << "Invalid operation name";
        return plotData;
    }
    
    try {
        // 1. 获取父文件
        QString parentFile = getParentFile(operationName);
        if (parentFile.isEmpty()) {
            qWarning() << "Parent file not found for operation:" << operationName;
            return plotData;
        }
        
        // 2. 从SFLP文件读取压缩数据
        QString segmentName = generateOperationSegmentName(operationName) + "_data";
        QByteArray compressedData = loadFromSflpFormat(parentFile, segmentName);
        
        if (compressedData.isEmpty()) {
            qWarning() << "Failed to load operation data from SFLP file";
            return plotData;
        }
        
        // 3. 解压缩数据
        QByteArray rawData = decompressData(compressedData);
        if (rawData.isEmpty()) {
            qWarning() << "Failed to decompress operation data";
            return plotData;
        }
        
        // 4. 反序列化数据
        if (!plotData.deserialize(rawData)) {
            qWarning() << "Failed to deserialize plot data";
            plotData.clear();
            return plotData;
        }
        
        // 5. 验证数据完整性
        if (!plotData.isValid()) {
            qWarning() << "Invalid plot data loaded";
            plotData.clear();
            return plotData;
        }
        
        logFileOperation("loadOperationData", operationName, true);
        
    } catch (const std::exception& e) {
        qCritical() << "Exception in loadOperationData:" << e.what();
        plotData.clear();
        logFileOperation("loadOperationData", operationName, false);
    }
    
    return plotData;
}

AnalysisResults ProjectFileManagerEnhancements::loadAnalysisData(const QString& analysisName) {
    AnalysisResults results;
    
    if (analysisName.isEmpty()) {
        qWarning() << "Invalid analysis name";
        return results;
    }
    
    try {
        // 1. 获取基础操作名称和父文件
        QString baseOperation = extractBaseOperationName(analysisName);
        QString parentFile = getParentFile(baseOperation);
        
        if (parentFile.isEmpty()) {
            qWarning() << "Parent file not found for analysis:" << analysisName;
            return results;
        }
        
        // 2. 读取分析结果
        QString segmentName = generateAnalysisSegmentName(analysisName) + "_results";
        QByteArray compressedResults = loadFromSflpFormat(parentFile, segmentName);
        
        if (compressedResults.isEmpty()) {
            qWarning() << "Failed to load analysis data from SFLP file";
            return results;
        }
        
        // 3. 解压缩数据
        QByteArray rawResults = decompressData(compressedResults);
        if (rawResults.isEmpty()) {
            qWarning() << "Failed to decompress analysis data";
            return results;
        }
        
        // 4. 反序列化分析结果
        if (!results.deserialize(rawResults)) {
            qWarning() << "Failed to deserialize analysis results";
            results.clear();
            return results;
        }
        
        // 5. 验证数据完整性
        if (!results.isValid()) {
            qWarning() << "Invalid analysis results loaded";
            results.clear();
            return results;
        }
        
        logFileOperation("loadAnalysisData", analysisName, true);
        
    } catch (const std::exception& e) {
        qCritical() << "Exception in loadAnalysisData:" << e.what();
        results.clear();
        logFileOperation("loadAnalysisData", analysisName, false);
    }
    
    return results;
}

QStringList ProjectFileManagerEnhancements::getChildFiles(const QString& parentFile) const {
    QMutexLocker locker(&m_relationMutex);
    return m_childrenMap.value(parentFile, QStringList());
}

QString ProjectFileManagerEnhancements::getParentFile(const QString& childFile) const {
    QMutexLocker locker(&m_relationMutex);
    return m_parentMap.value(childFile, QString());
}

FileType ProjectFileManagerEnhancements::getFileType(const QString& fileName) const {
    FileNameManager* nameManager = FileNameManager::getInstance();
    return nameManager->identifyFileType(fileName);
}

OperationCounters ProjectFileManagerEnhancements::getOperationCounters(const QString& sflpFileName) const {
    QMutexLocker locker(&m_counterMutex);
    
    if (!m_sflpCounters.contains(sflpFileName)) {
        // 从文件加载计数器
        const_cast<ProjectFileManagerEnhancements*>(this)->loadCountersFromFile(sflpFileName);
    }
    
    return m_sflpCounters.value(sflpFileName, OperationCounters());
}

void ProjectFileManagerEnhancements::updateOperationCounter(const QString& sflpFileName, 
                                                           OperationType type, int newValue) {
    QMutexLocker locker(&m_counterMutex);
    
    if (!m_sflpCounters.contains(sflpFileName)) {
        loadCountersFromFile(sflpFileName);
    }
    
    m_sflpCounters[sflpFileName].setCounter(type, newValue);
    saveCountersToFile(sflpFileName);
    
    emit operationCounterUpdated(sflpFileName, type, newValue);
}

void ProjectFileManagerEnhancements::updateAnalysisCounter(const QString& sflpFileName, 
                                                          AnalysisType type, int newValue) {
    QMutexLocker locker(&m_counterMutex);
    
    if (!m_sflpCounters.contains(sflpFileName)) {
        loadCountersFromFile(sflpFileName);
    }
    
    m_sflpCounters[sflpFileName].setCounter(type, newValue);
    saveCountersToFile(sflpFileName);
    
    emit analysisCounterUpdated(sflpFileName, type, newValue);
}

bool ProjectFileManagerEnhancements::saveToSflpFormat(const QString& sflpFileName, 
                                                      const QByteArray& data,
                                                      const QString& dataSegmentName) {
    if (sflpFileName.isEmpty() || data.isEmpty() || dataSegmentName.isEmpty()) {
        qWarning() << "Invalid parameters for saveToSflpFormat";
        return false;
    }
    
    return writeDataSegmentToSflp(sflpFileName, dataSegmentName, data);
}

QByteArray ProjectFileManagerEnhancements::loadFromSflpFormat(const QString& sflpFileName,
                                                             const QString& dataSegmentName) {
    if (sflpFileName.isEmpty() || dataSegmentName.isEmpty()) {
        qWarning() << "Invalid parameters for loadFromSflpFormat";
        return QByteArray();
    }
    
    return readDataSegmentFromSflp(sflpFileName, dataSegmentName);
}

QByteArray ProjectFileManagerEnhancements::compressData(const QByteArray& rawData) const {
    return SflpFileManager::compressData(rawData);
}

QByteArray ProjectFileManagerEnhancements::decompressData(const QByteArray& compressedData) const {
    return SflpFileManager::decompressData(compressedData);
}

// 私有方法实现
bool ProjectFileManagerEnhancements::writeDataSegmentToSflp(const QString& sflpFileName,
                                                            const QString& segmentName,
                                                            const QByteArray& data) {
    SflpFileManager* manager = getSflpManager(sflpFileName);
    if (!manager) {
        qWarning() << "Failed to get SFLP manager for file:" << sflpFileName;
        return false;
    }

    if (!manager->isOpen()) {
        if (!manager->openFile(QIODevice::ReadWrite)) {
            qWarning() << "Failed to open SFLP file:" << sflpFileName;
            releaseSflpManager(sflpFileName);
            return false;
        }
    }

    bool success = manager->writeDataSegment(segmentName, data);
    if (!success) {
        qWarning() << "Failed to write data segment:" << segmentName
                   << "Error:" << manager->getLastError();
    }

    return success;
}

QByteArray ProjectFileManagerEnhancements::readDataSegmentFromSflp(const QString& sflpFileName,
                                                                   const QString& segmentName) {
    SflpFileManager* manager = getSflpManager(sflpFileName);
    if (!manager) {
        qWarning() << "Failed to get SFLP manager for file:" << sflpFileName;
        return QByteArray();
    }

    if (!manager->isOpen()) {
        if (!manager->openFile(QIODevice::ReadOnly)) {
            qWarning() << "Failed to open SFLP file:" << sflpFileName;
            releaseSflpManager(sflpFileName);
            return QByteArray();
        }
    }

    QByteArray data = manager->readDataSegment(segmentName);
    if (data.isEmpty() && manager->hasError()) {
        qWarning() << "Failed to read data segment:" << segmentName
                   << "Error:" << manager->getLastError();
    }

    return data;
}

SflpFileManager* ProjectFileManagerEnhancements::getSflpManager(const QString& sflpFileName) {
    QMutexLocker locker(&m_managerMutex);

    if (!m_sflpManagers.contains(sflpFileName)) {
        SflpFileManager* manager = new SflpFileManager(sflpFileName, this);
        m_sflpManagers[sflpFileName] = manager;

        // 连接信号
        connect(manager, &SflpFileManager::errorOccurred,
                this, [this, sflpFileName](const QString& error) {
                    emit sflpFileCorrupted(sflpFileName, error);
                });
    }

    return m_sflpManagers[sflpFileName];
}

void ProjectFileManagerEnhancements::releaseSflpManager(const QString& sflpFileName) {
    QMutexLocker locker(&m_managerMutex);

    if (m_sflpManagers.contains(sflpFileName)) {
        SflpFileManager* manager = m_sflpManagers[sflpFileName];
        manager->closeFile();
        // 不删除管理器，保持缓存以提高性能
    }
}

void ProjectFileManagerEnhancements::cleanupSflpManagers() {
    QMutexLocker locker(&m_managerMutex);

    for (auto it = m_sflpManagers.begin(); it != m_sflpManagers.end(); ++it) {
        it.value()->closeFile();
        it.value()->deleteLater();
    }
    m_sflpManagers.clear();
}

void ProjectFileManagerEnhancements::updateFileRelationship(const QString& parent, const QString& child) {
    QMutexLocker locker(&m_relationMutex);

    // 添加父子关系
    if (!m_childrenMap[parent].contains(child)) {
        m_childrenMap[parent].append(child);
    }
    m_parentMap[child] = parent;

    emit fileRelationshipChanged();
}

void ProjectFileManagerEnhancements::addFileRelation(const QString& parent, const QString& child) {
    updateFileRelationship(parent, child);
}

void ProjectFileManagerEnhancements::removeFileRelation(const QString& parent, const QString& child) {
    QMutexLocker locker(&m_relationMutex);

    m_childrenMap[parent].removeAll(child);
    if (m_childrenMap[parent].isEmpty()) {
        m_childrenMap.remove(parent);
    }
    m_parentMap.remove(child);

    emit fileRelationshipChanged();
}

void ProjectFileManagerEnhancements::loadCountersFromFile(const QString& sflpFileName) {
    // 从SFLP文件加载计数器数据
    QString segmentName = "operation_counters";
    QByteArray counterData = readDataSegmentFromSflp(sflpFileName, segmentName);

    OperationCounters counters;
    if (!counterData.isEmpty()) {
        QByteArray rawData = decompressData(counterData);
        if (!rawData.isEmpty()) {
            counters.deserialize(rawData);
        }
    }

    m_sflpCounters[sflpFileName] = counters;
}

void ProjectFileManagerEnhancements::saveCountersToFile(const QString& sflpFileName) {
    if (!m_sflpCounters.contains(sflpFileName)) {
        return;
    }

    const OperationCounters& counters = m_sflpCounters[sflpFileName];
    QByteArray rawData = counters.serialize();
    QByteArray compressedData = compressData(rawData);

    QString segmentName = "operation_counters";
    writeDataSegmentToSflp(sflpFileName, segmentName, compressedData);
}

bool ProjectFileManagerEnhancements::validateOperationData(const PlotDataCollection& plotData) const {
    return plotData.isValid() && plotData.calculateSize() > 0;
}

bool ProjectFileManagerEnhancements::validateAnalysisData(const AnalysisResults& results) const {
    return results.isValid();
}

bool ProjectFileManagerEnhancements::validateFileName(const QString& fileName) const {
    return UnifiedFileUtils::validateFileName(fileName);
}

QString ProjectFileManagerEnhancements::extractBaseOperationName(const QString& analysisName) const {
    // 从分析名称中提取基础操作名称
    // 例如：从 "SFL240101_001_al1_cr2_DecAna_1" 提取 "SFL240101_001_al1_cr2"
    QRegularExpression re("^(.+)_(DecAna|SpeAna)_\\d+$");
    QRegularExpressionMatch match = re.match(analysisName);

    if (match.hasMatch()) {
        return match.captured(1);
    }

    return analysisName;
}

QString ProjectFileManagerEnhancements::generateOperationSegmentName(const QString& operationName) const {
    return QString("op_%1").arg(operationName);
}

QString ProjectFileManagerEnhancements::generateAnalysisSegmentName(const QString& analysisName) const {
    return QString("ana_%1").arg(analysisName);
}

bool ProjectFileManagerEnhancements::ensureSflpFileExists(const QString& sflpFileName) {
    if (QFile::exists(sflpFileName)) {
        return true;
    }

    return SflpFileManager::createEmptyFile(sflpFileName);
}

void ProjectFileManagerEnhancements::logFileOperation(const QString& operation,
                                                     const QString& fileName,
                                                     bool success) {
    if (success) {
        qDebug() << "File operation succeeded:" << operation << "on" << fileName;
    } else {
        qWarning() << "File operation failed:" << operation << "on" << fileName;
    }
}
