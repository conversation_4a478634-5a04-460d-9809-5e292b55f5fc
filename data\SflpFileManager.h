#ifndef SFLPFILEMANAGER_H
#define SFLPFILEMANAGER_H

#include <QObject>
#include <QString>
#include <QFile>
#include <QMap>
#include <QMutex>
#include <QIODevice>
#include "UnifiedFileStructures.h"

class SflpFileManager : public QObject
{
    Q_OBJECT

public:
    explicit SflpFileManager(const QString& fileName, QObject* parent = nullptr);
    ~SflpFileManager();

    // 文件操作
    bool openFile(QIODevice::OpenMode mode = QIODevice::ReadWrite);
    void closeFile();
    bool isOpen() const;
    bool exists() const;

    // 数据段操作
    bool writeDataSegment(const QString& segmentName, const QByteArray& data);
    QByteArray readDataSegment(const QString& segmentName);
    bool removeDataSegment(const QString& segmentName);
    QStringList getDataSegmentNames() const;
    bool hasDataSegment(const QString& segmentName) const;

    // 压缩数据操作
    bool writeCompressedDataSegment(const QString& segmentName, const QByteArray& rawData);
    QByteArray readCompressedDataSegment(const QString& segmentName);

    // 索引管理
    bool rebuildIndex();
    bool validateFile() const;
    bool validateDataSegment(const QString& segmentName) const;
    qint64 getFileSize() const;
    qint64 getDataSegmentSize(const QString& segmentName) const;

    // 文件信息
    SflpFileHeader getFileHeader() const;
    QMap<QString, DataSegmentIndex> getDataSegmentIndex() const;
    int getDataSegmentCount() const;

    // 错误处理
    QString getLastError() const;
    bool hasError() const;
    void clearError();

    // 静态工具方法
    static bool createEmptyFile(const QString& fileName);
    static bool isValidSflpFile(const QString& fileName);
    static QByteArray compressData(const QByteArray& data);
    static QByteArray decompressData(const QByteArray& compressedData);

signals:
    void dataSegmentWritten(const QString& segmentName, qint64 size);
    void dataSegmentRemoved(const QString& segmentName);
    void fileHeaderUpdated();
    void indexRebuilt();
    void errorOccurred(const QString& error);

private slots:
    void handleFileError();

private:
    QString m_fileName;
    QFile* m_file;
    SflpFileHeader m_header;
    QMap<QString, DataSegmentIndex> m_indexMap;
    QString m_lastError;
    mutable QMutex m_fileMutex;
    bool m_isModified;

    // 内部文件操作
    bool readHeader();
    bool writeHeader();
    bool readIndex();
    bool writeIndex();
    bool updateFileSize();

    // 数据段内部操作
    bool writeDataSegmentInternal(const QString& segmentName, 
                                 const QByteArray& data, 
                                 bool compressed = false);
    QByteArray readDataSegmentInternal(const QString& segmentName);
    qint64 findFreeSpace(qint64 requiredSize);
    bool compactFile();

    // 校验和计算
    quint32 calculateDataChecksum(const QByteArray& data) const;
    bool verifyDataChecksum(const QString& segmentName, const QByteArray& data) const;

    // 错误处理
    void setError(const QString& error);
    bool checkFileIntegrity() const;

    // 索引管理
    void updateDataSegmentIndex(const QString& segmentName, 
                               qint64 offset, 
                               qint64 compressedSize, 
                               qint64 originalSize,
                               quint32 checksum);
    void removeDataSegmentIndex(const QString& segmentName);
    qint64 calculateIndexOffset() const;

    // 文件锁定
    bool lockFile();
    void unlockFile();

    // 禁用拷贝构造和赋值
    SflpFileManager(const SflpFileManager&) = delete;
    SflpFileManager& operator=(const SflpFileManager&) = delete;
};

#endif // SFLPFILEMANAGER_H
