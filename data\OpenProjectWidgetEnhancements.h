#ifndef OPENPROJECTWIDGETENHANCEMENTS_H
#define OPENPROJECTWIDGETENHANCEMENTS_H

// 这个文件展示了需要添加到现有OpenProjectWidget类中的增强功能
// 实际实现时，应该将这些接口和实现直接添加到OpenProjectWidget.h/cpp中

#include <QWidget>
#include <QTreeView>
#include <QStandardItemModel>
#include <QStandardItem>
#include <QSortFilterProxyModel>
#include <QMap>
#include <QMutex>
#include "UnifiedFileStructures.h"

class ProjectFileManager;
class FileNameManager;

// Tab类型枚举
enum class TabType {
    Acquire,
    Process,
    Analysis
};

// 需要添加到OpenProjectWidget类中的新接口
class OpenProjectWidgetEnhancements : public QWidget
{
    Q_OBJECT

public:
    // 新增：刷新文件树（支持虚拟节点）
    void refreshFileTreeWithVirtualNodes();
    
    // 新增：文件类型过滤
    void setFileTypeFilter(TabType currentTab);
    
    // 新增：虚拟节点管理
    void expandVirtualNodes(bool expand = true);
    void collapseVirtualNodes();
    
    // 新增：文件选择和导航
    bool selectFileInTree(const QString& fileName);
    QString getCurrentSelectedFile() const;
    QStringList getSelectedFiles() const;
    
    // 新增：文件树状态管理
    void saveTreeState();
    void restoreTreeState();
    void resetTreeState();
    
    // 新增：搜索和过滤
    void setSearchFilter(const QString& searchText);
    void clearSearchFilter();
    bool isFilterActive() const;

signals:
    void virtualNodeSelected(const QString& nodeName, FileType nodeType);
    void fileTreeRefreshed();
    void filterChanged(TabType currentTab);
    void searchFilterApplied(const QString& searchText);

private slots:
    void onItemSelectionChanged();
    void onItemDoubleClicked(const QModelIndex& index);
    void onContextMenuRequested(const QPoint& pos);
    void onFileRelationshipChanged();

private:
    // 现有成员变量保持不变...
    QTreeView* treeView;
    QStandardItemModel* treeModel;
    QSortFilterProxyModel* proxyModel;
    ProjectFileManager* projectFileManager;
    
    // 新增：虚拟节点管理
    QMap<QString, QStandardItem*> m_virtualNodeMap;
    QMap<QString, QStringList> m_expandedNodes;
    mutable QMutex m_treeMutex;
    
    // 新增：过滤和搜索
    TabType m_currentTabType;
    QString m_searchFilter;
    bool m_filterActive;
    
    // 新增：树状态管理
    QMap<QString, bool> m_nodeExpandState;
    QString m_lastSelectedFile;
    
    // 新增：虚拟节点处理
    void addVirtualChildren(QStandardItem* parentItem, const QString& sflpFileName);
    void addOperationChildren(QStandardItem* parentItem, const QString& sflpFileName);
    void addAnalysisChildren(QStandardItem* operationItem, const QString& operationName);
    
    // 新增：文件类型识别和创建
    QStandardItem* createFileItem(const QString& fileName);
    QStandardItem* createOperationItem(const QString& operationName);
    QStandardItem* createAnalysisItem(const QString& analysisName, AnalysisType type);
    QStandardItem* createSplitItem(const QString& splitName);
    
    // 新增：图标和显示管理
    QIcon getFileTypeIcon(FileType fileType) const;
    QString getDisplayName(const QString& fileName, FileType fileType) const;
    void updateItemAppearance(QStandardItem* item, FileType fileType);
    
    // 新增：过滤实现
    void hideItemsByType(const QList<FileType>& typesToHide);
    void showAllItems();
    void applyCurrentFilter();
    bool shouldShowItem(QStandardItem* item, TabType tabType) const;
    
    // 新增：自然排序
    void applySortingToTree();
    void sortItemChildren(QStandardItem* parentItem);
    bool naturalCompare(const QString& str1, const QString& str2) const;
    
    // 新增：树结构管理
    void clearTreeModel();
    void buildTreeStructure();
    void refreshSingleFile(const QString& sflpFileName);
    void removeFileFromTree(const QString& fileName);
    
    // 新增：选择和导航
    QStandardItem* findItemByFileName(const QString& fileName) const;
    QStandardItem* findItemRecursive(QStandardItem* parent, const QString& fileName) const;
    void selectItem(QStandardItem* item);
    void ensureItemVisible(QStandardItem* item);
    
    // 新增：上下文菜单
    void showContextMenu(const QPoint& pos);
    void createContextMenuActions();
    QMenu* createFileContextMenu(const QString& fileName, FileType fileType);
    
    // 新增：数据验证
    bool isValidSflpFile(const QString& fileName) const;
    bool isVirtualNode(const QString& nodeName) const;
    FileType getItemFileType(QStandardItem* item) const;
    
    // 新增：性能优化
    void beginTreeUpdate();
    void endTreeUpdate();
    void batchUpdateItems(const QList<QPair<QStandardItem*, QString>>& updates);
    
    // 新增：错误处理
    void handleTreeError(const QString& operation, const QString& error);
    void logTreeOperation(const QString& operation, bool success);
    
    // 新增：工具方法
    QString extractFileNameFromItem(QStandardItem* item) const;
    QStringList getAllSflpFiles() const;
    void updateFileRelationships();
    void refreshFileMetadata(const QString& fileName);
};

// 自定义排序代理模型
class NaturalSortProxyModel : public QSortFilterProxyModel
{
    Q_OBJECT

public:
    explicit NaturalSortProxyModel(QObject* parent = nullptr);

protected:
    bool lessThan(const QModelIndex& left, const QModelIndex& right) const override;
    bool filterAcceptsRow(int sourceRow, const QModelIndex& sourceParent) const override;

private:
    bool naturalCompare(const QString& str1, const QString& str2) const;
    int compareNumericPart(const QString& str1, int pos1, const QString& str2, int pos2) const;
};

#endif // OPENPROJECTWIDGETENHANCEMENTS_H
