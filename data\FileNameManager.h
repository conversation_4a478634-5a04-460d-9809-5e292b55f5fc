#ifndef FILENAMEMANAGER_H
#define FILENAMEMANAGER_H

#include <QObject>
#include <QString>
#include <QDate>
#include <QMap>
#include <QMutex>
#include "UnifiedFileStructures.h"

class AppConfig;

class FileNameManager : public QObject
{
    Q_OBJECT

public:
    static FileNameManager* getInstance();
    ~FileNameManager();

    // 项目前缀管理（简化版）
    QString getProjectPrefix() const;

    // 工作区文件命名
    QString generateWorkspaceFileName() const;
    QString generateWorkspaceFileName(const QDate& date) const;

    // 工程文件命名
    QString generateProjectFileName(int sequence) const;
    QString generateProjectFileName(const QDate& date, int sequence) const;

    // 操作序列命名
    QString generateOperationName(const QString& baseFileName, 
                                 const OperationSequence& sequence) const;

    // 拟合数据命名（DecAna_{序号}/SpeAna_{序号}/FluAna_{序号}格式）
    QString generateDecayAnalysisName(const QString& baseFileName,
                                     int sequence) const;
    QString generateSpectralAnalysisName(const QString& baseFileName,
                                        int sequence) const;
    QString generateFluorescenceAnalysisName(const QString& baseFileName,
                                            int sequence) const;

    // Split文件命名
    QString generateSplitFileName(const QString& originalFileName, 
                                 int splitSequence) const;

    // 显示名称生成
    QString generateDisplayName(const QString& fileName, 
                               FileType fileType) const;

    // 序列号管理（sflp文件范围）
    int getNextProjectSequence() const;
    int getNextOperationSequence(const QString& sflpFileName, 
                                OperationType operationType) const;
    int getNextAnalysisSequence(const QString& sflpFileName, 
                               AnalysisType analysisType) const;

    // 文件名验证和处理
    bool validateFileName(const QString& fileName) const;
    QString sanitizeFileName(const QString& fileName) const;

    // 文件类型识别
    FileType identifyFileType(const QString& fileName) const;
    bool isOperationFile(const QString& fileName) const;
    bool isAnalysisFile(const QString& fileName) const;

    // 序列号解析
    int extractSequenceNumber(const QString& fileName, const QString& pattern) const;
    OperationSequence parseOperationSequence(const QString& fileName) const;

    
    // 序列号管理
    int getNextSequenceForType(const QString& sflpFileName, const QString& typePrefix) const;
    void updateSequenceCounter(const QString& sflpFileName, const QString& typePrefix, int value) const;

    // 配置管理
    void initializeFromConfig();
    QString getConfigValue(const QString& key, const QString& defaultValue) const;

    // 文件名模式匹配
    bool matchesPattern(const QString& fileName, const QString& pattern) const;
    QStringList getExistingFiles(const QString& directory, const QString& pattern) const;

    // 显示名称提取
    QString extractOperationDisplayName(const QString& fileName) const;
    QString extractAnalysisDisplayName(const QString& fileName) const;

signals:
    void fileNameGenerated(const QString& fileName, FileType fileType);
    void sequenceNumberUpdated(const QString& sflpFileName, OperationType type, int newValue);
    void sequenceNumberUpdated(const QString& sflpFileName, AnalysisType type, int newValue);

private:
    explicit FileNameManager(QObject* parent = nullptr);
    static FileNameManager* s_instance;
    static QMutex s_mutex;

    AppConfig* m_appConfig;
    mutable QMutex m_operationMutex;

    // 内部辅助方法
    QString formatDateString(const QDate& date) const;
    bool isValidFileName(const QString& fileName) const;
    QString getDefaultProjectPrefix() const;
    QString extractBaseFileName(const QString& fileName) const;
    QString generateUniqueFileName(const QString& baseName, const QString& extension) const;
    // 禁用拷贝构造和赋值
    FileNameManager(const FileNameManager&) = delete;
    FileNameManager& operator=(const FileNameManager&) = delete;
};

#endif // FILENAMEMANAGER_H
