#ifndef PROJECTFILEMANAGERENHANCEMENTS_H
#define PROJECTFILEMANAGERENHANCEMENTS_H

// 这个文件展示了需要添加到现有ProjectFileManager类中的增强功能
// 实际实现时，应该将这些接口和实现直接添加到ProjectFileManager.h/cpp中

#include <QObject>
#include <QString>
#include <QMap>
#include <QMutex>
#include "UnifiedFileStructures.h"
#include "FileNameManager.h"
#include "SflpFileManager.h"

// 需要添加到ProjectFileManager类中的新接口
class ProjectFileManagerEnhancements : public QObject
{
    Q_OBJECT

public:
    // 新增：统一文件操作接口
    bool saveOperationData(const QString& sflpFileName, 
                          const OperationSequence& sequence,
                          const PlotDataCollection& plotData);
    
    bool saveAnalysisData(const QString& sflpFileName,
                         const QString& baseOperationName,
                         AnalysisType analysisType,
                         const AnalysisResults& results);
    
    PlotDataCollection loadOperationData(const QString& operationName);
    AnalysisResults loadAnalysisData(const QString& analysisName);

    // 新增：文件关系管理
    QStringList getChildFiles(const QString& parentFile) const;
    QString getParentFile(const QString& childFile) const;
    FileType getFileType(const QString& fileName) const;

    // 新增：操作计数器管理（sflp文件范围）
    OperationCounters getOperationCounters(const QString& sflpFileName) const;
    void updateOperationCounter(const QString& sflpFileName, 
                               OperationType type, int newValue);
    void updateAnalysisCounter(const QString& sflpFileName, 
                              AnalysisType type, int newValue);

    // 新增：SFLP格式支持
    bool saveToSflpFormat(const QString& sflpFileName, 
                         const QByteArray& data,
                         const QString& dataSegmentName);
    QByteArray loadFromSflpFormat(const QString& sflpFileName,
                                 const QString& dataSegmentName);

    // 新增：压缩数据管理
    QByteArray compressData(const QByteArray& rawData) const;
    QByteArray decompressData(const QByteArray& compressedData) const;

    // 新增：文件验证和完整性检查
    bool validateSflpFile(const QString& sflpFileName) const;
    bool repairSflpFile(const QString& sflpFileName);
    
    // 新增：批量操作支持
    bool saveBatchOperationData(const QMap<QString, PlotDataCollection>& operationDataMap);
    QMap<QString, PlotDataCollection> loadBatchOperationData(const QStringList& operationNames);

signals:
    void operationDataSaved(const QString& operationName);
    void analysisDataSaved(const QString& analysisName);
    void fileRelationshipChanged();
    void operationCounterUpdated(const QString& sflpFileName, OperationType type, int newValue);
    void analysisCounterUpdated(const QString& sflpFileName, AnalysisType type, int newValue);
    void sflpFileCreated(const QString& sflpFileName);
    void sflpFileCorrupted(const QString& sflpFileName, const QString& error);

private:
    // 新增：文件关系缓存（简化版）
    QMap<QString, QStringList> m_childrenMap; // 父文件→子项列表
    QMap<QString, QString> m_parentMap;       // 子项→父文件
    mutable QMutex m_relationMutex;

    // 新增：操作计数器缓存（按sflp文件管理）
    QMap<QString, OperationCounters> m_sflpCounters;
    mutable QMutex m_counterMutex;

    // 新增：SFLP文件管理器缓存
    QMap<QString, SflpFileManager*> m_sflpManagers;
    mutable QMutex m_managerMutex;

    // 新增：内部实现方法
    bool writeDataSegmentToSflp(const QString& sflpFileName,
                                const QString& segmentName,
                                const QByteArray& data);
    QByteArray readDataSegmentFromSflp(const QString& sflpFileName,
                                      const QString& segmentName);
    void initializeCountersFromSflp(const QString& sflpFileName);
    void updateFileRelationship(const QString& parent, const QString& child);
    
    // SFLP文件管理器获取和管理
    SflpFileManager* getSflpManager(const QString& sflpFileName);
    void releaseSflpManager(const QString& sflpFileName);
    void cleanupSflpManagers();
    
    // 文件关系管理
    void addFileRelation(const QString& parent, const QString& child);
    void removeFileRelation(const QString& parent, const QString& child);
    void refreshFileRelations(const QString& sflpFileName);
    
    // 计数器管理
    void loadCountersFromFile(const QString& sflpFileName);
    void saveCountersToFile(const QString& sflpFileName);
    void incrementOperationCounter(const QString& sflpFileName, OperationType type);
    void incrementAnalysisCounter(const QString& sflpFileName, AnalysisType type);
    
    // 错误处理和恢复
    bool handleSflpError(const QString& sflpFileName, const QString& operation);
    void logFileOperation(const QString& operation, const QString& fileName, bool success);
    
    // 数据验证
    bool validateOperationData(const PlotDataCollection& plotData) const;
    bool validateAnalysisData(const AnalysisResults& results) const;
    bool validateFileName(const QString& fileName) const;
    
    // 内部工具方法
    QString extractBaseOperationName(const QString& analysisName) const;
    QString generateOperationSegmentName(const QString& operationName) const;
    QString generateAnalysisSegmentName(const QString& analysisName) const;
    bool ensureSflpFileExists(const QString& sflpFileName);
};

#endif // PROJECTFILEMANAGERENHANCEMENTS_H
