#include "Analysis.h"
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>
#include <QPushButton>
#include <QStackedWidget>
#include <QGroupBox>
#include <QLabel>
#include <QListWidget>
#include <QTreeWidget>
#include <QFrame>
#include <QFileDialog>
#include <QFileSystemModel>
#include <QTreeView>
#include <QLineEdit>
#include <QComboBox>
#include <QMessageBox>
#include <QFileInfo>
#include <QDebug>
#include "OpenProjectWidget.h" // 添加头文件
#include "CustomizePlot.h"
#include "ProjectFileManager.h"
#include "MeasureDataHandler.h"
#include "analysis/FitManager.h"
#include "analysis/FitCurveDisplay.h"
#include "Constants.h"
#include "SharedDataManager.h"
#include "ThemeManager.h"
#include "IRFImporter.h" // 添加IRF导入器头文件
#include "data/UnifiedFileStructures.h"


QWidget* AnalysisTab::createFluorescenceMapPageWidget() {
    QWidget* widget = new QWidget();
    widget->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);  // 设置大小策略为自适应
    widget->setMinimumWidth(0); // 显式设置最小宽度为0
    QVBoxLayout* mainLayout = new QVBoxLayout(widget);

    QLabel* todoLabel = new QLabel("TODO: Implement this widget");
    mainLayout->addWidget(todoLabel);

    return widget;
}

QWidget* AnalysisTab::createSpectralCurvePageWidget() {

    QWidget* widget = new QWidget();
    widget->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);  // 设置大小策略为自适应
    QVBoxLayout* mainLayout = new QVBoxLayout(widget);

    // 分析模型部分
    QGroupBox* analysisModelGroup = new QGroupBox();
    QVBoxLayout* analysisModelLayout = new QVBoxLayout(analysisModelGroup);

    // 第一行布局 - Analysis Model和下拉框
    QHBoxLayout* firstRowLayout = new QHBoxLayout();
    QLabel* modelTitleLabel = new QLabel("Analysis Model:");
    firstRowLayout->addWidget(modelTitleLabel);  // 左对齐

    firstRowLayout->addStretch();  // 添加伸缩因子使下拉框右对齐

    m_fittingModelComboBox = new QComboBox();
    m_fittingModelComboBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);  // 设置大小策略为自适应
    m_fittingModelComboBox->addItem("Gaussian Function");  // 默认选项
    m_fittingModelComboBox->addItem("Lorentz Function");
    m_fittingModelComboBox->addItem("Voigt Function");
    m_fittingModelComboBox->addItem("Polynomial Function");
    firstRowLayout->addWidget(m_fittingModelComboBox);

    // 添加"Cnts"标签
    QLabel* cntsLabel = new QLabel("     ");
    firstRowLayout->addWidget(cntsLabel, 0, Qt::AlignRight);

    analysisModelLayout->addLayout(firstRowLayout);

    // 第二行布局 - Function Expression
    QHBoxLayout* secondRowLayout = new QHBoxLayout();
    QLabel* functionExpressionLabel = new QLabel("Function Expression:");
    secondRowLayout->addWidget(functionExpressionLabel);  // 左对齐
    secondRowLayout->addStretch();  // 添加伸缩因子使表达式右对齐

    QLabel* functionExpressionValue = new QLabel();
    functionExpressionValue->setTextFormat(Qt::RichText);
    functionExpressionValue->setText(
        "<div style='font-family: Times New Roman; font-size: 8pt; line-height: 1.1;'>"
        "G(λ) = <span style='font-size: 9pt;'>∑</span><sub>i=1</sub><sup>n</sup>"
        " A<sub>i</sub> exp(-4ln2·(λ - λ<sub>0i</sub>)<sup>2</sup> / FWHM<sub>i</sub><sup>2</sup>) + C"
        "</div>"
        );
    functionExpressionValue->setSizePolicy(QSizePolicy::MinimumExpanding, QSizePolicy::Preferred);
    functionExpressionValue->setMinimumWidth(0);
    secondRowLayout->addWidget(functionExpressionValue);

    // 添加"Cnts"标签
    QLabel* cntsLabel2 = new QLabel("     ");
    secondRowLayout->addWidget(cntsLabel2, 0, Qt::AlignRight);

    analysisModelLayout->addLayout(secondRowLayout);

    // 第三行布局 - Model Parameters
    QHBoxLayout* thirdRowLayout = new QHBoxLayout();
    QLabel* modelParametersLabel = new QLabel("Model Parameters (n):");
    thirdRowLayout->addWidget(modelParametersLabel);  // 左对齐

    thirdRowLayout->addStretch();  // 添加伸缩因子使输入框右对齐

    QSpinBox* modelParametersSpinBox = new QSpinBox();
    modelParametersSpinBox->setValue(1);
    modelParametersSpinBox->setMinimum(1);
    modelParametersSpinBox->setMaximum(5);
    modelParametersSpinBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    modelParametersSpinBox->setMinimumWidth(50);  // 设置最小宽度为100像素
    thirdRowLayout->addWidget(modelParametersSpinBox);

    // 添加"Cnts"标签
    QLabel* cntsLabel3 = new QLabel("     ");
    thirdRowLayout->addWidget(cntsLabel3, 0, Qt::AlignRight);

    analysisModelLayout->addLayout(thirdRowLayout);

    // 第四行布局 - Iterations
    QHBoxLayout* fourthRowLayout = new QHBoxLayout();
    QLabel* iterationsLabel = new QLabel("Iterations:");
    fourthRowLayout->addWidget(iterationsLabel);  // 左对齐

    fourthRowLayout->addStretch();  // 添加伸缩因子使输入框右对齐

    QSpinBox* iterationsSpinBox = new QSpinBox();
    iterationsSpinBox->setValue(0);
    iterationsSpinBox->setMinimum(0);
    iterationsSpinBox->setMaximum(100000);
    iterationsSpinBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);  // 设置大小策略为自适应
    fourthRowLayout->addWidget(iterationsSpinBox);

    // 添加"Cnts"标签
    QLabel* cntsLabel4 = new QLabel("     ");
    fourthRowLayout->addWidget(cntsLabel4, 0, Qt::AlignRight);

    analysisModelLayout->addLayout(fourthRowLayout);

    // 第五行布局 - Fitting Range
    QHBoxLayout* fifthRowLayout = new QHBoxLayout();
    QLabel* fittingRangeLabel = new QLabel("Fitting Range:");
    fifthRowLayout->addWidget(fittingRangeLabel);  // 左对齐

    fifthRowLayout->addStretch();  // 添加伸缩因子使输入框右对齐

    QDoubleSpinBox* rangeFromSpinBox = new QDoubleSpinBox();
    rangeFromSpinBox->setDecimals(2);
    rangeFromSpinBox->setValue(0.00);
    rangeFromSpinBox->setMinimum(0.00);
    rangeFromSpinBox->setMaximum(10000.00);
    rangeFromSpinBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);  // 设置大小策略为自适应
    fifthRowLayout->addWidget(new QLabel("From:"));
    fifthRowLayout->addWidget(rangeFromSpinBox);
    fifthRowLayout->addWidget(new QLabel("to:"));

    QDoubleSpinBox* rangeToSpinBox = new QDoubleSpinBox();
    rangeToSpinBox->setDecimals(2);
    rangeToSpinBox->setValue(0.00);
    rangeToSpinBox->setMinimum(0.00);
    rangeToSpinBox->setMaximum(10000.00);
    rangeToSpinBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);  // 设置大小策略为自适应
    fifthRowLayout->addWidget(rangeToSpinBox);

    // 添加"Cnts"标签
    QLabel* cntsLabel5 = new QLabel("nm");
    fifthRowLayout->addWidget(cntsLabel5, 0, Qt::AlignRight);

    analysisModelLayout->addLayout(fifthRowLayout);

    mainLayout->addWidget(analysisModelGroup);

    // 参数表格部分
    QTableWidget* parameterTable = new QTableWidget();
    parameterTable->setColumnCount(3);
    parameterTable->setRowCount(4);  // 从7改为4，因为现在只有4个参数
    QStringList horizontalHeaders;
    horizontalHeaders << "Parameter" << "Value" << "Fix";
    parameterTable->setHorizontalHeaderLabels(horizontalHeaders);

    // 设置列的伸缩模式为自适应，与createAnalysisWidget中的方式一致
    parameterTable->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Stretch); // Parameter列自适应
    parameterTable->horizontalHeader()->setSectionResizeMode(1, QHeaderView::Stretch); // Value列自适应
    parameterTable->horizontalHeader()->setSectionResizeMode(2, QHeaderView::Fixed);   // Fix列固定宽度

    // 只为Fix列设置固定宽度，因为它只需要小的固定空间
    parameterTable->setColumnWidth(2, 45);

    // 隐藏垂直表头
    parameterTable->verticalHeader()->setVisible(false);

    // 设置表格的大小策略为自适应
    parameterTable->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);

    QStringList verticalHeaders;
    verticalHeaders << "λ01 [nm]" << "FWHM1 [nm]" << "A1 [Cnts]" << "C [Cnts]";

    for (int row = 0; row < 4; ++row) {  // 循环次数从7改为4
        QTableWidgetItem* paramItem = new QTableWidgetItem(verticalHeaders[row]);
        paramItem->setTextAlignment(Qt::AlignLeft | Qt::AlignVCenter);  // 添加垂直居中
        QFont font = paramItem->font();
        font.setPointSize(font.pointSize() - 1);  // 字体大小减1
        paramItem->setFont(font);
        parameterTable->setItem(row, 0, paramItem);

        // 修改Value列为QDoubleSpinBox
        QDoubleSpinBox* valueSpinBox = new QDoubleSpinBox();
        valueSpinBox->setDecimals(4);
        valueSpinBox->setValue(0.0000);
        valueSpinBox->setMinimum(0.0);
        valueSpinBox->setMaximum(10000.0);
        valueSpinBox->setAlignment(Qt::AlignCenter);  // 添加这行使数字居中
        parameterTable->setCellWidget(row, 1, valueSpinBox);

        // 修改Fix列的样式与示例代码一致
        QPushButton* fixButton = new QPushButton("");
        fixButton->setCheckable(true);
        fixButton->setChecked(false);
        fixButton->setStyleSheet("padding: 2px; margin: 2px; min-width: 18px; max-width: 18px; text-align: center; vertical-align: middle;");

        connect(fixButton, &QPushButton::clicked, [fixButton]() {
            fixButton->setText(fixButton->isChecked() ? "✓" : "");
        });

        QWidget* fixButtonWidget = new QWidget();
        QHBoxLayout* fixButtonLayout = new QHBoxLayout(fixButtonWidget);
        fixButtonLayout->addWidget(fixButton);
        fixButtonLayout->setAlignment(Qt::AlignCenter);
        fixButtonLayout->setContentsMargins(0, 0, 0, 0);

        parameterTable->setCellWidget(row, 2, fixButtonWidget);

        // 添加⇔按钮到Parameter列的右侧
        QWidget* limitsButtonWidget = new QWidget();
        QHBoxLayout* limitsButtonLayout = new QHBoxLayout(limitsButtonWidget);
        limitsButtonLayout->setContentsMargins(0, 0, 0, 0);

        QPushButton* limitsButton = new QPushButton("⇔");
        limitsButton->setFixedWidth(50);  // 设置固定宽度为50像素
        limitsButton->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
        limitsButtonLayout->addWidget(limitsButton);
        limitsButtonLayout->setAlignment(limitsButton, Qt::AlignRight);

        // 将⇔按钮添加到Parameter列的右侧
        QWidget* paramCellWidget = new QWidget();
        QHBoxLayout* paramCellLayout = new QHBoxLayout(paramCellWidget);
        paramCellLayout->setContentsMargins(0, 0, 0, 0);
        paramCellLayout->addWidget(new QLabel(verticalHeaders[row]));  // 添加参数标签
        paramCellLayout->addStretch();  // 添加伸缩因子，使⇔按钮右对齐
        paramCellLayout->addWidget(limitsButtonWidget);

        parameterTable->setCellWidget(row, 0, paramCellWidget);

        // 创建弹出框
        QWidget* popupWidget = new QWidget();
        popupWidget->setWindowFlags(Qt::Popup);
        // 不设置固定高度，而是使用大小策略
        popupWidget->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
        QHBoxLayout* popupLayout = new QHBoxLayout(popupWidget);
        popupLayout->setSpacing(5);
        popupLayout->setContentsMargins(10, 5, 10, 5);

        // Min部分
        QLabel* minLabel = new QLabel("Min:");
        QFont minFont = minLabel->font();
        minFont.setPointSize(minFont.pointSize() - 1); // 减小一个字号
        minLabel->setFont(minFont);
        popupLayout->addWidget(minLabel);

        QPushButton* minCheckBox = new QPushButton("");
        minCheckBox->setCheckable(true);
        minCheckBox->setChecked(false);
        minCheckBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
        minCheckBox->setStyleSheet("padding: 2px; margin: 2px; text-align: center; vertical-align: middle;");
        popupLayout->addWidget(minCheckBox);

        QDoubleSpinBox* minSpinBox = new QDoubleSpinBox();
        minSpinBox->setDecimals(4);
        minSpinBox->setValue(0.0000);
        minSpinBox->setMinimum(0.0);
        minSpinBox->setMaximum(10000.0);
        minSpinBox->setEnabled(false);
        popupLayout->addWidget(minSpinBox);

        // Max部分
        QLabel* maxLabel = new QLabel("Max:");
        popupLayout->addWidget(maxLabel);

        QPushButton* maxCheckBox = new QPushButton("");
        maxCheckBox->setCheckable(true);
        maxCheckBox->setChecked(false);
        maxCheckBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
        maxCheckBox->setStyleSheet("padding: 2px; margin: 2px; text-align: center; vertical-align: middle;");
        popupLayout->addWidget(maxCheckBox);

        QDoubleSpinBox* maxSpinBox = new QDoubleSpinBox();
        maxSpinBox->setDecimals(4);
        maxSpinBox->setValue(0.0000);
        maxSpinBox->setMinimum(0.0);
        maxSpinBox->setMaximum(10000.0);
        maxSpinBox->setEnabled(false);
        popupLayout->addWidget(maxSpinBox);

        // Close按钮
        QPushButton* closeButton = new QPushButton("Close");
        popupLayout->addWidget(closeButton);

        // 连接信号槽
        connect(minCheckBox, &QPushButton::clicked, [minCheckBox, minSpinBox, limitsButton, maxCheckBox]() {
            minCheckBox->setText(minCheckBox->isChecked() ? "✓" : "");
            minSpinBox->setEnabled(minCheckBox->isChecked());
            if (minCheckBox->isChecked() || maxCheckBox->isChecked()) {
                limitsButton->setStyleSheet("background-color: #FF6666;");
            } else {
                limitsButton->setStyleSheet("background-color: #333333; color: #FFFFFF;");
            }
        });

        connect(maxCheckBox, &QPushButton::clicked, [maxCheckBox, maxSpinBox, limitsButton, minCheckBox]() {
            maxCheckBox->setText(maxCheckBox->isChecked() ? "✓" : "");
            maxSpinBox->setEnabled(maxCheckBox->isChecked());
            if (minCheckBox->isChecked() || maxCheckBox->isChecked()) {
                limitsButton->setStyleSheet("background-color: #FF6666;");
            } else {
                limitsButton->setStyleSheet("background-color: #333333; color: #FFFFFF;");
            }
        });

        connect(closeButton, &QPushButton::clicked, popupWidget, &QWidget::close);

        // 点击⇔按钮时显示弹出框
        connect(limitsButton, &QPushButton::clicked, [limitsButton, popupWidget]() {
            QPoint pos = limitsButton->mapToGlobal(QPoint(limitsButton->width(), 0));
            popupWidget->move(pos);
            popupWidget->show();
        });

        parameterTable->setCellWidget(row, 3, limitsButtonWidget);
    }

    mainLayout->addWidget(parameterTable);


    return widget;


}

QWidget* AnalysisTab::createDecayCurvePage() {
    QWidget* widget = new QWidget();
    widget->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);  // 设置大小策略为自适应
    widget->setMinimumWidth(0); // 显式设置最小宽度为0
    QVBoxLayout* mainLayout = new QVBoxLayout(widget);

    setupAnalysisMethodSection(mainLayout);

    QGroupBox* fittingGroup = new QGroupBox();
    QVBoxLayout* groupLayout = new QVBoxLayout(fittingGroup);

    setupFittingModelSection(groupLayout);
    setupIRFSection(groupLayout);
    setupAnalysisModeSection(groupLayout);
    setupExponentialModelSection(groupLayout);
    setupModelParametersSection(groupLayout);
    setupModelAlgorithmSection(groupLayout);
    setupIterationsSection(groupLayout);
    setupFittingRangeSection(groupLayout);

    mainLayout->addWidget(fittingGroup);
    setupParameterTable(mainLayout);
    setupActionButtons(mainLayout);


    // 创建参数模型和UI绑定器
    m_fitUIBinder = new FitUIBinder(this);

    // 绑定各个UI控件
    m_fitUIBinder->bindAnalysisMethod(analysisMethodComboBox);
    m_fitUIBinder->bindFittingModel(m_fittingModelComboBox);
    m_fitUIBinder->bindIRFParameters(m_t0SpinBox, m_fwhmSpinBox);
    m_fitUIBinder->bindAnalysisMode(m_analysisModeComboBox);
    m_fitUIBinder->bindExponentialModel(m_exponentialModelComboBox);
    m_fitUIBinder->bindModelParametersCount(m_modelParamsSpinBox);
    m_fitUIBinder->bindModelAlgorithm(m_modelAlgorithmComboBox);
    m_fitUIBinder->bindIterations(m_iterationsSpinBox);
    m_fitUIBinder->bindFittingRange(m_rangeFromSpinBox, m_rangeToSpinBox);
    m_fitUIBinder->bindParameterTable(m_parameterTable);

    return widget;
}

void AnalysisTab::setupAnalysisMethodSection(QVBoxLayout* mainLayout) {
    QGroupBox* group = new QGroupBox();
    QHBoxLayout* layout = new QHBoxLayout();

    // 创建标签并左对齐
    QLabel* label = new QLabel("Analytical Method:");
    layout->addWidget(label);

    // 添加伸缩因子，使下拉框右对齐
    layout->addStretch();

    // 创建下拉框并设置最小宽度
    analysisMethodComboBox = new QComboBox();
    analysisMethodComboBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);

    // 添加下拉框选项
    analysisMethodComboBox->addItem("Fitting");
    analysisMethodComboBox->addItem("Phasor");
    analysisMethodComboBox->addItem("Non-Fitting");

    // 添加下拉框到布局
    layout->addWidget(analysisMethodComboBox);

    // 添加空白标签，保留右侧空白
    QLabel* cntsLabel = new QLabel("      ");
    layout->addWidget(cntsLabel, 0, Qt::AlignRight);

    // 设置布局到组框
    group->setLayout(layout);

    // 将组框添加到主布局
    mainLayout->addWidget(group);
}

void AnalysisTab::setupFittingModelSection(QVBoxLayout* groupLayout) {
    QHBoxLayout* fittingModelLayout = new QHBoxLayout();
    QLabel* fittingModelLabel = new QLabel("Fitting Model:");

    m_fittingModelComboBox_1 = new QComboBox();

    m_fittingModelComboBox_1->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed); // 设置大小策略为自适应

    m_fittingModelComboBox_1->addItem("Tail Fitting");
    m_fittingModelComboBox_1->addItem("Convolution Fitting");
    fittingModelLayout->addWidget(fittingModelLabel); // 恢复 Fitting Model 标签并左对齐

    fittingModelLayout->addStretch(); // 添加伸缩因子，使下拉框右对齐
    fittingModelLayout->addWidget(m_fittingModelComboBox_1);

    QLabel* cntsLabel = new QLabel("      ");
    fittingModelLayout->addWidget(cntsLabel, 0, Qt::AlignRight); // 将 QLabel 右对齐
    groupLayout->addLayout(fittingModelLayout);
}

void AnalysisTab::setupIRFSection(QVBoxLayout* groupLayout) {
    // 第一行布局
    QHBoxLayout* firstRowLayout = new QHBoxLayout();
    QLabel* irfLabel = new QLabel("IRF:");
    firstRowLayout->addWidget(irfLabel);

    // 创建导入按钮
    QPushButton* importButton = new QPushButton("Import");
    importButton->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed); // 设置大小策略为自适应
    importButton->setMinimumWidth(70); // 添加最小宽度为70像素
    importButton->setEnabled(false); // 初始时按钮不可点击

    // 创建下拉框用于显示导入的IRF文件名
    QComboBox* irfComboBox = new QComboBox();
    irfComboBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed); // 设置大小策略为自适应
    irfComboBox->setEnabled(false); // 初始时下拉框不可用

    // 连接m_fittingModelComboBox的currentTextChanged信号
    connect(m_fittingModelComboBox_1, &QComboBox::currentTextChanged, this, [importButton, irfComboBox](const QString& text) {
        // 只有当选择了"Convolution Fitting"时，importButton才可点击
        bool isConvolutionFitting = (text == "Convolution Fitting");
        importButton->setEnabled(isConvolutionFitting);
        irfComboBox->setEnabled(isConvolutionFitting);
    });

    // 连接导入按钮的点击事件
    connect(importButton, &QPushButton::clicked, this, [this, irfComboBox]() {
        // 打开文件选择对话框
        QString filePath = QFileDialog::getOpenFileName(
            this,
            tr("Import IRF Data"),
            "",
            tr("Data Files (*.csv *.txt *.dat);;All Files (*)")
            );

        if (!filePath.isEmpty()) {
            // 使用IRFImporter导入数据
            bool success = IRFImporter::getInstance()->importIRFFromFile(filePath);

            if (success) {
                // 获取文件名并添加到下拉框
                QString fileName = QFileInfo(filePath).fileName();

                // 清空下拉框并添加新文件名
                irfComboBox->clear();
                irfComboBox->addItem(fileName);
                irfComboBox->setCurrentIndex(0);

                QMessageBox::information(
                    this,
                    tr("Import Successful"),
                    tr("IRF data imported successfully from %1.\nTotal data points: %2")
                        .arg(fileName)
                        .arg(IRFImporter::getInstance()->getIRFData().size())
                    );
            } else {
                QMessageBox::warning(
                    this,
                    tr("Import Failed"),
                    tr("Failed to import IRF data from %1.\nPlease check the file format.")
                        .arg(QFileInfo(filePath).fileName())
                    );
            }
        }
    });

    firstRowLayout->addStretch(); // 使后续控件右对齐
    firstRowLayout->addWidget(importButton);
    firstRowLayout->addWidget(irfComboBox);

    // 添加空白标签，保留右侧空白
    QLabel* cntsLabel = new QLabel("      ");
    firstRowLayout->addWidget(cntsLabel, 0, Qt::AlignRight); // 将 QLabel 右对齐

    groupLayout->addLayout(firstRowLayout);

    // 第二行布局
    QHBoxLayout* secondRowLayout = new QHBoxLayout();
    secondRowLayout->addStretch(); // 使控件右对齐

    m_t0SpinBox = new QDoubleSpinBox();
    QHBoxLayout* t0Layout = new QHBoxLayout();
    t0Layout->addWidget(new QLabel("  t0:  "));
    t0Layout->addWidget(m_t0SpinBox);
    m_t0SpinBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed); // 设置大小策略为自适应

    m_t0SpinBox->setDecimals(2); // 设置小数点后1位
    m_t0SpinBox->setValue(0.00); // 设置默认值为0.0
    m_t0SpinBox->setMinimum(0.00); // 设置最小值为0.0
    m_t0SpinBox->setMaximum(10000.00); // 设置最大值为1000.0

    m_fwhmSpinBox = new QDoubleSpinBox();
    QHBoxLayout* fwhmLayout = new QHBoxLayout();
    fwhmLayout->addWidget(new QLabel("FWHM:"));
    fwhmLayout->addWidget(m_fwhmSpinBox);
    m_fwhmSpinBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed); // 设置大小策略为自适应

    m_fwhmSpinBox->setDecimals(2); // 设置小数点后1位
    m_fwhmSpinBox->setValue(0.00); // 设置默认值为0.0
    m_fwhmSpinBox->setMinimum(0.00); // 设置最小值为0.0
    m_fwhmSpinBox->setMaximum(10000.00); // 设置最大值为1000.0

    secondRowLayout->addLayout(t0Layout);
    secondRowLayout->addLayout(fwhmLayout);

    // 在最右侧加入 " ns " 标签
    QLabel* nsLabel = new QLabel("ns   ");
    secondRowLayout->addWidget(nsLabel);

    groupLayout->addLayout(secondRowLayout);
}

void AnalysisTab::setupAnalysisModeSection(QVBoxLayout* groupLayout) {
    QHBoxLayout* layout = new QHBoxLayout();
    QLabel* label = new QLabel("Analysis Mode:");
    m_analysisModeComboBox = new QComboBox();
    layout->addWidget(label);

    layout->addStretch(); // 添加伸缩因子，使下拉框右对齐

    m_analysisModeComboBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed); // 设置大小策略为自适应

    m_analysisModeComboBox->addItem("Individual Analysis");
    m_analysisModeComboBox->addItem("Global Analysis");

    layout->addWidget(m_analysisModeComboBox);

    QLabel* cntsLabel = new QLabel("      ");
    layout->addWidget(cntsLabel);

    groupLayout->addLayout(layout);
}

void AnalysisTab::setupExponentialModelSection(QVBoxLayout* groupLayout) {
    QHBoxLayout* layout = new QHBoxLayout();
    QLabel* label = new QLabel("Exponential Model:");
    m_exponentialModelComboBox = new QComboBox();
    layout->addWidget(label);

    layout->addStretch(); // 添加伸缩因子，使下拉框右对齐

    m_exponentialModelComboBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed); // 设置大小策略为自适应

    m_exponentialModelComboBox->addItem("Exponential");
    m_exponentialModelComboBox->addItem("Stretched Exponential");

    layout->addWidget(m_exponentialModelComboBox);

    QLabel* cntsLabel = new QLabel("      ");
    layout->addWidget(cntsLabel);

    groupLayout->addLayout(layout);
}

void AnalysisTab::setupModelParametersSection(QVBoxLayout* groupLayout) {
    QHBoxLayout* layout = new QHBoxLayout();
    QLabel* label = new QLabel("Model Parameters n:  ");
    m_modelParamsSpinBox = new QSpinBox();
    layout->addWidget(label);

    layout->addStretch(); // 添加伸缩因子，使输入框右对齐

    m_modelParamsSpinBox->setValue(1); // 设置默认值为1
    m_modelParamsSpinBox->setMinimum(1); // 设置最小值为1
    m_modelParamsSpinBox->setMaximum(5); // 设置最大值为5
    m_modelParamsSpinBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed); // 设置大小策略为自适应
    m_modelParamsSpinBox->setMinimumWidth(50); // 设置最小宽度为50像素

    layout->addWidget(m_modelParamsSpinBox);

    QLabel* cntsLabel = new QLabel("      ");
    layout->addWidget(cntsLabel);
    groupLayout->addLayout(layout);
}

void AnalysisTab::setupModelAlgorithmSection(QVBoxLayout* groupLayout) {
    QHBoxLayout* layout = new QHBoxLayout();
    QLabel* label = new QLabel("Model Algorithm:");
    m_modelAlgorithmComboBox = new QComboBox();
    layout->addWidget(label);

    layout->addStretch(); // 添加伸缩因子，使下拉框右对齐

    m_modelAlgorithmComboBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed); // 设置大小策略为自适应

    m_modelAlgorithmComboBox->addItem("Least Square Method");
    m_modelAlgorithmComboBox->addItem("Maximum Likelihood Estimation");
    m_modelAlgorithmComboBox->addItem("Bayesian Analysis");

    layout->addWidget(m_modelAlgorithmComboBox);

    // 添加一个 QLabel
    QLabel* cntsLabel = new QLabel("      ");
    layout->addWidget(cntsLabel);

    groupLayout->addLayout(layout);
}

QHBoxLayout* AnalysisTab::createLabelComboRow(const QString& labelText, QComboBox*& comboBox) {
    QHBoxLayout* layout = new QHBoxLayout();
    QLabel* label = new QLabel(labelText);
    comboBox = new QComboBox();
    layout->addWidget(label);

    layout->addStretch(); // 添加伸缩因子，使下拉框右对齐


    layout->addWidget(comboBox);
    return layout;


}

QHBoxLayout* AnalysisTab::createLabelSpinBoxRow(const QString& labelText, QSpinBox*& spinBox) {
    QHBoxLayout* layout = new QHBoxLayout();
    QLabel* label = new QLabel(labelText);
    spinBox = new QSpinBox();
    layout->addWidget(label);
    layout->addWidget(spinBox);
    return layout;
}

QHBoxLayout* AnalysisTab::createLabelDoubleSpinBoxRow(const QString& labelText, QDoubleSpinBox*& spinBox) {
    QHBoxLayout* layout = new QHBoxLayout();
    QLabel* label = new QLabel(labelText);
    spinBox = new QDoubleSpinBox();
    layout->addWidget(label);
    layout->addWidget(spinBox);
    return layout;
}
void AnalysisTab::setupIterationsSection(QVBoxLayout* groupLayout) {
    QHBoxLayout* layout = new QHBoxLayout();

    // "Iterations:" 标签左对齐
    QLabel* iterationsLabel = new QLabel("Iterations:");
    layout->addWidget(iterationsLabel);

    // 添加一个伸缩因子，使后续控件右对齐
    layout->addStretch();

    // 创建 QSpinBox 并设置默认值
    m_iterationsSpinBox = new QSpinBox();


    //m_iterationsSpinBox->setMinimum(1); // 设置最小值为1
    m_iterationsSpinBox->setMaximum(1000000000); // 设置最大值为
    m_iterationsSpinBox->setValue(100000); // 设置默认值为100000
    m_iterationsSpinBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed); // 设置大小策略为自适应

    layout->addWidget(m_iterationsSpinBox);

    // "Cnts" 标签右对齐
    QLabel* cntsLabel = new QLabel("Cnts");
    layout->addWidget(cntsLabel);

    groupLayout->addLayout(layout);
}

void AnalysisTab::setupFittingRangeSection(QVBoxLayout* groupLayout) {
    QHBoxLayout* mainLayout = new QHBoxLayout();

    QLabel* rangeLabel = new QLabel("Fitting Range:");
    mainLayout->addWidget(rangeLabel);
    checkrange = new QCheckBox();

    mainLayout->addWidget(checkrange);
    m_rangeFromSpinBox = new QDoubleSpinBox();
    m_rangeFromSpinBox->setSingleStep(0.5);
    QHBoxLayout* fromLayout = new QHBoxLayout();
    fromLayout->addWidget(new QLabel("From:"));
    fromLayout->addWidget(m_rangeFromSpinBox);
    m_rangeFromSpinBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed); // 设置大小策略为自适应
    m_rangeFromSpinBox->setDecimals(2); // 设置小数点后1位
    m_rangeFromSpinBox->setValue(0.00); // 设置默认值为0.0
    m_rangeFromSpinBox->setMinimum(0.00); // 设置最小值为0.0
    m_rangeFromSpinBox->setMaximum(10000.00); // 设置最大值为1000.0

    m_rangeToSpinBox = new QDoubleSpinBox();
    m_rangeToSpinBox->setSingleStep(0.5);
    QHBoxLayout* toLayout = new QHBoxLayout();
    toLayout->addWidget(new QLabel("      to:"));
    toLayout->addWidget(m_rangeToSpinBox);
    m_rangeToSpinBox->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed); // 设置大小策略为自适应
    m_rangeToSpinBox->setDecimals(2); // 设置小数点后1位
    m_rangeToSpinBox->setValue(0.00); // 设置默认值为0.0
    m_rangeToSpinBox->setMinimum(0.00); // 设置最小值为0.0
    m_rangeToSpinBox->setMaximum(10000.00); // 设置最大值为1000.0

    // 添加一个伸缩因子，使后续控件右对齐
    mainLayout->addStretch();
    mainLayout->addLayout(fromLayout);
    mainLayout->addLayout(toLayout);


    QLabel* cntsLabel = new QLabel("ns   ");
    mainLayout->addWidget(cntsLabel);

    groupLayout->addLayout(mainLayout);

    // 连接信号槽
    connect(checkrange, &QCheckBox::stateChanged, this, &AnalysisTab::onCheckRangeStateChanged);
    connect(m_rangeFromSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &AnalysisTab::onRangeFromValueChanged);
    connect(m_rangeToSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged), this, &AnalysisTab::onRangeToValueChanged);
}

void AnalysisTab::setupParameterTable(QVBoxLayout* mainLayout) {
    QVBoxLayout* parameterTableLayout = new QVBoxLayout();
    m_parameterTable = new QTableWidget();
    m_parameterTable->setColumnCount(3);
    m_parameterTable->setRowCount(3);
    m_parameterTable->verticalHeader()->setVisible(false);
    m_parameterTable->setHorizontalHeaderLabels(QStringList() << "Parameter" << "Value" << "Fix");

    // 设置列的伸缩模式为自适应
    m_parameterTable->horizontalHeader()->setSectionResizeMode(0, QHeaderView::Stretch); // Parameter列自适应
    m_parameterTable->horizontalHeader()->setSectionResizeMode(1, QHeaderView::Stretch); // Value列自适应
    m_parameterTable->horizontalHeader()->setSectionResizeMode(2, QHeaderView::Fixed);   // Fix列固定宽度

    // 只为Fix列设置固定宽度，因为它只需要小的固定空间
    m_parameterTable->setColumnWidth(2, 45);

    //setupParameterRow(m_parameterTable, 0, "A1 [kCnts]");
    //setupParameterRow(m_parameterTable, 1, "τ1 [ns]");
    //setupParameterRow(m_parameterTable, 2, "B [Cnts]");

    // 设置表格的大小策略为自适应
    m_parameterTable->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);

    // 设置表格的最小高度，确保有足够的空间显示所有行
    m_parameterTable->setMinimumHeight(m_parameterTable->rowCount() * 30 + 30); // 30是每行的估计高度，加上表头

    parameterTableLayout->addWidget(m_parameterTable);
    mainLayout->addLayout(parameterTableLayout);
}

void AnalysisTab::setupParameterRow(QTableWidget* table, int row, const QString& label) {
    // QWidget* cellWidget = new QWidget();
    // QHBoxLayout* layout = new QHBoxLayout(cellWidget);

    // // 创建标签并设置其大小策略为自适应
    // QLabel* paramLabel = new QLabel(label);
    // paramLabel->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
    // layout->addWidget(paramLabel);

    // // 添加伸缩因子，使按钮右对齐
    // layout->addStretch();

    // // 创建按钮并设置固定宽度
    // QPushButton* limitsButton = new QPushButton("⇔");
    // limitsButton->setFixedWidth(50);
    // layout->addWidget(limitsButton);
    // layout->setContentsMargins(0, 0, 0, 0);

    // // 创建弹出框
    // QWidget* popupWidget = new QWidget();
    // popupWidget->setWindowFlags(Qt::Popup);
    // QHBoxLayout* popupLayout = new QHBoxLayout(popupWidget);
    // popupLayout->setContentsMargins(5, 5, 5, 5);

    // // Min部分
    // QLabel* minLabel = new QLabel("Min:");
    // popupLayout->addWidget(minLabel);

    // QPushButton* minCheckBox = new QPushButton("");
    // minCheckBox->setCheckable(true);
    // minCheckBox->setChecked(false);
    // minCheckBox->setStyleSheet("padding: 2px; margin: 2px; min-width: 18px; max-width: 18px; text-align: center; vertical-align: middle;");
    // popupLayout->addWidget(minCheckBox);

    // QDoubleSpinBox* minSpinBox = new QDoubleSpinBox();
    // minSpinBox->setDecimals(4);
    // minSpinBox->setValue(0.0000);
    // minSpinBox->setMinimum(0.0);
    // minSpinBox->setMaximum(10000.0);
    // minSpinBox->setEnabled(false);
    // popupLayout->addWidget(minSpinBox);

    // // Max部分
    // QLabel* maxLabel = new QLabel("Max:");
    // popupLayout->addWidget(maxLabel);

    // QPushButton* maxCheckBox = new QPushButton("");
    // maxCheckBox->setCheckable(true);
    // maxCheckBox->setChecked(false);
    // maxCheckBox->setStyleSheet("padding: 2px; margin: 2px; min-width: 18px; max-width: 18px; text-align: center; vertical-align: middle;");
    // popupLayout->addWidget(maxCheckBox);

    // QDoubleSpinBox* maxSpinBox = new QDoubleSpinBox();
    // maxSpinBox->setDecimals(4);
    // maxSpinBox->setValue(0.0000);
    // maxSpinBox->setMinimum(0.0);
    // maxSpinBox->setMaximum(10000.0);
    // maxSpinBox->setEnabled(false);
    // popupLayout->addWidget(maxSpinBox);

    // // Close按钮
    // QPushButton* closeButton = new QPushButton("Close");
    // popupLayout->addWidget(closeButton);

    // // 连接信号槽
    // connect(minCheckBox, &QPushButton::clicked, [minCheckBox, minSpinBox, limitsButton, maxCheckBox]() {  // 显式捕获maxCheckBox
    //     minCheckBox->setText(minCheckBox->isChecked() ? "✓" : "");
    //     minSpinBox->setEnabled(minCheckBox->isChecked());
    //     // 设置⇔按钮的背景色
    //     if (minCheckBox->isChecked() || maxCheckBox->isChecked()) {
    //         limitsButton->setStyleSheet("background-color: #FF6666;");  // 修改为更深的红色
    //     } else {
    //         limitsButton->setStyleSheet("background-color: #333333; color: #FFFFFF;");  // 暗色背景，白色文字
    //     }
    // });

    // connect(maxCheckBox, &QPushButton::clicked, [maxCheckBox, maxSpinBox, limitsButton, minCheckBox]() {  // 显式捕获minCheckBox
    //     maxCheckBox->setText(maxCheckBox->isChecked() ? "✓" : "");
    //     maxSpinBox->setEnabled(maxCheckBox->isChecked());
    //     // 设置⇔按钮的背景色
    //     if (minCheckBox->isChecked() || maxCheckBox->isChecked()) {
    //         limitsButton->setStyleSheet("background-color: #FF6666;");  // 修改为更深的红色
    //     } else {
    //         limitsButton->setStyleSheet("background-color: #333333; color: #FFFFFF;");  // 暗色背景，白色文字
    //     }
    // });

    // connect(closeButton, &QPushButton::clicked, popupWidget, &QWidget::close);

    // // 点击⇔按钮时显示弹出框
    // connect(limitsButton, &QPushButton::clicked, [limitsButton, popupWidget]() {
    //     QPoint pos = limitsButton->mapToGlobal(QPoint(limitsButton->width(), 0));
    //     popupWidget->move(pos);
    //     popupWidget->show();
    // });

    // table->setCellWidget(row, 0, cellWidget);

    // // 创建一个容器来包裹输入框，使其能够自适应
    // QWidget* valueWidget = new QWidget();
    // QHBoxLayout* valueLayout = new QHBoxLayout(valueWidget);
    // valueLayout->setContentsMargins(0, 0, 0, 0);

    // QDoubleSpinBox* spinBox = new QDoubleSpinBox();
    // spinBox->setDecimals(4);
    // spinBox->setValue(0.0000);
    // spinBox->setMinimum(0.0);
    // spinBox->setMaximum(1000.0);
    // spinBox->setAlignment(Qt::AlignCenter); // 设置数字居中显示
    // spinBox->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed); // 设置大小策略为水平自适应

    // valueLayout->addWidget(spinBox);

    // table->setCellWidget(row, 1, valueWidget);

    // QPushButton* fixButton = new QPushButton(""); // 默认没有✓
    // fixButton->setCheckable(true); // 设置为可勾选
    // fixButton->setChecked(false); // 默认不勾选
    // fixButton->setStyleSheet("padding: 2px; margin: 2px; min-width: 18px; max-width: 18px; text-align: center; vertical-align: middle;");

    // // 连接点击事件，切换勾选状态
    // connect(fixButton, &QPushButton::clicked, [fixButton]() {
    //     fixButton->setText(fixButton->isChecked() ? "✓" : "");
    // });
    // // Create a horizontal layout for the Fix button and center it
    // QWidget* fixButtonWidget = new QWidget();
    // QHBoxLayout* fixButtonLayout = new QHBoxLayout(fixButtonWidget);
    // fixButtonLayout->addWidget(fixButton);
    // fixButtonLayout->setAlignment(Qt::AlignCenter); // Center the button
    // fixButtonLayout->setContentsMargins(0, 0, 0, 0);

    // table->setCellWidget(row, 2, fixButtonWidget);
}

void AnalysisTab::setupActionButtons(QVBoxLayout* mainLayout) {
    QGroupBox* buttonGroup = new QGroupBox();
    QHBoxLayout* buttonLayout = new QHBoxLayout(buttonGroup);

    QPushButton* clearButton = new QPushButton("Clear");
    QPushButton* fitButton = new QPushButton("Fit");
    QPushButton* saveButton = new QPushButton("Save");
    QPushButton* helpButton = new QPushButton("Help");

    // 保存按钮引用以便后续连接信号和槽
    m_fitButton = fitButton; // 直接从参数执行拟合的按钮
    m_clearFitsButton = clearButton;
    m_saveButton = saveButton;

    // 连接按钮信号
    connect(m_fitButton, &QPushButton::clicked, this, [this]() {
        // 检查当前选择的拟合模型
        QString fittingModel = m_fittingModelComboBox_1->currentText();

        // 根据拟合模型选择不同的拟合函数
        if (fittingModel == "Convolution Fitting") {
            // 检查是否有IRF数据
            if (!IRFImporter::getInstance()->hasIRFData()) {
                QMessageBox::warning(this, "警告", "请先导入仪器响应函数");
                return;
            }

            // 执行卷积拟合
            this->onConvolutionFitClicked();
        }
        else {
            // 执行尾部拟合
            this->onFitFromParametersClicked();
        }
    });

    connect(m_clearFitsButton, &QPushButton::clicked, this, &AnalysisTab::onClearFitsClicked);
    connect(m_saveButton, &QPushButton::clicked, this, &AnalysisTab::onSaveClicked);

    buttonLayout->addWidget(clearButton);
    buttonLayout->addWidget(fitButton);
    buttonLayout->addWidget(saveButton);
    buttonLayout->addWidget(helpButton);

    mainLayout->addWidget(buttonGroup);
}

// 构造函数实现
AnalysisTab::AnalysisTab(QWidget* parent)
    : BaseTab(parent, TabType::Analysis) {
    // 创建主布局，设置为左右分栏
    QHBoxLayout* mainLayout = new QHBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0); // 设置主布局的边距为0，与ProcessTab保持一致
    mainLayout->setSpacing(0); // 设置主布局的间距为0，与ProcessTab保持一致

    // 左侧布局（文件目录和功能按钮切换）
    QPushButton* singleButton = new QPushButton("衰减曲线拟合");
    QPushButton* doubleButton = new QPushButton("光谱曲线拟合");
    QVBoxLayout* leftLayout = new QVBoxLayout();
    leftLayout->setContentsMargins(0, 0, 0, 0); // 设置左侧布局的边距为0，与ProcessTab保持一致
    leftLayout->setSpacing(0); // 设置左侧布局的间距为0，与ProcessTab保持一致

    // 创建一个左侧容器，确保它不会设置最小宽度
    QWidget* leftContainer = new QWidget();
    leftContainer->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);
    leftContainer->setMinimumWidth(0); // 显式设置最小宽度为0

    singleButton->setStyleSheet("background-color: rgb(255, 0, 0);");
    // 顶部按钮区域（文件目录和 analysis 页面切换）
    QHBoxLayout* topButtonLayout = new QHBoxLayout();
    topButtonLayout->setContentsMargins(0, 0, 0, 0); // 设置顶部按钮布局的边距为0
    topButtonLayout->setSpacing(2); // 设置顶部按钮布局的间距为2像素

    QPushButton* openFileButton = new QPushButton("WorkSpace");
    QPushButton* analysisButton = new QPushButton("Analysis");

    // 设置按钮的大小策略，确保与ProcessTab一致
    openFileButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    analysisButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);

    // 不设置固定宽度，使用QSS样式控制

    // 设置按钮的样式类
    openFileButton->setProperty("class", "StandardButton");
    analysisButton->setProperty("class", "StandardButton");

    // 设置按钮为可选中状态
    openFileButton->setCheckable(true);
    analysisButton->setCheckable(true);

    topButtonLayout->addWidget(openFileButton);
    topButtonLayout->addWidget(analysisButton);

    // 不添加拉伸因子，确保按钮占满整个布局

    leftLayout->addLayout(topButtonLayout);

    // 创建文件目录和 analysis 页面切换容器
    QStackedWidget* stackedWidget = new QStackedWidget();
    stackedWidget->setContentsMargins(0, 0, 0, 0);
    stackedWidget->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding); // 设置大小策略为自适应
    stackedWidget->setMinimumWidth(0); // 显式设置最小宽度为0
    leftLayout->addWidget(stackedWidget, 1);
    leftLayout->setContentsMargins(0, 0, 0, 0);

    // 页面1：文件目录页面
    // 添加 OpenProjectWidget 到文件目录页面，传递this作为父对象，与ProcessTab保持一致
    m_pOpenProjectWidget = new OpenProjectWidget(this);
    m_pOpenProjectWidget->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding); // 设置大小策略与ProcessTab一致
    stackedWidget->addWidget(m_pOpenProjectWidget);

    // 连接 OpenProjectWidget 的 dataRowClicked 信号到 onFileSelected 槽函数
    connect(m_pOpenProjectWidget, &OpenProjectWidget::dataRowClicked, this, &AnalysisTab::onFileSelected);

    // 页面2：analysis 功能按钮页面
    QWidget* analysisPage = new QWidget();
    QVBoxLayout* analysisPageLayout = new QVBoxLayout(analysisPage);
    analysisPageLayout->setContentsMargins(0, 0, 0, 0); // 设置边距为0，与ProcessTab保持一致
    analysisPageLayout->setSpacing(0); // 设置间距为0，与ProcessTab保持一致

    // 设置大小策略，确保宽度与ProcessTab一致
    analysisPage->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);

    QHBoxLayout* chooseLayout = new QHBoxLayout();
    chooseLayout->setContentsMargins(0, 0, 0, 0);
    chooseLayout->setSpacing(2); // 设置间距为2像素，使按钮之间有小的间隔

    // 添加顶部按钮，使用更短的文本
    decayCurveButton = new QPushButton("Decay Curve");
    spectralCurveButton = new QPushButton("Spectral Curve");
    fluorescenceMapButton = new QPushButton("Fluorescence Map");

    // 设置按钮的大小策略，确保它们大小一致
    decayCurveButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    spectralCurveButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    fluorescenceMapButton->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);

    // 不设置固定宽度，使用QSS样式控制

    // 设置按钮的样式类
    decayCurveButton->setProperty("class", "SubButton");
    spectralCurveButton->setProperty("class", "SubButton");
    fluorescenceMapButton->setProperty("class", "SubButton");

    // 设置按钮为可选中状态
    decayCurveButton->setCheckable(true);
    spectralCurveButton->setCheckable(true);
    fluorescenceMapButton->setCheckable(true);

    // 默认选中第一个按钮
    decayCurveButton->setChecked(true);

    chooseLayout->addWidget(decayCurveButton);
    chooseLayout->addWidget(spectralCurveButton);
    chooseLayout->addWidget(fluorescenceMapButton);

    // 不添加拉伸因子，确保按钮占满整个布局

    analysisPageLayout->addLayout(chooseLayout);

    // 创建单波长页面和多波长页面切换容器
    QStackedWidget* stackedWidgetAnalysis = new QStackedWidget();
    stackedWidgetAnalysis->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding); // 使用Preferred而非Expanding
    stackedWidgetAnalysis->setMinimumWidth(0); // 显式设置最小宽度为0
    stackedWidgetAnalysis->setContentsMargins(0, 0, 0, 0);
    analysisPageLayout->addWidget(stackedWidgetAnalysis);
    analysisPageLayout->setContentsMargins(0, 0, 0, 0);

    QWidget* decayCurvePage = new QWidget();
    QVBoxLayout* decayCurvePageLayout = new QVBoxLayout(decayCurvePage);
    decayCurvePageLayout->setContentsMargins(0, 0, 0, 0);
    decayCurvePageLayout->setSpacing(0);

    QWidget* decayCurvePageWidget = createDecayCurvePage();
    // 设置大小策略，确保它不会过宽
    decayCurvePageWidget->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);
    decayCurvePageLayout->addWidget(decayCurvePageWidget);

    QWidget* spectralCurvePage = new QWidget();
    QVBoxLayout* spectralCurvePageLayout = new QVBoxLayout(spectralCurvePage);
    spectralCurvePageLayout->setContentsMargins(0, 0, 0, 0);
    spectralCurvePageLayout->setSpacing(0);

    QWidget* spectralCurvePageWidget = createSpectralCurvePageWidget();
    // 设置大小策略，确保它不会过宽
    spectralCurvePageWidget->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);
    spectralCurvePageLayout->addWidget(spectralCurvePageWidget);

    QWidget* fluorescenceMapPage = new QWidget();
    QVBoxLayout* fluorescenceMapPageLayout = new QVBoxLayout(fluorescenceMapPage);
    fluorescenceMapPageLayout->setContentsMargins(0, 0, 0, 0);
    fluorescenceMapPageLayout->setSpacing(0);

    QWidget* fluorescenceMapPageWidget = createFluorescenceMapPageWidget();
    // 设置大小策略，确保它不会过宽
    fluorescenceMapPageWidget->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);
    fluorescenceMapPageLayout->addWidget(fluorescenceMapPageWidget);

    stackedWidgetAnalysis->addWidget(decayCurvePage);
    stackedWidgetAnalysis->addWidget(spectralCurvePage);
    stackedWidgetAnalysis->addWidget(fluorescenceMapPage);

    stackedWidget->addWidget(analysisPage);

    // 将左侧布局添加到容器中
    leftContainer->setLayout(leftLayout);

    // 将容器添加到主布局
    mainLayout->addWidget(leftContainer, 1); // 左侧占比1

    // 右侧布局（图像显示部分）
    QGridLayout* rightLayout = new QGridLayout();
    rightLayout->setContentsMargins(0, 0, 0, 0); // 设置右侧布局的边距为0，与ProcessTab保持一致
    rightLayout->setSpacing(0); // 设置右侧布局的间距为0，与ProcessTab保持一致

    // 创建图表容器
    // 注意：我们不需要重新创建 m_pPlot1、m_pPlot2、m_pPlot3，因为它们已经在 setupPlots() 中创建了

    // 创建四个大小相同的容器，确保所有图表大小一致
    // 创建 plot1 容器，与ProcessTab保持一致
    QWidget* plot1Container = new QWidget();
    plot1Container->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding); // 设置大小策略为可扩展
    QHBoxLayout* plot1Layout = new QHBoxLayout(plot1Container);
    plot1Layout->setContentsMargins(0, 0, 0, 0);
    plot1Layout->setSpacing(0);

    // 创建 plot2 容器，与ProcessTab保持一致
    QWidget* plot2Container = new QWidget();
    plot2Container->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding); // 设置大小策略为可扩展
    QHBoxLayout* plot2Layout = new QHBoxLayout(plot2Container);
    plot2Layout->setContentsMargins(0, 0, 0, 0);
    plot2Layout->setSpacing(0);

    // 创建 plot3 容器，与ProcessTab保持一致
    QWidget* plot3Container = new QWidget();
    plot3Container->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding); // 设置大小策略为可扩展
    QHBoxLayout* plot3Layout = new QHBoxLayout(plot3Container);
    plot3Layout->setContentsMargins(0, 0, 0, 0);
    plot3Layout->setSpacing(0);
    // 创建标签页容器
    QWidget* tabContainer = new QWidget();
    tabContainer->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding); // 设置大小策略为可扩展
    // 确保与其他容器大小一致
    tabContainer->setMaximumHeight(plot1Container->maximumHeight());
    QHBoxLayout* tabLayout = new QHBoxLayout(tabContainer);
    tabLayout->setContentsMargins(0, 0, 0, 0);
    tabLayout->setSpacing(0);

    // 创建标签页中的图表
    plot4 = new CustomizePlot();
    QCPTextElement*title4;
    plot4->plotLayout()->insertRow(0);
    title4 = new QCPTextElement(plot4,"Decay Analysis");
    plot4->plotLayout()->addElement(0,0,title4);
    plot4->xAxis->setLabel("Time (ns)");
    plot4->yAxis->setLabel("Residual");
    plot4->setStyleSheet("QTabBar::close-button { image: none; width: 0; height: 0; }");
    plot4->applyCurrentThemeStyle();
    plot4->rescaleAxes();

    CustomizePlot *plot5 = new CustomizePlot();
    QCPTextElement*title5;
    plot5->plotLayout()->insertRow(0);
    title5 = new QCPTextElement(plot5,"Spectral Curves:");
    plot5->plotLayout()->addElement(0,0,title5);
    plot5->xAxis->setLabel("X Axis");
    plot5->yAxis->setLabel("Y Axis");
    plot5->setStyleSheet("QTabBar::close-button { image: none; width: 0; height: 0; }");
    plot5->applyCurrentThemeStyle();
    plot5->rescaleAxes();

    // 创建文本编辑控件用于显示拟合结果
    m_fitResultsTextEdit = new QPlainTextEdit();
    m_fitResultsTextEdit->setReadOnly(true);  // 设置为只读
    m_fitResultsTextEdit->setFont(QFont("Consolas", 9));  // 使用等宽字体
    m_fitResultsTextEdit->setPlaceholderText("拟合结果将显示在这里...");  // 设置占位文本

    // 使用已经创建的m_fitResultsTextEdit作为第二个页面的文本编辑控件
    QTabWidget*tabWidegt = new QTabWidget;
    QWidget *page1 = new QWidget;
    QWidget *page2 = new QWidget;

    QVBoxLayout*page1_layout = new QVBoxLayout(page1);
    page1_layout->addWidget(plot4,3);
    page1_layout->addWidget(m_fitResultsTextEdit,2);

    QVBoxLayout*page2_layout = new QVBoxLayout(page2);
    page2_layout->addWidget(plot5,3);

    // 创建第二个文本编辑控件用于光谱分析结果
    QPlainTextEdit* spectralResultsTextEdit = new QPlainTextEdit();
    spectralResultsTextEdit->setReadOnly(true);
    spectralResultsTextEdit->setFont(QFont("Consolas", 9));
    spectralResultsTextEdit->setPlaceholderText("光谱分析结果将显示在这里...");
    page2_layout->addWidget(spectralResultsTextEdit,2);

    tabWidegt->addTab(page1,"Decay Analysis");
    tabWidegt->addTab(page2,"Spectral Analysis");

    // 先创建图表和工具栏
    setupPlots();
    setupPlotToolbars();

    // 创建拟合曲线显示组件
    m_decayCurveFitDisplay = new FitCurveDisplay(m_pPlot2, PlotDataType::DecayCurve, TabType::Analysis, this);
    m_spectralCurveFitDisplay = new FitCurveDisplay(m_pPlot3, PlotDataType::SpectralCurve, TabType::Analysis, this);

    // 连接数据同步完成信号
    connect(SharedDataManager::getInstance(), &SharedDataManager::dataSynced,
            this, &AnalysisTab::onDataSynced);

    // 添加图表到容器中
    plot1Layout->addWidget(m_pPlot1, 1); // 1 是伸缩因子
    plot2Layout->addWidget(m_pPlot2, 1);
    plot3Layout->addWidget(m_pPlot3, 1);

    // 将工具栏添加到布局中，使其与 plot 左右分开显示
    plot1Layout->addWidget(m_pPlot1Toolbar, 0); // 0 是伸缩因子，使工具栏不会被拉伸
    plot2Layout->addWidget(m_pPlot2Toolbar, 0);
    plot3Layout->addWidget(m_pPlot3Toolbar, 0);

    // 应用当前主题样式
    m_pPlot1->applyCurrentThemeStyle();
    m_pPlot2->applyCurrentThemeStyle();
    m_pPlot3->applyCurrentThemeStyle();

    // 应用当前主题到标签页中的图表
    CustomizePlot* tabPlot1 = qobject_cast<CustomizePlot*>(page1_layout->itemAt(0)->widget());
    CustomizePlot* tabPlot2 = qobject_cast<CustomizePlot*>(page2_layout->itemAt(0)->widget());
    if (tabPlot1) tabPlot1->applyCurrentThemeStyle();
    if (tabPlot2) tabPlot2->applyCurrentThemeStyle();

    // 设置网格布局的属性，与ProcessTab完全一致
    // 设置每列和每行的伸缩因子为1，以确保它们等分
    for (int i = 0; i < 2; ++i) {
        rightLayout->setColumnStretch(i, 1);
        rightLayout->setRowStretch(i, 1);
    }

    // 添加容器到网格布局
    rightLayout->addWidget(plot1Container, 0, 0, 1, 1); // plot1在左上角（Fluorescence Map）
    rightLayout->addWidget(plot2Container, 0, 1, 1, 1); // plot2在右上角（Decay Curve）
    rightLayout->addWidget(plot3Container, 1, 0, 1, 1); // plot3在左下角（Spectral Curve）
    rightLayout->addWidget(tabWidegt, 1, 1, 1, 1); // 标签页在右下角（Decay Analysis/Spectral Analysis）

    // 确保所有图表容器具有相同的大小策略
    QSizePolicy sizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    plot1Container->setSizePolicy(sizePolicy);
    plot2Container->setSizePolicy(sizePolicy);
    plot3Container->setSizePolicy(sizePolicy);
    tabWidegt->setSizePolicy(sizePolicy);

    // 将右侧布局添加到主布局
    mainLayout->addLayout(rightLayout, 3); // 右侧占比3，与 ProcessTab 画面保持一致

    // 连接按钮和页面切换
    connect(openFileButton, &QPushButton::clicked, this, [openFileButton, analysisButton, stackedWidget]() {
        stackedWidget->setCurrentIndex(0); // 切换到文件目录页面
        openFileButton->setChecked(true);
        analysisButton->setChecked(false);
    });
    connect(analysisButton, &QPushButton::clicked, this, [openFileButton, analysisButton, stackedWidget]() {
        stackedWidget->setCurrentIndex(1); // 切换到 analysis 功能按钮页面
        openFileButton->setChecked(false);
        analysisButton->setChecked(true);
    });

    // 连接按钮信号
    connect(decayCurveButton, &QPushButton::clicked, this, [this, stackedWidgetAnalysis]() {
        stackedWidgetAnalysis->setCurrentIndex(0);
        decayCurveButton->setChecked(true);
        spectralCurveButton->setChecked(false);
        fluorescenceMapButton->setChecked(false);
    });
    connect(spectralCurveButton, &QPushButton::clicked, this, [this, stackedWidgetAnalysis]() {
        stackedWidgetAnalysis->setCurrentIndex(1);
        decayCurveButton->setChecked(false);
        spectralCurveButton->setChecked(true);
        fluorescenceMapButton->setChecked(false);
    });
    connect(fluorescenceMapButton, &QPushButton::clicked, this, [this, stackedWidgetAnalysis]() {
        stackedWidgetAnalysis->setCurrentIndex(2);
        decayCurveButton->setChecked(false);
        spectralCurveButton->setChecked(false);
        fluorescenceMapButton->setChecked(true);
    });


    // 默认显示文件目录页面
    stackedWidget->setCurrentIndex(1);
    stackedWidgetAnalysis->setCurrentIndex(0);

    // 设置默认选中状态
    openFileButton->setChecked(false);
    analysisButton->setChecked(true);
    decayCurveButton->setChecked(true);
    spectralCurveButton->setChecked(false);
    fluorescenceMapButton->setChecked(false);
}

// 添加析构函数实现
AnalysisTab::~AnalysisTab()
{
    // 清理垂直线对象
    if (m_pPlot2) {
        if (m_rangeFromLine) {
            m_pPlot2->removeItem(m_rangeFromLine);
            m_rangeFromLine = nullptr;
        }

        if (m_rangeToLine) {
            m_pPlot2->removeItem(m_rangeToLine);
            m_rangeToLine = nullptr;
        }
    }

    // 基类的析构函数会处理共享资源的清理
}

void AnalysisTab::setupPlots()
{
    // 直接调用基类的 setupPlots 方法初始化 m_pPlot1, m_pPlot2, m_pPlot3
    // 使用基类中设置的标题和坐标轴标签
    BaseTab::setupPlots();

    // 确保plot2和plot3的y轴标签与Process画面保持一致
    if (m_pPlot2) {
        m_pPlot2->yAxis->setLabel("Intensity");
    }
    if (m_pPlot3) {
        m_pPlot3->yAxis->setLabel("Intensity");
    }
}

// 实现文件选择相关的槽函数
void AnalysisTab::onFileSelected(int row, const QString &filePath)
{
    // 添加调试信息
    qDebug() << "Analysis::onFileSelected called with row:" << row << "filePath:" << filePath;

    // 清除 SharedDataManager 中的数据
    SharedDataManager::getInstance()->clearTabData(TabType::Analysis);

    // 清除之前的拟合曲线
    if (m_decayCurveFitDisplay) {
        m_decayCurveFitDisplay->clearFitCurves();
    }
    if (m_spectralCurveFitDisplay) {
        m_spectralCurveFitDisplay->clearFitCurves();
    }

    // 清除拟合结果显示
    if (m_fitResultsTextEdit) {
        m_fitResultsTextEdit->clear();
        m_fitResultsTextEdit->setPlaceholderText("拟合结果将显示在这里...");
    }

    // 重置FitParameterModel中的参数值为默认值
    FitParameterModel* paramModel = FitParameterModel::getInstance();
    QVector<FitParameterModel::ParameterInfo> params = paramModel->getParameters();
    for (const auto& param : params) {
        // 对于A参数设置为100，其他参数设置为0
        if (param.name.startsWith("A")) {
            paramModel->setParameterValue(param.name, 100.0);
        } else {
            paramModel->setParameterValue(param.name, 0.0);
        }
    }

    // 调用基类的 onFileSelected 方法处理所有基本功能
    // 包括文件加载、数据处理和图表填充
    BaseTab::onFileSelected(row, filePath);

    // 强制重绘所有图表，确保数据显示正确
    qDebug() << "Analysis::onFileSelected - Forcing replot of all plots";
    if (m_pPlot1) {
        qDebug() << "Analysis::onFileSelected - Replotting Plot1";
        m_pPlot1->replot(QCustomPlot::rpQueuedReplot);
    }
    if (m_pPlot2) {
        qDebug() << "Analysis::onFileSelected - Replotting Plot2";
        m_pPlot2->replot(QCustomPlot::rpQueuedReplot);
    }
    if (m_pPlot3) {
        qDebug() << "Analysis::onFileSelected - Replotting Plot3";
        m_pPlot3->replot(QCustomPlot::rpQueuedReplot);
    }

    // 清除残差图
    if (plot4) {
        plot4->clearGraphs();
        plot4->replot();
    }

    qDebug() << "Analysis::onFileSelected completed";
}





// 清除所有拟合曲线
void AnalysisTab::onClearFitsClicked() {
    // 清除共享数据管理器中的所有拟合曲线
    SharedDataManager::getInstance()->clearAllFits(TabType::Analysis);

    // 清除拟合结果显示
    if (m_fitResultsTextEdit) {
        m_fitResultsTextEdit->clear();
        m_fitResultsTextEdit->setPlaceholderText("拟合结果将显示在这里...");
    }

    // 重置FitParameterModel中的参数值为默认值
    FitParameterModel* paramModel = FitParameterModel::getInstance();
    QVector<FitParameterModel::ParameterInfo> params = paramModel->getParameters();
    for (const auto& param : params) {
        // 对于A参数设置为100，其他参数设置为0
        if (param.name.startsWith("A")) {
            paramModel->setParameterValue(param.name, 100.0);
        } else {
            paramModel->setParameterValue(param.name, 0.0);
        }
    }

    // 确保重绘图表
    if (m_pPlot2) {
        m_pPlot2->replot();
    }
    if (m_pPlot3) {
        m_pPlot3->replot();
    }

    // 清除残差图
    if (plot4) {
        plot4->clearGraphs();
        plot4->replot();
    }
}

// 直接从参数配置执行尾部拟合
void AnalysisTab::onFitFromParametersClicked() {
    // 确定当前拟合类型
    PlotDataType plotType;

    // 判断当前分析的是衰减曲线还是光谱曲线
    // 根据按钮的选中状态来决定
    if (spectralCurveButton && spectralCurveButton->isChecked()) {
        plotType = PlotDataType::SpectralCurve;
        qDebug() << "AnalysisTab::onFitFromParametersClicked - Using Spectral Curve fitting";
    } else {
        // 默认使用衰减曲线拟合
        plotType = PlotDataType::DecayCurve;
        qDebug() << "AnalysisTab::onFitFromParametersClicked - Using Decay Curve fitting";
    }

    // 记录日志
    qDebug() << "AnalysisTab::onFitFromParametersClicked - Using Tail Fitting";

    // 从SharedDataManager获取原始数据
    QVector<GraphData> originalData = SharedDataManager::getInstance()->getData(
        TabType::Process, plotType, DataOperationType::Original);

    // 检查是否有数据
    if (originalData.isEmpty()) {
        QMessageBox::warning(this, "错误", "没有可用的数据进行拟合");
        return;
    }

    // 创建一个向量来存储所有有效的曲线数据
    QVector<QPair<QVector<double>, QVector<double>>> validCurves;
    QVector<QString> curveNames;

    // 从原始数据中提取所有有效曲线
    for (const GraphData& data : originalData) {
        if (data.isValid() && !data.xData.isEmpty() && !data.yData.isEmpty()) {
            // 确保数据按X轴排序
            QVector<QPair<double, double>> pairs;
            for (int i = 0; i < data.xData.size(); ++i) {
                pairs.append(qMakePair(data.xData[i], data.yData[i]));
            }

            std::sort(pairs.begin(), pairs.end(),
                      [](const QPair<double, double>& a, const QPair<double, double>& b) {
                          return a.first < b.first;
                      });

            QVector<double> sortedX, sortedY;
            for (const auto& pair : pairs) {
                sortedX.append(pair.first);
                sortedY.append(pair.second);
            }

            validCurves.append(qMakePair(sortedX, sortedY));
            curveNames.append(data.name.isEmpty() ? "未命名曲线" : data.name);
        }
    }

    // 如果没有找到有效数据，尝试从添加的曲线中获取
    if (validCurves.isEmpty()) {
        QVector<GraphData> addedData = SharedDataManager::getInstance()->getData(
            TabType::Process, plotType, DataOperationType::AddedCurve);

        for (const GraphData& data : addedData) {
            if (data.isValid() && !data.xData.isEmpty() && !data.yData.isEmpty()) {
                // 确保数据按X轴排序
                QVector<QPair<double, double>> pairs;
                for (int i = 0; i < data.xData.size(); ++i) {
                    pairs.append(qMakePair(data.xData[i], data.yData[i]));
                }

                std::sort(pairs.begin(), pairs.end(),
                          [](const QPair<double, double>& a, const QPair<double, double>& b) {
                              return a.first < b.first;
                          });

                QVector<double> sortedX, sortedY;
                for (const auto& pair : pairs) {
                    sortedX.append(pair.first);
                    sortedY.append(pair.second);
                }

                validCurves.append(qMakePair(sortedX, sortedY));
                curveNames.append(data.name.isEmpty() ? "未命名曲线" : data.name);
            }
        }
    }

    // 如果仍然没有找到有效数据，显示错误消息并返回
    if (validCurves.isEmpty()) {
        QMessageBox::warning(this, "错误", "无法获取有效的数据进行拟合");
        return;
    }

    qDebug() << "找到" << validCurves.size() << "条有效曲线进行拟合";

    // 获取参数模型
    FitParameterModel* model = FitParameterModel::getInstance();

    // 获取拟合参数
    QString modelName = model->exponentialModel();
    double rangeFrom = model->rangeFrom();
    double rangeTo = model->rangeTo();

    // 清除之前的所有拟合曲线
    SharedDataManager::getInstance()->clearFitCurves(TabType::Analysis, plotType);

    // 存储所有曲线的拟合结果
    QVector<FitResult> allResults;
    QVector<QColor> curveColors = {
        QColor(255, 0, 0),    // 红色
        QColor(0, 0, 255),    // 蓝色
        QColor(0, 128, 0),    // 绿色
        QColor(128, 0, 128),  // 紫色
        QColor(255, 165, 0),  // 橙色
        QColor(0, 128, 128),  // 青色
        QColor(128, 128, 0),  // 橄榄色
        QColor(128, 0, 0)     // 栗色
    };

    // 为每条曲线执行拟合
    for (int curveIndex = 0; curveIndex < validCurves.size(); curveIndex++) {
        QVector<double> xData = validCurves[curveIndex].first;
        QVector<double> yData = validCurves[curveIndex].second;
        QString curveName = curveNames[curveIndex];

        qDebug() << "正在拟合曲线:" << curveName << "(" << curveIndex + 1 << "/" << validCurves.size() << ")";

        // 计算当前曲线的拟合范围
        double currentRangeFrom = rangeFrom;
        double currentRangeTo = rangeTo;

        // 如果checkBoxrange被勾选，则使用红线位置对应的时间值
        if (checkrange && checkrange->isChecked()) {
            // 获取x轴的范围
            QCPRange xRange = m_pPlot2->xAxis->range();
            // 获取最大值
            double xMax = xRange.upper;
            // 使用红线位置对应的时间值
            currentRangeFrom = m_rangeFromSpinBox->value() / xMax * xData.size();
            currentRangeTo = m_rangeToSpinBox->value() / xMax * xData.size();

            // 记录日志
            qDebug() << "曲线" << curveIndex + 1 << "使用红线范围: " << currentRangeFrom << " 到 " << currentRangeTo;
        }

        // 执行拟合但不直接存储结果
        FitResult result = FitManager::getInstance()->performFit(
            modelName, xData, yData, currentRangeFrom, currentRangeTo);

        // 如果拟合成功，保存结果
        if (result.success) {
            allResults.append(result);

            // 如果启用了红线范围，则只显示范围内的拟合曲线
            if (checkrange && checkrange->isChecked()) {
                // 获取x轴的范围
                QCPRange xRange = m_pPlot2->xAxis->range();
                // 获取最大值
                double xMax = xRange.upper;

                // 获取红线位置对应的数据索引
                double fromIndex = m_rangeFromSpinBox->value() / xMax * result.xData.size();
                double toIndex = m_rangeToSpinBox->value() / xMax * result.xData.size();

                // 转换为整数索引
                int fromIdxInt = static_cast<int>(fromIndex);
                int toIdxInt = static_cast<int>(toIndex);

                // 确保索引在有效范围内
                fromIdxInt = qBound(0, fromIdxInt, result.xData.size() - 1);
                toIdxInt = qBound(0, toIdxInt, result.xData.size() - 1);

                // // 创建仅包含范围内数据的拟合结果
                // FitResult rangeResult = result;
                // QVector<double> rangeXData, rangeYData;
                // for (int i = fromIdxInt; i <= toIdxInt; ++i) {
                //     rangeXData.append(result.xData[i]);
                //     rangeYData.append(result.yData[i]);
                // }
                // rangeResult.xData = rangeXData;
                // rangeResult.yData = rangeYData;

                // 使用范围内的结果添加拟合曲线
                QColor curveColor = curveColors[curveIndex % curveColors.size()];
                SharedDataManager::getInstance()->addFitCurveFromResult(
                    TabType::Analysis, plotType, result, modelName, curveColor);
            } else {
                // 使用完整范围的结果添加拟合曲线
                QColor curveColor = curveColors[curveIndex % curveColors.size()];
                SharedDataManager::getInstance()->addFitCurveFromResult(
                    TabType::Analysis, plotType, result, modelName, curveColor);
            }
        } else {
            qDebug() << "曲线" << curveIndex + 1 << "拟合失败: " << result.errorMessage;
        }
    }

    // 如果没有成功拟合任何曲线，显示错误消息并返回
    if (allResults.isEmpty()) {
        QMessageBox::warning(this, "拟合失败", "所有曲线拟合均失败");
        return;
    }

    // 使用最后一条成功拟合的曲线的结果更新UI
    FitResult lastResult = allResults.last();

    // 处理最后一条曲线的拟合结果
    // 构建结果消息
    QString resultMessage = QString("拟合成功\n");
    resultMessage += QString("模型: %1\n").arg(modelName);
    resultMessage += QString("共拟合 %1 条曲线，以下为最后一条曲线的参数\n").arg(allResults.size());
    resultMessage += QString("卡方值 (χ²): %1\n").arg(lastResult.chiSquare, 0, 'g', 6);
    resultMessage += QString("决定系数 (R²): %1\n").arg(lastResult.rSquare, 0, 'g', 6);

    // 添加参数信息
    resultMessage += "\n参数:\n";
    for (auto it = lastResult.parameters.begin(); it != lastResult.parameters.end(); ++it) {
        resultMessage += QString("%1 = %2\n").arg(it.key()).arg(it.value(), 0, 'g', 6);
    }

    // 更新文本编辑控件显示拟合结果
    if (m_fitResultsTextEdit) {
        m_fitResultsTextEdit->setPlainText(resultMessage);
    }

    // 更新FitParameterModel中的参数值，这将自动更新左侧表格
    // 只使用最后一条曲线的参数
    FitParameterModel* paramModel = FitParameterModel::getInstance();
    for (auto it = lastResult.parameters.begin(); it != lastResult.parameters.end(); ++it) {
        // 只更新模型中已存在的参数
        if (paramModel->getParameterNames().contains(it.key())) {
            paramModel->setParameterValue(it.key(), it.value());
        }
    }

    // 更新图表显示
    if (plotType == PlotDataType::DecayCurve && m_pPlot2) {
        m_pPlot2->replot();

        // 只有在拟合曲线超出当前视图范围时才调整坐标轴
        QCPRange currentXRange = m_pPlot2->xAxis->range();
        QCPRange currentYRange = m_pPlot2->yAxis->range();

        bool needRescale = false;
        // 检查所有拟合曲线是否超出当前视图范围
        QVector<GraphData> fitCurves = SharedDataManager::getInstance()->getFitCurves(TabType::Analysis, plotType);
        for (const GraphData& curve : fitCurves) {
            for (double x : curve.xData) {
                if (x < currentXRange.lower || x > currentXRange.upper) {
                    needRescale = true;
                    break;
                }
            }
            if (needRescale) break;

            for (double y : curve.yData) {
                if (y < currentYRange.lower || y > currentYRange.upper) {
                    needRescale = true;
                    break;
                }
            }
            if (needRescale) break;
        }

        if (needRescale) {
            m_pPlot2->rescaleAxes();
            m_pPlot2->replot();
        }

        // 获取最后一条曲线的数据用于计算残差图
        QVector<double> lastXData = validCurves.last().first;
        QVector<double> lastYData = validCurves.last().second;

        // 计算并显示残差图 - 只显示最后一条曲线的残差
        calculateAndPlotResiduals(lastXData, lastResult.originalY, lastResult.yData, true);

    } else if (plotType == PlotDataType::SpectralCurve && m_pPlot3) {
        m_pPlot3->replot();

        // 只有在拟合曲线超出当前视图范围时才调整坐标轴
        QCPRange currentXRange = m_pPlot3->xAxis->range();
        QCPRange currentYRange = m_pPlot3->yAxis->range();

        bool needRescale = false;
        // 检查所有拟合曲线是否超出当前视图范围
        QVector<GraphData> fitCurves = SharedDataManager::getInstance()->getFitCurves(TabType::Analysis, plotType);
        for (const GraphData& curve : fitCurves) {
            for (double x : curve.xData) {
                if (x < currentXRange.lower || x > currentXRange.upper) {
                    needRescale = true;
                    break;
                }
            }
            if (needRescale) break;

            for (double y : curve.yData) {
                if (y < currentYRange.lower || y > currentYRange.upper) {
                    needRescale = true;
                    break;
                }
            }
            if (needRescale) break;
        }

        if (needRescale) {
            m_pPlot3->rescaleAxes();
            m_pPlot3->replot();
        }
    }
}

// 处理数据同步完成信号
void AnalysisTab::onDataSynced(TabType sourceTab, TabType targetTab, PlotDataType plotType) {
    // 只处理目标是当前标签页的同步
    if (targetTab != TabType::Analysis) {
        return;
    }

    qDebug() << "AnalysisTab::onDataSynced - Data synced from" << static_cast<int>(sourceTab)
             << "to" << static_cast<int>(targetTab) << "for plot type" << static_cast<int>(plotType);

    // 根据同步的数据类型更新相应的图表
    switch (plotType) {
    case PlotDataType::DecayCurve:
        if (m_pPlot2) {
            // 清除所有图形
            m_pPlot2->clearGraphs();

            // 获取同步的数据
            QVector<GraphData> data = SharedDataManager::getInstance()->getDecayCurveData(TabType::Analysis);

            // 添加同步的数据到图表（排除拟合曲线，它们将由FitCurveDisplay处理）
            for (const GraphData& graph : data) {
                if (graph.operationType != DataOperationType::FittedCurve) {
                    QCPGraph* newGraph = m_pPlot2->addGraph();
                    graph.applyTo(newGraph);
                }
            }

            // 应用设置，但保留坐标轴类型
            PlotSettings settings = SharedDataManager::getInstance()->getDecayCurveSettings();
            settings.applyToPreserveAxisSettings(m_pPlot2);

            // 重绘图表
            m_pPlot2->replot();

            // 更新拟合曲线（FitCurveDisplay会处理拟合曲线的添加）
            if (m_decayCurveFitDisplay) {
                m_decayCurveFitDisplay->updateFitCurves();
            }
        }
        break;

    case PlotDataType::SpectralCurve:
        if (m_pPlot3) {
            // 清除所有图形
            m_pPlot3->clearGraphs();

            // 获取同步的数据
            QVector<GraphData> data = SharedDataManager::getInstance()->getSpectralCurveData(TabType::Analysis);

            // 添加同步的数据到图表（排除拟合曲线，它们将由FitCurveDisplay处理）
            for (const GraphData& graph : data) {
                if (graph.operationType != DataOperationType::FittedCurve) {
                    QCPGraph* newGraph = m_pPlot3->addGraph();
                    graph.applyTo(newGraph);
                }
            }

            // 应用设置，但保留坐标轴类型
            PlotSettings settings = SharedDataManager::getInstance()->getSpectralCurveSettings();
            settings.applyToPreserveAxisSettings(m_pPlot3);

            // 重绘图表
            m_pPlot3->replot();

            // 更新拟合曲线（FitCurveDisplay会处理拟合曲线的添加）
            if (m_spectralCurveFitDisplay) {
                m_spectralCurveFitDisplay->updateFitCurves();
            }
        }
        break;

    case PlotDataType::FluorescenceMap:
        // 荧光图同步逻辑（如果需要）
        if (m_pPlot1) {
            // 获取同步的数据
            ColorMapData mapData = SharedDataManager::getInstance()->getFluorescenceMapData(TabType::Analysis);

            // 检查是否有有效的颜色图
            QCPColorMap* colorMap = qobject_cast<QCPColorMap*>(m_pPlot1->plottable(0));
            if (colorMap) {
                // 应用数据
                mapData.applyTo(colorMap);

                // 重绘图表
                m_pPlot1->replot();
            }
        }
        break;

    default:
        break;
    }
}

// 保存按钮点击处理函数
void AnalysisTab::onSaveClicked() {
    // 空函数
}

// 卷积拟合函数
void AnalysisTab::onConvolutionFitClicked() {
    // 确定当前拟合类型
    PlotDataType plotType;

    // 判断当前分析的是衰减曲线还是光谱曲线
    // 根据按钮的选中状态来决定
    if (spectralCurveButton && spectralCurveButton->isChecked()) {
        plotType = PlotDataType::SpectralCurve;
        qDebug() << "AnalysisTab::onConvolutionFitClicked - Using Spectral Curve fitting";
    } else {
        // 默认使用衰减曲线拟合
        plotType = PlotDataType::DecayCurve;
        qDebug() << "AnalysisTab::onConvolutionFitClicked - Using Decay Curve fitting";
    }

    // 从SharedDataManager获取原始数据
    QVector<GraphData> originalData = SharedDataManager::getInstance()->getData(
        TabType::Process, plotType, DataOperationType::Original);

    // 检查是否有数据
    if (originalData.isEmpty()) {
        QMessageBox::warning(this, "错误", "没有可用的数据进行拟合");
        return;
    }

    // 提取数据点
    QVector<double> xData, yData;

    // 找到第一个有效的数据源
    for (const GraphData& data : originalData) {
        if (data.isValid()) {
            xData = data.xData;
            yData = data.yData;
            break;
        }
    }

    // 如果没有找到有效数据，尝试从添加的曲线中获取
    if (xData.isEmpty() || yData.isEmpty()) {
        QVector<GraphData> addedData = SharedDataManager::getInstance()->getData(
            TabType::Process, plotType, DataOperationType::AddedCurve);

        for (const GraphData& data : addedData) {
            if (data.isValid()) {
                xData = data.xData;
                yData = data.yData;
                break;
            }
        }
    }

    // 如果仍然没有找到有效数据，显示错误消息并返回
    if (xData.isEmpty() || yData.isEmpty()) {
        QMessageBox::warning(this, "错误", "无法获取有效的数据进行拟合");
        return;
    }

    // 确保数据按X轴排序
    QVector<QPair<double, double>> pairs;
    for (int i = 0; i < xData.size(); ++i) {
        pairs.append(qMakePair(xData[i], yData[i]));
    }

    std::sort(pairs.begin(), pairs.end(),
              [](const QPair<double, double>& a, const QPair<double, double>& b) {
                  return a.first < b.first;
              });

    xData.clear();
    yData.clear();
    for (const auto& pair : pairs) {
        xData.append(pair.first);
        yData.append(pair.second);
    }

    // 获取参数模型
    FitParameterModel* model = FitParameterModel::getInstance();

    // 获取拟合参数
    QString modelName = model->exponentialModel();
    double rangeFrom = model->rangeFrom();
    double rangeTo = model->rangeTo();

    // 获取IRF数据
    QVector<double> irfData = IRFImporter::getInstance()->getIRFData();
    if (irfData.isEmpty()) {
        QMessageBox::warning(this, "错误", "没有可用的仪器响应函数数据");
        return;
    }

    // 获取指定的拟合模型
    QSharedPointer<FitModelInterface> fitModel = FitManager::getInstance()->getModel(modelName);
    if (!fitModel) {
        QMessageBox::warning(this, "错误", QString("找不到拟合模型: %1").arg(modelName));
        return;
    }

    // 检查是否为NExponentialDecayModel类型
    NExponentialDecayModel* nExpModel = dynamic_cast<NExponentialDecayModel*>(fitModel.data());
    if (!nExpModel) {
        QMessageBox::warning(this, "错误", "卷积拟合仅支持N指数衰减模型");
        return;
    }

    // 设置IRF数据并启用卷积
    nExpModel->setIRFData(irfData);

    // 获取当前选择的拟合算法
    QString algorithmName = model->modelAlgorithm();
    if (algorithmName == "Least Square Method") {
        nExpModel->setFitAlgorithm(NExponentialDecayModel::LeastSquares);
        qDebug() << "Setting algorithm to Least Square Method for convolution fitting";
    } else if (algorithmName == "Maximum Likelihood Estimation") {
        nExpModel->setFitAlgorithm(NExponentialDecayModel::MaximumLikelihood);
        qDebug() << "Setting algorithm to Maximum Likelihood Estimation for convolution fitting";
    } else if (algorithmName == "Bayesian Analysis") {
        nExpModel->setFitAlgorithm(NExponentialDecayModel::BayesianAnalysis);
        qDebug() << "Setting algorithm to Bayesian Analysis for convolution fitting";
    } else {
        // 默认使用最小二乘法
        nExpModel->setFitAlgorithm(NExponentialDecayModel::LeastSquares);
        qDebug() << "Unknown algorithm:" << algorithmName << ", using default Least Square Method for convolution fitting";
    }

    // 新的卷积拟合逻辑：
    // 直接进行卷积拟合，不通过尾部拟合获取初始参数

    // 如果checkBoxrange被勾选，则使用红线位置对应的时间值
    if (checkrange && checkrange->isChecked())
    {
        // 获取x轴的范围
        QCPRange xRange = m_pPlot2->xAxis->range();
        // 获取最小值和最大值
        double xMax = xRange.upper;
        // 使用红线位置对应的时间值
        rangeFrom = m_rangeFromSpinBox->value()/xMax *xData.size();
        rangeTo = m_rangeToSpinBox->value()/xMax *xData.size();

        // 验证选择的范围是否合适
        // 1. 找到曲线的最大值点
        int maxValueIndex = 0;
        double maxValue = yData[0];
        for (int i = 1; i < yData.size(); ++i) {
            if (yData[i] > maxValue) {
                maxValue = yData[i];
                maxValueIndex = i;
            }
        }

        // 2. 检查范围是否包含最大值点
        int rangeFromInt = static_cast<int>(rangeFrom);
        int rangeToInt = static_cast<int>(rangeTo);

        // 确保索引在有效范围内
        rangeFromInt = qBound(0, rangeFromInt, xData.size() - 1);
        rangeToInt = qBound(0, rangeToInt, xData.size() - 1);

        if (maxValueIndex < rangeFromInt || maxValueIndex > rangeToInt) {
            QMessageBox::warning(this, "范围不合适", "所选范围不包含曲线的最大值点，请调整范围。");
            return;
        }

    }

    // 清除之前的所有拟合曲线
    SharedDataManager::getInstance()->clearFitCurves(TabType::Analysis, plotType);

    // 设置IRF数据并启用卷积
    nExpModel->setIRFData(irfData);

    // 自动估计IRF时间偏移
    double timeOffset = nExpModel->estimateTimeOffset(xData);
    qDebug() << "启用卷积，直接进行卷积拟合，时间偏移:" << timeOffset;

    // 执行直接卷积拟合
    FitResult result = FitManager::getInstance()->performConvolutionFit(
        modelName, xData, yData, rangeFrom, rangeTo);

    // 如果拟合成功，使用完整的数据范围生成拟合曲线
    if (result.success) {
        // 保存拟合参数
        QMap<QString, double> fitParams = result.parameters;

        // 创建完整范围的拟合结果
        FitResult fullRangeResult = result;

        // 使用原始数据的X范围
        QVector<double> fullXData = xData;

        // 使用拟合模型计算完整范围的Y值
        QSharedPointer<FitModelInterface> model = FitManager::getInstance()->getModel(modelName);
        NExponentialDecayModel* nExpModel = dynamic_cast<NExponentialDecayModel*>(model.data());

        if (nExpModel) {
            // 设置拟合参数
            for (auto it = fitParams.begin(); it != fitParams.end(); ++it) {
                nExpModel->setParameter(it.key(), it.value());
            }

            // 确保IRF数据已设置并启用卷积
            nExpModel->setIRFData(irfData);

            // 使用模型的calculate方法计算完整的卷积拟合曲线
            QVector<double> fullYData = nExpModel->calculate(fullXData);

            // 如果启用了红线范围，则只显示范围内的拟合曲线
            if (checkrange && checkrange->isChecked()) {
                // 获取x轴的范围
                QCPRange xRange = m_pPlot2->xAxis->range();
                // 获取最大值
                double xMax = xRange.upper;

                // 获取红线位置对应的数据索引
                double fromIndex = m_rangeFromSpinBox->value() / xMax * fullXData.size();
                double toIndex = m_rangeToSpinBox->value() / xMax * fullXData.size();

                // 转换为整数索引
                int fromIdxInt = static_cast<int>(fromIndex);
                int toIdxInt = static_cast<int>(toIndex);

                // 确保索引在有效范围内
                fromIdxInt = qBound(0, fromIdxInt, fullXData.size() - 1);
                toIdxInt = qBound(0, toIdxInt, fullXData.size() - 1);

                qDebug() << "Limiting fit curve display to range indices:" << fromIdxInt << "to" << toIdxInt;

                // 创建仅包含范围内数据的向量
                QVector<double> rangeXData, rangeYData;
                for (int i = fromIdxInt; i <= toIdxInt; ++i) {
                    rangeXData.append(fullXData[i]);
                    rangeYData.append(fullYData[i]);
                }

                // 更新结果为仅范围内的数据
                fullRangeResult.xData = rangeXData;
                fullRangeResult.yData = rangeYData;
            } else {
                // 使用完整范围
                fullRangeResult.xData = fullXData;
                fullRangeResult.yData = fullYData;
            }

            // 将拟合结果存储到SharedDataManager
            SharedDataManager::getInstance()->addFitCurveFromResult(
                TabType::Analysis, plotType, fullRangeResult, modelName);

            // 计算并显示残差图 - 对于卷积拟合，也使用与尾部拟合相同的处理方式
            calculateAndPlotResiduals(fullXData, yData, fullYData, false);
        } else {
            // 如果无法转换为NExponentialDecayModel，使用原始拟合结果
            // 如果启用了红线范围，则只显示范围内的拟合曲线
            if (checkrange && checkrange->isChecked()) {
                // 获取x轴的范围
                QCPRange xRange = m_pPlot2->xAxis->range();
                // 获取最大值
                double xMax = xRange.upper;

                // 获取红线位置对应的数据索引
                double fromIndex = m_rangeFromSpinBox->value() / xMax * result.xData.size();
                double toIndex = m_rangeToSpinBox->value() / xMax * result.xData.size();

                // 转换为整数索引
                int fromIdxInt = static_cast<int>(fromIndex);
                int toIdxInt = static_cast<int>(toIndex);

                // 确保索引在有效范围内
                fromIdxInt = qBound(0, fromIdxInt, result.xData.size() - 1);
                toIdxInt = qBound(0, toIdxInt, result.xData.size() - 1);

                qDebug() << "Limiting fit curve display to range indices:" << fromIdxInt << "to" << toIdxInt;

                // 创建仅包含范围内数据的拟合结果
                FitResult rangeResult = result;
                QVector<double> rangeXData, rangeYData;
                for (int i = fromIdxInt; i <= toIdxInt; ++i) {
                    rangeXData.append(result.xData[i]);
                    rangeYData.append(result.yData[i]);
                }
                rangeResult.xData = rangeXData;
                rangeResult.yData = rangeYData;

                // 使用范围内的结果
                SharedDataManager::getInstance()->addFitCurveFromResult(
                    TabType::Analysis, plotType, rangeResult, modelName);
            } else {
                // 使用完整范围的结果
                SharedDataManager::getInstance()->addFitCurveFromResult(
                    TabType::Analysis, plotType, result, modelName);
            }

            // 计算并显示残差图 - 对于卷积拟合，也使用与尾部拟合相同的处理方式
            calculateAndPlotResiduals(result.xData, result.originalY, result.yData, true);
        }
    } else {
        // 如果拟合失败，显示错误消息
        QMessageBox::warning(this, "卷积拟合失败", result.errorMessage);
        return;
    }

    // 处理拟合结果
    // 构建结果消息
    QString resultMessage = QString("卷积拟合成功\n");
    resultMessage += QString("模型: %1 (使用卷积)\n").arg(modelName);
    resultMessage += QString("卡方值 (χ²): %1\n").arg(result.chiSquare, 0, 'g', 6);
    resultMessage += QString("决定系数 (R²): %1\n").arg(result.rSquare, 0, 'g', 6);

    // 添加卷积拟合参数信息
    resultMessage += "\n卷积拟合参数:\n";
    for (auto it = result.parameters.begin(); it != result.parameters.end(); ++it) {
        resultMessage += QString("%1 = %2\n").arg(it.key()).arg(it.value(), 0, 'g', 6);
    }

    // 更新文本编辑控件显示拟合结果
    if (m_fitResultsTextEdit) {
        m_fitResultsTextEdit->setPlainText(resultMessage);
    }

    // 更新FitParameterModel中的参数值，这将自动更新左侧表格
    FitParameterModel* paramModel = FitParameterModel::getInstance();
    for (auto it = result.parameters.begin(); it != result.parameters.end(); ++it) {
        // 只更新模型中已存在的参数
        if (paramModel->getParameterNames().contains(it.key())) {
            paramModel->setParameterValue(it.key(), it.value());
        }
    }

    // 更新图表显示
    if (plotType == PlotDataType::DecayCurve && m_pPlot2) {
        m_pPlot2->replot();

        // 只有在拟合曲线超出当前视图范围时才调整坐标轴
        QCPRange currentXRange = m_pPlot2->xAxis->range();
        QCPRange currentYRange = m_pPlot2->yAxis->range();

        bool needRescale = false;
        for (double x : result.xData) {
            if (x < currentXRange.lower || x > currentXRange.upper) {
                needRescale = true;
                break;
            }
        }

        for (double y : result.yData) {
            if (y < currentYRange.lower || y > currentYRange.upper) {
                needRescale = true;
                break;
            }
        }

        if (needRescale) {
            m_pPlot2->rescaleAxes();
            m_pPlot2->replot();
        }
    } else if (plotType == PlotDataType::SpectralCurve && m_pPlot3) {
        m_pPlot3->replot();

        // 只有在拟合曲线超出当前视图范围时才调整坐标轴
        QCPRange currentXRange = m_pPlot3->xAxis->range();
        QCPRange currentYRange = m_pPlot3->yAxis->range();

        bool needRescale = false;
        for (double x : result.xData) {
            if (x < currentXRange.lower || x > currentXRange.upper) {
                needRescale = true;
                break;
            }
        }

        for (double y : result.yData) {
            if (y < currentYRange.lower || y > currentYRange.upper) {
                needRescale = true;
                break;
            }
        }

        if (needRescale) {
            m_pPlot3->rescaleAxes();
            m_pPlot3->replot();
        }
    }
}

// 处理checkrange复选框状态变化
void AnalysisTab::onCheckRangeStateChanged(int state) {
    if (!m_pPlot2) {
        return; // 如果plot2不存在，直接返回
    }

    if (state == Qt::Checked) {
        // 创建两条垂直线
        if (!m_rangeFromLine) {
            m_rangeFromLine = new QCPItemLine(m_pPlot2);
            m_rangeFromLine->setPen(QPen(Qt::red, 1, Qt::DashLine));
        }

        if (!m_rangeToLine) {
            m_rangeToLine = new QCPItemLine(m_pPlot2);
            m_rangeToLine->setPen(QPen(Qt::red, 1, Qt::DashLine));
        }

        // 更新线的位置
        updateRangeLines();

        // 设置线可见
        m_rangeFromLine->setVisible(true);
        m_rangeToLine->setVisible(true);
    } else {
        // 设置线不可见
        if (m_rangeFromLine) {
            m_rangeFromLine->setVisible(false);
        }

        if (m_rangeToLine) {
            m_rangeToLine->setVisible(false);
        }
    }

    // 重绘图表
    m_pPlot2->replot();
}

// 处理From值变化
void AnalysisTab::onRangeFromValueChanged(double value) {
    if (checkrange && checkrange->isChecked()) {
        updateRangeLines();
        m_pPlot2->replot();
    }
}

// 处理To值变化
void AnalysisTab::onRangeToValueChanged(double value) {
    if (checkrange && checkrange->isChecked()) {
        updateRangeLines();
        m_pPlot2->replot();
    }
}

// 更新垂直线位置
void AnalysisTab::updateRangeLines() {
    if (!m_pPlot2 || !m_rangeFromLine || !m_rangeToLine) {
        return; // 如果plot2或线不存在，直接返回
    }

    double fromValue = m_rangeFromSpinBox->value();
    double toValue = m_rangeToSpinBox->value();

    // 设置第一条线的位置（From值）
    m_rangeFromLine->start->setCoords(fromValue, m_pPlot2->yAxis->range().lower);
    m_rangeFromLine->end->setCoords(fromValue, m_pPlot2->yAxis->range().upper);

    // 设置第二条线的位置（To值）
    m_rangeToLine->start->setCoords(toValue, m_pPlot2->yAxis->range().lower);
    m_rangeToLine->end->setCoords(toValue, m_pPlot2->yAxis->range().upper);
}

// 计算残差并在plot4中显示
void AnalysisTab::calculateAndPlotResiduals(const QVector<double>& xData, const QVector<double>& originalY,
                                            const QVector<double>& fittedY, bool isTailFitting) {
    // 检查数据有效性
    if (xData.isEmpty() || originalY.isEmpty() || fittedY.isEmpty() || !plot4) {
        qWarning() << "Cannot plot residuals: Invalid data or plot4 is null";
        return;
    }

    // 清除plot4中的所有图形
    plot4->clearGraphs();

    // 创建新的图形用于显示残差
    QCPGraph* residualGraph = plot4->addGraph();
    residualGraph->setName("Residuals");

    // 设置图形样式
    QPen graphPen;
    graphPen.setColor(Qt::blue);
    graphPen.setWidth(1);
    residualGraph->setPen(graphPen);

    // 计算残差数据
    QVector<double> residualX;
    QVector<double> residualY;
    QCPRange xRange = m_pPlot2->xAxis->range();
    // 获取最大值
    double xMax = xRange.upper;
    double xMin = xRange.lower;

    // 如果启用了红线范围，则只显示范围内的残差（无论是尾部拟合还是卷积拟合）
    if (checkrange && checkrange->isChecked()) {
        // 获取红线位置对应的数据索引
        double fromIndex = m_rangeFromSpinBox->value() / xMax * xData.size();
        double toIndex = m_rangeToSpinBox->value() / xMax * xData.size();

        // 转换为整数索引
        int fromIdxInt = static_cast<int>(fromIndex);
        int toIdxInt = static_cast<int>(toIndex);

        // 确保索引在有效范围内
        fromIdxInt = qBound(0, fromIdxInt, xData.size() - 1);
        toIdxInt = qBound(0, toIdxInt, xData.size() - 1);

        qDebug() << "Residual calculation - Range indices:" << fromIdxInt << "to" << toIdxInt;

        // 遍历数据，只添加在拟合范围内的点
        if (isTailFitting) {
            // 尾部拟合的情况，需要考虑索引偏移
            for (int i = fromIdxInt; i <= toIdxInt; ++i) {
                residualX.append(xData[i]);
                residualY.append(originalY[i] - fittedY[i-fromIdxInt]);
            }
        } else {
            // 卷积拟合的情况，直接使用相同索引
            for (int i = fromIdxInt; i <= toIdxInt; ++i) {
                if (i < originalY.size() && i < fittedY.size()) {
                    residualX.append(xData[i]);
                    residualY.append(originalY[i] - fittedY[i]);
                }
            }
        }

        int xsize = residualX.size();
        int ysize = residualY.size();
        qDebug() << "Residual data points: X=" << xsize << ", Y=" << ysize;
    } else {
        // 未使用红线范围，显示全部范围的残差
        // 确保数据长度一致
        int minSize = qMin(qMin(xData.size(), originalY.size()), fittedY.size());

        for (int i = 0; i < minSize; ++i) {
            residualX.append(xData[i]);
            residualY.append(originalY[i] - fittedY[i]);
        }
    }

    // 检查是否有残差数据
    if (residualX.isEmpty() || residualY.isEmpty()) {
        qWarning() << "No residual data points to plot";
        return;
    }

    // 设置数据
    residualGraph->setData(residualX, residualY);

    // 设置坐标轴标签
    plot4->xAxis->setLabel("Time (ns)");
    plot4->yAxis->setLabel("Residual");

    // 设置图表标题
    if (!plot4->plotLayout()->elementAt(0)) {
        plot4->plotLayout()->insertRow(0);
        QCPTextElement* title = new QCPTextElement(plot4, "Decay Analysis");
        plot4->plotLayout()->addElement(0, 0, title);
    }

    // 自动调整坐标轴范围
    plot4->xAxis->setRange(xMin,xMax);
    plot4->yAxis->rescale();

    // 重绘图表
    plot4->replot();

}
