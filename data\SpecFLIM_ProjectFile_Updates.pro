# SpecFLIM统一文件管理系统 - 项目文件更新内容
# 将以下内容添加到现有的SpecFLIM.pro文件中

# ========================================
# 新增头文件 (HEADERS)
# ========================================

HEADERS += \
    # 统一文件管理系统核心组件
    data/UnifiedFileStructures.h \
    data/FileNameManager.h \
    data/SflpFileManager.h \
    \
    # ProjectFileManager和OpenProjectWidget增强接口
    # 注意：这些是示例文件，实际实现时应该直接修改现有的类文件
    data/ProjectFileManagerEnhancements.h \
    data/OpenProjectWidgetEnhancements.h

# ========================================
# 新增源文件 (SOURCES)
# ========================================

SOURCES += \
    # 统一文件管理系统核心组件实现
    data/UnifiedFileStructures.cpp \
    data/FileNameManager.cpp \
    data/SflpFileManager.cpp \
    \
    # ProjectFileManager和OpenProjectWidget增强实现
    # 注意：这些是示例文件，实际实现时应该直接修改现有的类文件
    data/ProjectFileManagerEnhancements.cpp \
    data/OpenProjectWidgetEnhancements.cpp

# ========================================
# 依赖库和模块 (如果需要)
# ========================================

# Qt模块 - 确保包含必要的模块
QT += core widgets gui

# 如果使用了网络功能或其他模块，添加相应的模块
# QT += network sql xml

# ========================================
# 编译器配置
# ========================================

# C++标准版本 (确保支持C++11或更高版本)
CONFIG += c++11

# 如果需要C++14或C++17特性，可以使用：
# CONFIG += c++14
# CONFIG += c++17

# 调试和发布配置
CONFIG(debug, debug|release) {
    DEFINES += DEBUG_BUILD
    TARGET = SpecFLIM_debug
} else {
    DEFINES += RELEASE_BUILD
    TARGET = SpecFLIM
}

# ========================================
# 预处理器定义
# ========================================

DEFINES += \
    # 统一文件管理系统版本
    UNIFIED_FILE_MANAGEMENT_VERSION=\\\"1.0.0\\\" \
    \
    # SFLP文件格式版本
    SFLP_FORMAT_VERSION=1 \
    \
    # 启用统一文件管理功能
    ENABLE_UNIFIED_FILE_MANAGEMENT

# ========================================
# 包含路径
# ========================================

INCLUDEPATH += \
    # 添加data目录到包含路径
    $$PWD/data \
    \
    # 如果有其他依赖的包含路径，在这里添加
    # $$PWD/external/include

# ========================================
# 库路径和链接库 (如果需要)
# ========================================

# 如果需要链接外部库，在这里添加
# LIBS += -L$$PWD/external/lib -lsomelib

# Windows平台特定配置
win32 {
    # Windows特定的库和配置
    # LIBS += -luser32 -lgdi32
}

# Linux平台特定配置
unix:!macx {
    # Linux特定的库和配置
}

# macOS平台特定配置
macx {
    # macOS特定的库和配置
}

# ========================================
# 资源文件 (如果有新的图标或资源)
# ========================================

RESOURCES += \
    # 如果添加了新的图标文件，在这里包含资源文件
    # resources/unified_file_management.qrc

# ========================================
# 安装配置 (可选)
# ========================================

# 目标安装路径
target.path = $$[QT_INSTALL_EXAMPLES]/specflim
INSTALLS += target

# ========================================
# 版本信息
# ========================================

VERSION = 1.0.0
QMAKE_TARGET_COMPANY = "SpecFLIM Development Team"
QMAKE_TARGET_PRODUCT = "SpecFLIM Unified File Management"
QMAKE_TARGET_DESCRIPTION = "SpecFLIM Application with Unified File Management System"
QMAKE_TARGET_COPYRIGHT = "Copyright (C) 2024"

# ========================================
# 编译优化 (发布版本)
# ========================================

CONFIG(release, debug|release) {
    # 发布版本优化
    QMAKE_CXXFLAGS_RELEASE += -O2
    DEFINES += QT_NO_DEBUG_OUTPUT
}

# ========================================
# 警告级别
# ========================================

# 启用更多警告
QMAKE_CXXFLAGS += -Wall -Wextra

# 将警告视为错误 (可选，用于确保代码质量)
# QMAKE_CXXFLAGS += -Werror

# ========================================
# 特殊配置说明
# ========================================

# 注意事项：
# 1. data/ProjectFileManagerEnhancements.h/cpp 是示例文件
#    实际实现时应该直接修改现有的 ProjectFileManager.h/cpp
#
# 2. data/OpenProjectWidgetEnhancements.h/cpp 是示例文件
#    实际实现时应该直接修改现有的 OpenProjectWidget.h/cpp
#
# 3. 如果项目中已经存在 AppConfig 类，确保 FileNameManager 
#    能够正确访问项目前缀配置
#
# 4. 确保所有新增的头文件都被正确包含在相关的源文件中
#
# 5. 如果使用了自定义的图标资源，需要创建相应的 .qrc 文件
#    并在 RESOURCES 中包含

# ========================================
# 构建后处理 (可选)
# ========================================

# 如果需要在构建后执行特定操作，可以添加自定义目标
# 例如：复制配置文件、创建安装包等

# 示例：复制配置文件到输出目录
# copyconfig.commands = $(COPY_FILE) $$PWD/config/default.conf $$OUT_PWD
# first.depends = $(first) copyconfig
# export(first.depends)
# export(copyconfig.commands)
# QMAKE_EXTRA_TARGETS += first copyconfig

# ========================================
# 结束
# ========================================
