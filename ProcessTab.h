#pragma once

#include <QTreeWidget>
#include <QComboBox>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <vector>
#include <set>
#include "AppConfig.h"
#include "BaseTab.h"
#include <QColorSpace>
class CrossControl : public QWidget {
    Q_OBJECT
public:
    CrossControl(QWidget *parent = nullptr) : QWidget(parent) {
        // 使用 QGridLayout 替代嵌套布局
        QGridLayout *layout = new QGridLayout(this);
        layout->setSpacing(0); // 设置网格布局的间距为 0
        layout->setContentsMargins(0, 0, 0, 0); // 设置边距为 0

        // 获取当前主题
        QString colorScheme = AppConfig::instance()->readConfig("ColorScheme", "Dark").toString();
        QString theme = (colorScheme == "Dark") ? "dark" : "light";

        // 创建并布局按钮
        createButton("Top", QIcon(QString(":/qss_icons/%1/rc/arrow_up.png").arg(theme)), Qt::AlignCenter, layout, 0, 1);
        createButton("Left", QIcon(QString(":/qss_icons/%1/rc/arrow_left.png").arg(theme)), Qt::AlignCenter, layout, 1, 0);
        createButton("Center", QIcon(), Qt::AlignCenter, layout, 1, 1); // 中心按钮无图标
        createButton("Right", QIcon(QString(":/qss_icons/%1/rc/arrow_right.png").arg(theme)), Qt::AlignCenter, layout, 1, 2);
        createButton("Bottom", QIcon(QString(":/qss_icons/%1/rc/arrow_down.png").arg(theme)), Qt::AlignCenter, layout, 2, 1);

        setLayout(layout);
    }
signals:
    void buttonClicked(const QString &name); // 添加信号
private:
    void createButton(const QString &name, const QIcon &icon, Qt::Alignment alignment, QGridLayout *layout, int row, int col) {
        QPushButton *button = new QPushButton();
        QFont buttonFont("iconfont", 10);
        buttonFont.setBold(true); // 设置按钮字体为粗体
        button->setFont(buttonFont);

        if (icon.isNull()) {
            button->setStyleSheet(
                "QPushButton {"
                "    background-color: transparent;"
                "    border: none;"
                "}"
                );
        } else {
            button->setIcon(icon);
            button->setStyleSheet(
                "QPushButton {"
                "    border-radius: 3px;"
                "    min-width: 20px;"
                "    min-height: 20px;"
                "    padding: 0; /* 去掉按钮内边距 */"
                "}"
                );
        }

        adjustButtonSize(button, button->font());

        button->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed); // 固定按钮大小
        layout->addWidget(button, row, col); // 添加到网格布局中
        layout->setAlignment(button, alignment);
        // 连接按钮点击信号到槽函数
        connect(button, &QPushButton::clicked, this, [this, name]() {
            emit buttonClicked(name);
        });
    }


    void adjustButtonSize(QPushButton* button, const QFont& font) {
        QFontMetrics fm(font);
        int textWidth = fm.horizontalAdvance(button->text());
        int textHeight = fm.height();

        // 获取图标的大小
        QSize iconSize = button->iconSize();
        int iconWidth = iconSize.width();
        int iconHeight = iconSize.height();

        // 根据字体和图标的大小计算按钮的最小尺寸
        int width = qMax(textWidth, iconWidth);
        int height = qMax(textHeight, iconHeight);
        int size = qMax(width, height);

        // 设置按钮的最小尺寸，留出一定的边距
        button->setMinimumSize(size + 5, size + 5);
    }
};

class SpectrumData {
private:
    int timeZeroPoint; // 添加 timeZeroPoint 成员变量声明
public:
    int getTimeZeroPoint() const {
        // 返回时间零点值的逻辑
        return timeZeroPoint;
    }

    void adjustTimeZeroPoint(int delta) {
        // 调整时间零点值的逻辑
        timeZeroPoint += delta;
    }
};

enum class CurveType {
    Decay,
    Spectral
};

class FluorescenceSpectraView {
public:
    void curveSelected();
};

class AlignmentButtons {
public:
    void alignmentChanged();
};

class ProcessTab : public BaseTab {
    Q_OBJECT

public:
    explicit ProcessTab(QWidget *parent = nullptr);
    ~ProcessTab();

public slots:
              // 使用基类的 onMousePress, onMouseMove, onMouseRelease 实现
private:
    QTreeWidget* processTreeWidget;
    QPushButton* autoAlignButton;
    QDoubleSpinBox* autoAlignValue;
    QPushButton* manualAlignButton;
    CrossControl* crossControl;
    QSpinBox* hRangeFrom;
    QSpinBox* hRangeTo;
    QSpinBox* vRangeFrom;
    QSpinBox* vRangeTo;
    QPushButton* applyCropButton;
    QSpinBox* decayCurveLambda;
    QSpinBox* decayCurveDelta;
    QPushButton* decayCurveAddButton;
    QSpinBox* spectralCurveTau;
    QSpinBox* spectralCurveDelta;
    QPushButton* spectralCurveAddButton;
    QPushButton* allButton;
    QPushButton* rangeButton;
    QPushButton* singleButton;
    QWidget* rangeWidget; // 添加成员变量
    QWidget* singleWidget; // 添加成员变量
    QSpinBox* fromFrameSpinBox;
    QSpinBox* toFrameSpinBox;
    QToolButton* plusButton;
    QToolButton* zoomInButton;
    QToolButton* zoomOutButton;
    QToolButton* linLogButton;
    bool manualAlignmentSelected;
    QPushButton *undoButton;
    QPushButton *helpButton;
    QPushButton *saveButton;
    QPushButton* resetButton;

    std::vector<std::vector<int>> Original_Spectra_Data;//存储每一次处理后的数据
    int currentAlignment;//记录对齐后的零点位置
    QCPColorMap* Fluorescence_Map;
    std::set<int> selectedColumns; // 用于存储选中的列
    std::set<int> selectedDataIndices; // 存储实际数据索引
    std::set<int> CropselectedColumns; // 用于存储选中的列
    // 新增成员变量，用于存储准备裁剪范围的红线
    QCPItemLine* horizontalLine1; // 水平红线1
    QCPItemLine* horizontalLine2; // 水平红线2
    QCPItemLine* verticalLine1;   // 竖直红线1
    QCPItemLine* verticalLine2;   // 竖直红线2



    int lastMapYmax; //记录光谱图上限
    int lastMapYmin; //记录光谱图下限

    // 存储坐标轴范围，用于保持坐标轴不变
    bool hasCustomAxisRange = false;
    QCPRange customXRange;
    QCPRange customYRange;

    // 标记数据是否已经被加载，用于避免重复加载
    bool m_dataAlreadyLoaded = false;

    // 图例相关的映射，用于跟踪图形和图例项的关系
    QMap<QCPGraph*, QString> m_graphNameMap; // 存储图形和其名称的映射




private slots:
    void onProjectOpened(const QString &filePath1);
    void onProjectCreated(const QString &filePath);
    void onProjectSaved(const QString &filePath);

    void onAutomaticAlignmentClicked(std::vector<std::vector<int>>& data);//自动对齐函数
    void onManualAlignmentClicked();//进入手动对齐状态
    void manualAlignment();//连接鼠标函数
    void quitmanualAlignment();
    void onButtonClicked(const QString &name);//点击按键进行手动对齐
    void onMousePress(QMouseEvent* event);
    void onMouseMove(QMouseEvent* event);
    void onMouseRelease(QMouseEvent* event);//通过鼠标选择不同的列



    void applyCrop();
    void HFromValueChanged(int hRangeFrom);
    void HToValueChanged(int hRangeTo);
    void VFromValueChanged(int vRangeFrom);
    void vToValueChanged(int vRangeTo);
    void updateLinePositions(int hRangeFrom, int hRangeTo, int vRangeFrom, int vRangeTo);

    void addDecayCurve();
    void addSpectralCurve();
    void onCurveVisibilityChanged(bool checked, QCPGraph* graph);//设置衰减曲线隐藏或显示
    void onLegendClick(QCPLegend* legend, QCPAbstractLegendItem* item, QMouseEvent* event);//处理图例点击事件

    // void onSplitFramesClicked();
    void undoCrop();
    void onApplyCropClicked();
    void onUndoCropClicked();
    void onAddDecayCurveClicked();
    void onAddSpectralCurveClicked();
    void onCurveSelected();
    void onUndoSplitClicked();
    void onAlignmentChanged();
    void onPlusClicked();
    void onResetClicked();
    void onSaveClicked();

    void onFileSelected(int row, const QString &filePath);
    void initGlobalVariables();//初始一些全局变量



private:
    int findUnifiedZeroPoint(const std::vector<std::vector<int>>& data);
    void alignData(std::vector<std::vector<int>>& data, int unifiedZero);
    void automaticAlignment(int alignmentValue);
    bool isManualAlignmentSelected();
    void cropFluorescenceSpectra(int hRangeFrom, int hRangeTo, int vRangeFrom, int vRangeTo);
    void updateFluorescenceSpectraView(const std::vector<std::vector<int> > &Data);
    void shiftDataUp(std::vector<std::vector<int>>& data,int shiftCount);//自动对齐后调节零点上移
    void shiftDataDown(std::vector<std::vector<int>>& data,int shiftCount);//自动对齐后调节零点下移
    void addCurve(int value, int delta, CurveType type);
    void enableFrameSlider();
    void disableFrameSlider();
    void connectSignalsAndSlots();
    QWidget* createBottomToolbar();
    QTreeWidget* createProcessTreeWidget();
    void highlightSelectedColumns();//选中的列高亮处理
    void setupPlots();
    void enableAlignMode(bool enable);

    // 使用基类的onCrosshairsToggled和showHideCrossHairs实现

    // 重写基类的曲线更新方法，添加同步功能
    void updateDecayCurvePlot(int x) override;
    void updateSpectralCurvePlot(int y) override;

    // Event filter for handling resize events
    bool eventFilter(QObject* watched, QEvent* event) override;
};


