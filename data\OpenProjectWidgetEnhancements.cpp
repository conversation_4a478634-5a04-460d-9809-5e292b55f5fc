#include "OpenProjectWidgetEnhancements.h"
#include "FileNameManager.h"
#include <QDir>
#include <QFileInfo>
#include <QMutexLocker>
#include <QDebug>
#include <QMenu>
#include <QAction>
#include <QHeaderView>
#include <QRegularExpression>

// 这个文件展示了需要添加到现有OpenProjectWidget类中的增强功能实现
// 实际实现时，应该将这些方法直接添加到OpenProjectWidget.cpp中

void OpenProjectWidgetEnhancements::refreshFileTreeWithVirtualNodes() {
    QMutexLocker locker(&m_treeMutex);
    
    beginTreeUpdate();
    
    try {
        // 保存当前展开状态
        saveTreeState();
        
        // 清空现有树结构
        clearTreeModel();
        
        // 获取项目根目录
        QString projectPath = projectFileManager->getFilePath();
        if (projectPath.isEmpty()) {
            endTreeUpdate();
            return;
        }
        
        // 构建根节点
        QStandardItem* rootItem = new QStandardItem(QFileInfo(projectPath).fileName());
        rootItem->setIcon(QIcon(":/icons/project.png"));
        rootItem->setData(FileType::Project, Qt::UserRole);
        treeModel->appendRow(rootItem);
        
        // 获取所有SFLP文件
        QStringList sflpFiles = getAllSflpFiles();
        
        // 为每个SFLP文件构建树结构
        for (const QString& fileName : sflpFiles) {
            if (isValidSflpFile(fileName)) {
                QStandardItem* fileItem = createFileItem(fileName);
                rootItem->appendRow(fileItem);
                
                // 添加虚拟子节点
                addVirtualChildren(fileItem, fileName);
            }
        }
        
        // 应用排序和过滤
        applySortingToTree();
        applyCurrentFilter();
        
        // 恢复展开状态
        restoreTreeState();
        
        // 展开根节点
        treeView->expand(treeModel->indexFromItem(rootItem));
        
        logTreeOperation("refreshFileTreeWithVirtualNodes", true);
        emit fileTreeRefreshed();
        
    } catch (const std::exception& e) {
        handleTreeError("refreshFileTreeWithVirtualNodes", e.what());
    }
    
    endTreeUpdate();
}

void OpenProjectWidgetEnhancements::setFileTypeFilter(TabType currentTab) {
    m_currentTabType = currentTab;
    m_filterActive = true;
    
    applyCurrentFilter();
    
    emit filterChanged(currentTab);
}

void OpenProjectWidgetEnhancements::addVirtualChildren(QStandardItem* parentItem, 
                                                       const QString& sflpFileName) {
    if (!parentItem || sflpFileName.isEmpty()) {
        return;
    }
    
    try {
        // 获取操作子项
        QStringList operationChildren = projectFileManager->getChildFiles(sflpFileName);
        
        for (const QString& operationName : operationChildren) {
            FileType fileType = projectFileManager->getFileType(operationName);
            
            if (fileType == FileType::Operation) {
                QStandardItem* operationItem = createOperationItem(operationName);
                parentItem->appendRow(operationItem);
                
                // 添加分析子项
                addAnalysisChildren(operationItem, operationName);
                
                // 注册虚拟节点
                m_virtualNodeMap[operationName] = operationItem;
            }
        }
        
    } catch (const std::exception& e) {
        handleTreeError("addVirtualChildren", e.what());
    }
}

void OpenProjectWidgetEnhancements::addOperationChildren(QStandardItem* parentItem, 
                                                         const QString& sflpFileName) {
    addVirtualChildren(parentItem, sflpFileName);
}

void OpenProjectWidgetEnhancements::addAnalysisChildren(QStandardItem* parentItem, 
                                                        const QString& operationName) {
    if (!parentItem || operationName.isEmpty()) {
        return;
    }
    
    try {
        QStringList analysisChildren = projectFileManager->getChildFiles(operationName);
        
        for (const QString& analysisName : analysisChildren) {
            FileType fileType = projectFileManager->getFileType(analysisName);
            
            if (fileType == FileType::DecayAnalysis || fileType == FileType::SpectralAnalysis) {
                AnalysisType analysisType = (fileType == FileType::DecayAnalysis) ? 
                                          AnalysisType::DecayAnalysis : AnalysisType::SpectralAnalysis;
                
                QStandardItem* analysisItem = createAnalysisItem(analysisName, analysisType);
                parentItem->appendRow(analysisItem);
                
                // 注册虚拟节点
                m_virtualNodeMap[analysisName] = analysisItem;
            }
        }
        
    } catch (const std::exception& e) {
        handleTreeError("addAnalysisChildren", e.what());
    }
}

QStandardItem* OpenProjectWidgetEnhancements::createFileItem(const QString& fileName) {
    FileNameManager* nameManager = FileNameManager::getInstance();
    QString displayName = nameManager->generateDisplayName(fileName, FileType::Project);
    
    QStandardItem* item = new QStandardItem(displayName);
    item->setData(fileName, Qt::UserRole + 1); // 存储完整文件名
    item->setData(FileType::Project, Qt::UserRole);
    item->setIcon(getFileTypeIcon(FileType::Project));
    item->setToolTip(fileName);
    
    return item;
}

QStandardItem* OpenProjectWidgetEnhancements::createOperationItem(const QString& operationName) {
    FileNameManager* nameManager = FileNameManager::getInstance();
    QString displayName = nameManager->extractOperationDisplayName(operationName);
    
    QStandardItem* item = new QStandardItem(displayName);
    item->setData(operationName, Qt::UserRole + 1);
    item->setData(FileType::Operation, Qt::UserRole);
    item->setIcon(getFileTypeIcon(FileType::Operation));
    item->setToolTip(QString("Operation: %1").arg(operationName));
    
    return item;
}

QStandardItem* OpenProjectWidgetEnhancements::createAnalysisItem(const QString& analysisName, 
                                                                 AnalysisType type) {
    FileNameManager* nameManager = FileNameManager::getInstance();
    QString displayName = nameManager->extractAnalysisDisplayName(analysisName);
    
    QStandardItem* item = new QStandardItem(displayName);
    item->setData(analysisName, Qt::UserRole + 1);
    
    FileType fileType = (type == AnalysisType::DecayAnalysis) ? 
                       FileType::DecayAnalysis : FileType::SpectralAnalysis;
    item->setData(fileType, Qt::UserRole);
    item->setIcon(getFileTypeIcon(fileType));
    
    QString typeStr = (type == AnalysisType::DecayAnalysis) ? "Decay Analysis" : "Spectral Analysis";
    item->setToolTip(QString("%1: %2").arg(typeStr, analysisName));
    
    return item;
}

QStandardItem* OpenProjectWidgetEnhancements::createSplitItem(const QString& splitName) {
    FileNameManager* nameManager = FileNameManager::getInstance();
    QString displayName = nameManager->generateDisplayName(splitName, FileType::Split);
    
    QStandardItem* item = new QStandardItem(displayName);
    item->setData(splitName, Qt::UserRole + 1);
    item->setData(FileType::Split, Qt::UserRole);
    item->setIcon(getFileTypeIcon(FileType::Split));
    item->setToolTip(QString("Split file: %1").arg(splitName));
    
    return item;
}

QIcon OpenProjectWidgetEnhancements::getFileTypeIcon(FileType fileType) const {
    switch (fileType) {
    case FileType::Workspace:
        return QIcon(":/icons/workspace.png");
    case FileType::Project:
        return QIcon(":/icons/project_file.png");
    case FileType::Operation:
        return QIcon(":/icons/operation.png");
    case FileType::DecayAnalysis:
        return QIcon(":/icons/decay_analysis.png");
    case FileType::SpectralAnalysis:
        return QIcon(":/icons/spectral_analysis.png");
    case FileType::Split:
        return QIcon(":/icons/split_file.png");
    }
    return QIcon(":/icons/default_file.png");
}

QString OpenProjectWidgetEnhancements::getDisplayName(const QString& fileName, FileType fileType) const {
    FileNameManager* nameManager = FileNameManager::getInstance();
    return nameManager->generateDisplayName(fileName, fileType);
}

void OpenProjectWidgetEnhancements::applyCurrentFilter() {
    if (!m_filterActive) {
        showAllItems();
        return;
    }
    
    switch (m_currentTabType) {
    case TabType::Acquire:
        hideItemsByType({FileType::Operation, FileType::DecayAnalysis, FileType::SpectralAnalysis});
        break;
    case TabType::Process:
        hideItemsByType({FileType::DecayAnalysis, FileType::SpectralAnalysis});
        break;
    case TabType::Analysis:
        // 显示所有类型
        showAllItems();
        break;
    }
}

void OpenProjectWidgetEnhancements::hideItemsByType(const QList<FileType>& typesToHide) {
    if (!proxyModel) {
        return;
    }
    
    // 实现过滤逻辑
    for (int i = 0; i < treeModel->rowCount(); ++i) {
        QStandardItem* rootItem = treeModel->item(i);
        if (rootItem) {
            hideItemsByTypeRecursive(rootItem, typesToHide);
        }
    }
}

void OpenProjectWidgetEnhancements::showAllItems() {
    if (!proxyModel) {
        return;
    }
    
    // 显示所有项目
    for (int i = 0; i < treeModel->rowCount(); ++i) {
        QStandardItem* rootItem = treeModel->item(i);
        if (rootItem) {
            showAllItemsRecursive(rootItem);
        }
    }
}

void OpenProjectWidgetEnhancements::applySortingToTree() {
    if (!treeModel) {
        return;
    }
    
    // 对根级别项目进行排序
    for (int i = 0; i < treeModel->rowCount(); ++i) {
        QStandardItem* rootItem = treeModel->item(i);
        if (rootItem) {
            sortItemChildren(rootItem);
        }
    }
}

void OpenProjectWidgetEnhancements::sortItemChildren(QStandardItem* parentItem) {
    if (!parentItem || parentItem->rowCount() == 0) {
        return;
    }
    
    // 收集子项目
    QList<QStandardItem*> children;
    for (int i = 0; i < parentItem->rowCount(); ++i) {
        children.append(parentItem->child(i));
    }
    
    // 自然排序
    std::sort(children.begin(), children.end(), 
              [this](QStandardItem* a, QStandardItem* b) {
                  return naturalCompare(a->text(), b->text());
              });
    
    // 重新排列子项目
    parentItem->removeRows(0, parentItem->rowCount());
    for (QStandardItem* child : children) {
        parentItem->appendRow(child);
        // 递归排序子项目的子项目
        sortItemChildren(child);
    }
}

bool OpenProjectWidgetEnhancements::naturalCompare(const QString& str1, const QString& str2) const {
    QRegularExpression re("(\\d+)");
    QRegularExpressionMatchIterator it1 = re.globalMatch(str1);
    QRegularExpressionMatchIterator it2 = re.globalMatch(str2);
    
    int pos1 = 0, pos2 = 0;
    
    while (it1.hasNext() && it2.hasNext()) {
        QRegularExpressionMatch match1 = it1.next();
        QRegularExpressionMatch match2 = it2.next();
        
        // 比较数字前的文本部分
        QString prefix1 = str1.mid(pos1, match1.capturedStart() - pos1);
        QString prefix2 = str2.mid(pos2, match2.capturedStart() - pos2);
        
        int cmp = prefix1.compare(prefix2, Qt::CaseInsensitive);
        if (cmp != 0) {
            return cmp < 0;
        }
        
        // 比较数字部分
        int num1 = match1.captured(1).toInt();
        int num2 = match2.captured(1).toInt();
        
        if (num1 != num2) {
            return num1 < num2;
        }
        
        pos1 = match1.capturedEnd();
        pos2 = match2.capturedEnd();
    }
    
    // 比较剩余部分
    return str1.mid(pos1).compare(str2.mid(pos2), Qt::CaseInsensitive) < 0;
}

void OpenProjectWidgetEnhancements::clearTreeModel() {
    if (treeModel) {
        treeModel->clear();
        treeModel->setHorizontalHeaderLabels(QStringList() << "Files");
    }
    
    m_virtualNodeMap.clear();
}

QStringList OpenProjectWidgetEnhancements::getAllSflpFiles() const {
    QString projectPath = projectFileManager->getFilePath();
    if (projectPath.isEmpty()) {
        return QStringList();
    }
    
    QDir directory(projectPath);
    QStringList nameFilters;
    nameFilters << "*.sflp";
    
    QStringList files = directory.entryList(nameFilters, QDir::Files, QDir::Name);
    
    // 转换为完整路径
    QStringList fullPaths;
    for (const QString& file : files) {
        fullPaths << directory.absoluteFilePath(file);
    }
    
    return fullPaths;
}

bool OpenProjectWidgetEnhancements::isValidSflpFile(const QString& fileName) const {
    return QFile::exists(fileName) && fileName.endsWith(".sflp", Qt::CaseInsensitive);
}

void OpenProjectWidgetEnhancements::saveTreeState() {
    m_nodeExpandState.clear();
    m_lastSelectedFile = getCurrentSelectedFile();
    
    // 保存展开状态
    saveExpandStateRecursive(treeModel->invisibleRootItem());
}

void OpenProjectWidgetEnhancements::restoreTreeState() {
    // 恢复展开状态
    restoreExpandStateRecursive(treeModel->invisibleRootItem());
    
    // 恢复选择
    if (!m_lastSelectedFile.isEmpty()) {
        selectFileInTree(m_lastSelectedFile);
    }
}

void OpenProjectWidgetEnhancements::beginTreeUpdate() {
    if (treeView) {
        treeView->setUpdatesEnabled(false);
    }
}

void OpenProjectWidgetEnhancements::endTreeUpdate() {
    if (treeView) {
        treeView->setUpdatesEnabled(true);
        treeView->update();
    }
}

void OpenProjectWidgetEnhancements::handleTreeError(const QString& operation, const QString& error) {
    qWarning() << "Tree operation error in" << operation << ":" << error;
    logTreeOperation(operation, false);
}

void OpenProjectWidgetEnhancements::logTreeOperation(const QString& operation, bool success) {
    if (success) {
        qDebug() << "Tree operation succeeded:" << operation;
    } else {
        qWarning() << "Tree operation failed:" << operation;
    }
}

// 辅助方法实现
void OpenProjectWidgetEnhancements::hideItemsByTypeRecursive(QStandardItem* item,
                                                             const QList<FileType>& typesToHide) {
    if (!item) return;

    FileType itemType = static_cast<FileType>(item->data(Qt::UserRole).toInt());

    // 检查是否需要隐藏此项目
    bool shouldHide = typesToHide.contains(itemType);

    if (shouldHide) {
        // 隐藏项目（通过代理模型实现）
        QModelIndex index = treeModel->indexFromItem(item);
        if (proxyModel && index.isValid()) {
            // 这里需要实现具体的隐藏逻辑
            // 可以通过设置项目的可见性或使用过滤器
        }
    }

    // 递归处理子项目
    for (int i = 0; i < item->rowCount(); ++i) {
        hideItemsByTypeRecursive(item->child(i), typesToHide);
    }
}

void OpenProjectWidgetEnhancements::showAllItemsRecursive(QStandardItem* item) {
    if (!item) return;

    // 显示项目
    QModelIndex index = treeModel->indexFromItem(item);
    if (proxyModel && index.isValid()) {
        // 实现显示逻辑
    }

    // 递归处理子项目
    for (int i = 0; i < item->rowCount(); ++i) {
        showAllItemsRecursive(item->child(i));
    }
}

void OpenProjectWidgetEnhancements::saveExpandStateRecursive(QStandardItem* item) {
    if (!item) return;

    QString fileName = item->data(Qt::UserRole + 1).toString();
    if (!fileName.isEmpty()) {
        QModelIndex index = treeModel->indexFromItem(item);
        if (index.isValid()) {
            m_nodeExpandState[fileName] = treeView->isExpanded(proxyModel->mapFromSource(index));
        }
    }

    // 递归处理子项目
    for (int i = 0; i < item->rowCount(); ++i) {
        saveExpandStateRecursive(item->child(i));
    }
}

void OpenProjectWidgetEnhancements::restoreExpandStateRecursive(QStandardItem* item) {
    if (!item) return;

    QString fileName = item->data(Qt::UserRole + 1).toString();
    if (!fileName.isEmpty() && m_nodeExpandState.contains(fileName)) {
        QModelIndex index = treeModel->indexFromItem(item);
        if (index.isValid()) {
            QModelIndex proxyIndex = proxyModel->mapFromSource(index);
            treeView->setExpanded(proxyIndex, m_nodeExpandState[fileName]);
        }
    }

    // 递归处理子项目
    for (int i = 0; i < item->rowCount(); ++i) {
        restoreExpandStateRecursive(item->child(i));
    }
}

QString OpenProjectWidgetEnhancements::getCurrentSelectedFile() const {
    QModelIndexList selectedIndexes = treeView->selectionModel()->selectedIndexes();
    if (selectedIndexes.isEmpty()) {
        return QString();
    }

    QModelIndex proxyIndex = selectedIndexes.first();
    QModelIndex sourceIndex = proxyModel->mapToSource(proxyIndex);
    QStandardItem* item = treeModel->itemFromIndex(sourceIndex);

    if (item) {
        return item->data(Qt::UserRole + 1).toString();
    }

    return QString();
}

bool OpenProjectWidgetEnhancements::selectFileInTree(const QString& fileName) {
    QStandardItem* item = findItemByFileName(fileName);
    if (item) {
        selectItem(item);
        return true;
    }
    return false;
}

QStandardItem* OpenProjectWidgetEnhancements::findItemByFileName(const QString& fileName) const {
    return findItemRecursive(treeModel->invisibleRootItem(), fileName);
}

QStandardItem* OpenProjectWidgetEnhancements::findItemRecursive(QStandardItem* parent,
                                                                const QString& fileName) const {
    if (!parent) return nullptr;

    // 检查当前项目
    QString itemFileName = parent->data(Qt::UserRole + 1).toString();
    if (itemFileName == fileName) {
        return parent;
    }

    // 递归搜索子项目
    for (int i = 0; i < parent->rowCount(); ++i) {
        QStandardItem* found = findItemRecursive(parent->child(i), fileName);
        if (found) {
            return found;
        }
    }

    return nullptr;
}

void OpenProjectWidgetEnhancements::selectItem(QStandardItem* item) {
    if (!item) return;

    QModelIndex sourceIndex = treeModel->indexFromItem(item);
    QModelIndex proxyIndex = proxyModel->mapFromSource(sourceIndex);

    if (proxyIndex.isValid()) {
        treeView->selectionModel()->select(proxyIndex, QItemSelectionModel::ClearAndSelect);
        ensureItemVisible(item);
    }
}

void OpenProjectWidgetEnhancements::ensureItemVisible(QStandardItem* item) {
    if (!item) return;

    QModelIndex sourceIndex = treeModel->indexFromItem(item);
    QModelIndex proxyIndex = proxyModel->mapFromSource(sourceIndex);

    if (proxyIndex.isValid()) {
        treeView->scrollTo(proxyIndex, QAbstractItemView::EnsureVisible);
    }
}

// NaturalSortProxyModel 实现
NaturalSortProxyModel::NaturalSortProxyModel(QObject* parent)
    : QSortFilterProxyModel(parent)
{
    setSortCaseSensitivity(Qt::CaseInsensitive);
    setDynamicSortFilter(true);
}

bool NaturalSortProxyModel::lessThan(const QModelIndex& left, const QModelIndex& right) const {
    QString leftString = sourceModel()->data(left, Qt::DisplayRole).toString();
    QString rightString = sourceModel()->data(right, Qt::DisplayRole).toString();

    return naturalCompare(leftString, rightString);
}

bool NaturalSortProxyModel::filterAcceptsRow(int sourceRow, const QModelIndex& sourceParent) const {
    // 基础过滤实现
    return QSortFilterProxyModel::filterAcceptsRow(sourceRow, sourceParent);
}

bool NaturalSortProxyModel::naturalCompare(const QString& str1, const QString& str2) const {
    QRegularExpression re("(\\d+)");
    QRegularExpressionMatchIterator it1 = re.globalMatch(str1);
    QRegularExpressionMatchIterator it2 = re.globalMatch(str2);

    int pos1 = 0, pos2 = 0;

    while (it1.hasNext() && it2.hasNext()) {
        QRegularExpressionMatch match1 = it1.next();
        QRegularExpressionMatch match2 = it2.next();

        // 比较数字前的文本部分
        QString prefix1 = str1.mid(pos1, match1.capturedStart() - pos1);
        QString prefix2 = str2.mid(pos2, match2.capturedStart() - pos2);

        int cmp = prefix1.compare(prefix2, Qt::CaseInsensitive);
        if (cmp != 0) {
            return cmp < 0;
        }

        // 比较数字部分
        int num1 = match1.captured(1).toInt();
        int num2 = match2.captured(1).toInt();

        if (num1 != num2) {
            return num1 < num2;
        }

        pos1 = match1.capturedEnd();
        pos2 = match2.capturedEnd();
    }

    // 比较剩余部分
    return str1.mid(pos1).compare(str2.mid(pos2), Qt::CaseInsensitive) < 0;
}
