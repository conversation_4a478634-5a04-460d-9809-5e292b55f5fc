#pragma once

#include <QWidget>
#include <QTreeWidget>
#include <QComboBox>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QSplitter>
#include <QSpinBox>
#include <QStackedWidget>
#include <QRubberBand>
#include <QEvent>
#include "QCustomPlot.h"
#include "CustomizePlot.h"

#include "OpenProjectWidget.h"
#include "TabSynchronizer.h"
#include "PlotToolbar.h"
#include "data/UnifiedFileStructures.h"

// 基类 BaseTab，包含 Plot1-3, toolbar1-3 以及相关功能
class BaseTab : public QWidget {
    Q_OBJECT

public:
    explicit BaseTab(QWidget *parent = nullptr, TabType tabType = TabType::Acquire);
    virtual ~BaseTab(); // 虚析构函数，确保正确清理资源

    // 公共接口函数
    void Zero_Data(std::vector<std::vector<int>>& data);

    // 获取图表对象的方法
    CustomizePlot* getPlot1() { return m_pPlot1; }
    CustomizePlot* getPlot2() { return m_pPlot2; }
    CustomizePlot* getPlot3() { return m_pPlot3; }

    // 数据处理函数 - 移动到public以便外部调用
    virtual void fillQCustomPlot(const std::vector<std::vector<int>>& Data, bool suppressReplot = false);

    // 公共成员变量 - 图表相关
    CustomizePlot *m_pPlot1 = nullptr;
    CustomizePlot *m_pPlot2 = nullptr;
    CustomizePlot *m_pPlot3 = nullptr;

    int numRows;
    int numCols;
    QCPItemStraightLine *hline;
    QCPItemStraightLine *vline;
    bool hasHVLine = false;

    // 图表标题元素
    QCPTextElement* m_title1 = nullptr;
    QCPTextElement* m_title2 = nullptr;
    QCPTextElement* m_title3 = nullptr;

    // 图表工具栏 - 移动到public以便TabSynchronizer可以访问
    PlotToolbar* m_pPlot1Toolbar = nullptr;
    PlotToolbar* m_pPlot2Toolbar = nullptr;
    PlotToolbar* m_pPlot3Toolbar = nullptr;

    // 数据存储
    std::vector<std::vector<double>> mutableData;
    std::vector<std::vector<int>> m_fillQCustomPlotData;

    // 计数率数据
    QVector<double> m_wavelengths; // 波长数据
    QVector<double> m_countRates;  // 计数率数据
    std::vector<int> wavelengthValues; // 存储波长值
    std::vector<double> timeValues;  // 存储时间值
    int scaleFactor;



protected:
    // 项目树视图
    OpenProjectWidget* openProjectWidget = nullptr;

    // 文件路径显示
    QLineEdit* pLineSaveDataPath = nullptr;

    // 橡皮筋选择区域
    QRubberBand *rubberBand = nullptr;
    QPoint rubberOrigin;

    // 图表工具栏 - 已移动到public

    // 放大缩小相关变量
    bool m_bZoomInMode = false;  // 是否处于放大模式
    bool m_bZoomOutMode = false; // 是否处于缩小模式
    QCPRange m_originalXRange;   // 原始 X 轴范围
    QCPRange m_originalYRange;   // 原始 Y 轴范围

    // 存储原始交互状态，用于十字线模式切换时恢复
    QCP::Interactions m_originalInteractions;

    // 当前标签页类型
    TabType m_tabType;

    // 图表和工具栏设置函数
    virtual void setupPlots();
    virtual void setupPlotToolbars();
    virtual void clearPlots();

    // 数据处理函数
    virtual void createColorMap(int xA, int yA, bool suppressReplot = false);

    // 十字线相关函数
    virtual void createCrosshairs();
    virtual void removeCrosshairs();
    virtual void enableMouseTracking();
    virtual void disableMouseTracking();
    virtual void showHideCrossHairs();
    virtual void updateCrosshairPosition(int x, int y);
    virtual void updateCrosshairStyle();
    virtual void updateDecayCurvePlot(int x);
    virtual void updateSpectralCurvePlot(int y);

    // 重置工具栏按钮状态
    virtual void resetToolbarButtons();

public slots:
    // 文件选择槽函数
    virtual void onFileSelected(int row, const QString &filePath);
    // onResetBtnClicked 是 AcquireTab 特有的功能

    // 图表操作槽函数
    virtual void reset();
    virtual void onMousePress(QMouseEvent *event);
    virtual void onMouseMove(QMouseEvent *event);
    virtual void onMouseRelease(QMouseEvent *event);
    virtual void onRightButtonPress(QMouseEvent *event);

    void choose_zoom();
    void mousePress(QMouseEvent *e);
    void mouseMove2(QMouseEvent *e);
    void mouseRelease(QMouseEvent *e);
    // 波长转换相关函数和变量
    void calculateWavelengths(double centerWavelength);
    void calculateTime(int resolution);

protected slots:
    // 工具栏通用槽函数
    virtual void onCrosshairsToggled(bool enabled);
    virtual void onPanClicked(bool enabled);

    // 主题变化槽函数
    virtual void onThemeChanged(const QString& theme);

    // Plot1 特定工具栏槽函数
    virtual void onPlot1ResetClicked();
    virtual void onPlot1ZoomInClicked();
    virtual void onPlot1ZoomOutClicked();

    // Plot2 特定工具栏槽函数
    virtual void onPlot2ResetClicked();

    // Plot3 特定工具栏槽函数
    virtual void onPlot3ResetClicked();

    // 兼容性槽函数
    virtual void onResetClicked();
    virtual void onZoomInClicked();
    virtual void onZoomOutClicked();
    virtual void onLinLogToggled(bool isLogScale, const QString& axisType = "yAxis");
    virtual void onNormalizeClicked();

    // 放大缩小相关槽函数
    virtual void onZoomMousePress(QMouseEvent *event);
    virtual void saveOriginalRange();
    virtual void restoreOriginalRange();

    // 事件过滤器，处理调整大小事件
    virtual bool eventFilter(QObject* watched, QEvent* event) override;

};


